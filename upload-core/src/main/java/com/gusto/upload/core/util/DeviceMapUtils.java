package com.gusto.upload.core.util;

import com.gusto.upload.model.entity.Device;
import com.gusto.upload.model.entity.dto.DeviceDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
        componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface DeviceMapUtils {

    DeviceDTO toDeviceDTO(Device device);

}
