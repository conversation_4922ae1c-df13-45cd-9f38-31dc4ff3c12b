package com.gusto.upload.core.handler;

import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;

/**
 * ClientIp注解实现类
 *
 * <AUTHOR>
 * @since 2021-06-25
 */
@Component
public class ClientIpHandlerMethodArgResolver implements HandlerMethodArgumentResolver {
    /**
     * 判断是否支持使用@ClientIp注解的参数
     */
    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        // 如果该参数注解有@ClientIp且参数类型为String
        return parameter.getParameterAnnotation(ClientIp.class) != null
                && parameter.getParameterType() == String.class;
    }

    /**
     * 注入参数值
     */
    @Override
    public Object resolveArgument(
            MethodParameter parameter,
            ModelAndViewContainer mavContainer,
            NativeWebRequest webRequest,
            WebDataBinderFactory binderFactory
    ) throws Exception {
        var request = (HttpServletRequest) webRequest.getNativeRequest();
        return request.getAttribute("clientIp");
    }
}
