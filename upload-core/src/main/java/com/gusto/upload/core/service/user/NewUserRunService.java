package com.gusto.upload.core.service.user;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gusto.upload.core.dao.user.NewUserRunDao;
import com.gusto.upload.model.entity.user.NewUserRun;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 新用户跑步记录 服务类
 *
 * <AUTHOR>
 * @since 2022-07-20
 */
@Service
public class NewUserRunService extends ServiceImpl<NewUserRunDao, NewUserRun> {

    /**
     * 根据用户 ID 和标识符获取列表
     */
    public List<NewUserRun> getListByUserIdAndRunId(Long userId, Long activityId) {
        var wrapper = Wrappers.lambdaQuery(NewUserRun.class)
                .eq(NewUserRun::getUserId, userId)
                .eq(NewUserRun::getActivityId, activityId);
        return list(wrapper);
    }

    /**
     * 根据用户 ID 和标识符获取列表
     */
    public List<NewUserRun> getListByUserIdAndTimeForDuplicate(Long userId, Long startTime, Long endTime) {
        var wrapper = Wrappers.lambdaQuery(NewUserRun.class)
                .eq(NewUserRun::getUserId, userId)
                .eq(NewUserRun::getState, 1)
                .not(it -> it.lt(NewUserRun::getEndTime, startTime).or().gt(NewUserRun::getStartTime, endTime));
        return list(wrapper);
    }

}
