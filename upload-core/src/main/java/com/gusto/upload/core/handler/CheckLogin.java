package com.gusto.upload.core.handler;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 在方法上添加此注解，则此方法会被拦截，在方法执行之前，检查用户的登录状态，如果不能通过检查，则抛出异常。
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CheckLogin {
}