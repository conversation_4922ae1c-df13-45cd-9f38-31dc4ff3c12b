package com.gusto.upload.core.handler;

import com.gusto.framework.core.exception.LogLevelProvider;
import com.gusto.upload.core.common.MatchConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-11-28
 */
@Component
public class UploadLogLevelProvider implements LogLevelProvider {

    @Autowired
    private MatchConfigService configService;

    @Override
    public Map<Integer, String> getLogLevelMap() {
        return configService.getLogLevelMap();
    }

}
