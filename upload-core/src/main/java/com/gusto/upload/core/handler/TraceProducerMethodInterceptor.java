package com.gusto.upload.core.handler;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerInterceptor;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.MDC;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-09-19
 */
@Slf4j
public class TraceProducerMethodInterceptor implements ProducerInterceptor<String, String> {

    @Override
    public ProducerRecord<String, String> onSend(ProducerRecord<String, String> producerRecord) {
        var traceId = MDC.get("trace-id");
        if (StrUtil.isEmpty(traceId)) {
            MDC.put("trace-id", IdUtil.getSnowflakeNextIdStr());
        } else {
            MDC.put("trace-id", traceId);
        }
        producerRecord.headers().add("trace-id", traceId.getBytes(StandardCharsets.UTF_8));
        return producerRecord;
    }

    @Override
    public void onAcknowledgement(RecordMetadata recordMetadata, Exception e) {

    }

    @Override
    public void close() {
        MDC.clear();
    }

    @Override
    public void configure(Map<String, ?> map) {

    }

}
