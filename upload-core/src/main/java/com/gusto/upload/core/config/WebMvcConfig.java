package com.gusto.upload.core.config;

import com.dtflys.forest.converter.json.ForestFastjsonConverter;
import com.dtflys.forest.converter.json.ForestJsonConverter;
import com.gusto.upload.core.handler.AuthorizationInterceptor;
import com.gusto.upload.core.handler.ClientIpHandlerMethodArgResolver;
import com.gusto.upload.core.handler.ClientIpInterceptor;
import com.gusto.upload.core.handler.RepeatableFilter;
import com.gusto.upload.core.handler.TraceOncePerRequestFilter;
import com.gusto.upload.core.handler.WebLogInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * WebMvcConfig
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@Slf4j
@SpringBootConfiguration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private AuthorizationInterceptor authorizationInterceptor;

    @Autowired
    private ClientIpInterceptor clientIpInterceptor;

    @Autowired
    private ClientIpHandlerMethodArgResolver clientIpHandlerMethodArgResolver;

    @Autowired
    private WebLogInterceptor webLogInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authorizationInterceptor).addPathPatterns("/**");
        registry.addInterceptor(clientIpInterceptor).addPathPatterns("/**");
        registry.addInterceptor(webLogInterceptor).addPathPatterns("/**");
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(clientIpHandlerMethodArgResolver);
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        log.debug("[addCorsMappings] handler cors success");
        // 添加映射路径
        registry.addMapping("/**")
                // 是否发送Cookie
                .allowCredentials(true)
                // 设置放行哪些原始域
                .allowedOriginPatterns("*")
                // 放行哪些请求方式
                .allowedMethods("GET", "POST", "OPTIONS")
                // 放行哪些原始请求头部信息
                .allowedHeaders("client-token", "Content-Type")
                // 暴露哪些原始请求头部信息
                .exposedHeaders("*");
    }

    @Bean
    public FilterRegistrationBean<TraceOncePerRequestFilter> traceOncePerRequestFilter() {
        FilterRegistrationBean<TraceOncePerRequestFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new TraceOncePerRequestFilter());
        return registrationBean;
    }

    /**
     * Forest全局Fastjson转换器
     */
    @Bean
    public ForestJsonConverter forestFastjsonConverter() {
        return new ForestFastjsonConverter();
    }

    @Bean
    public FilterRegistrationBean<RepeatableFilter> repeatableFilter() {
        FilterRegistrationBean<RepeatableFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new RepeatableFilter());
        return registrationBean;
    }

}
