package com.gusto.upload.core.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

/**
 * <AUTHOR>
 * @since 2024-04-15
 */
@Slf4j
public class VivoUtils {

    /**
     * 构造签名
     */
    public static String getSign(Map<String, Object> headers, String paramJson, String secret) {
        try {
            String sortedStr = getSortedParamStr(headers);
            String paraStr = sortedStr + paramJson;
            log.debug("[getSign] paraStr = {}", paraStr);
            return sha256Hmac(paraStr, secret);
        } catch (InvalidKeyException | NoSuchAlgorithmException e) {
            log.warn("[getSign] error = ", e);
        }

        return StringUtils.EMPTY;
    }

    /**
     * sha256_HMAC加密
     *
     * @param content 内容
     * @param secret  秘钥
     * @return 签名
     */
    private static String sha256Hmac(String content, String secret) throws InvalidKeyException, NoSuchAlgorithmException {
        Mac hmacSha256 = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(), "HmacSHA256");
        hmacSha256.init(secretKey);
        byte[] bytes = hmacSha256.doFinal(content.getBytes());
        return org.apache.commons.codec.binary.Base64.encodeBase64URLSafeString(bytes);
    }

    /**
     * 构造自然排序请求参数
     * 对参数做字典序升序排列，参与排序的参数包括header里面除了signature外的所有非空参数，如果参数值为空则不参与签名（比如三方生态云vivo Health Kit云时无需携带accessToken，则将accessToken排除）
     * 将上一步排序好的参数格式化成“参数名称”（统一转换为小写）=“参数值”的形式，然后将格式化后的各个参数用“&”拼接在一起，生成请求字符串A
     */
    private static String getSortedParamStr(Map<String, Object> params) {
        Set<String> sortedParams = new TreeSet<>(params.keySet());
        StringBuilder sb = new StringBuilder();
        // 排除signature和空值参数
        for (String key : sortedParams) {
            if ("signature".equalsIgnoreCase(key)) {
                continue;
            }
            var value = params.get(key);
            if (value != null && StringUtils.isNotEmpty(value.toString())) {
                sb.append(key.toLowerCase()).append("=").append(value).append("&");
            }
        }
        return sb.substring(0, sb.length() - 1);
    }

}
