package com.gusto.upload.core.handler;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.gusto.upload.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * Web的日志拦截器
 */
@Slf4j
@Component
public class WebLogInterceptor implements HandlerInterceptor {

    private final TransmittableThreadLocal<StopWatch> invokeTimeTL = new TransmittableThreadLocal<>();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String url = request.getMethod() + " " + request.getRequestURI();
        var clientIp = request.getAttribute("clientIp").toString();
        var clientPort = request.getRemotePort();

        var clientInfo = StrUtil.format(
                "client-token={} client-source={} client-version={} client-brand={} client-model={}",
                request.getHeader("client-token"),
                request.getHeader("client-source"),
                request.getHeader("client-version"),
                request.getHeader("client-brand"),
                request.getHeader("client-model")
        );

        // 打印请求参数和header
        if (isJsonRequest(request)) {
            String jsonParam = "";
            if (request instanceof RepeatedlyRequestWrapper) {
                jsonParam = getBodyString(request);
            }
            log.debug("[{}-{}] [{}] 开始请求 => URL[{}], 参数类型[json], 参数:[{}]", clientIp, clientPort, clientInfo, url, jsonParam);
        } else {
            Map<String, String[]> parameterMap = request.getParameterMap();
            if (MapUtil.isNotEmpty(parameterMap)) {
                String parameters = JsonUtils.toJsonString(parameterMap);
                log.debug("[{}-{}] [{}] 开始请求 => URL[{}], 参数类型[param], 参数:[{}]", clientIp, clientPort, clientInfo, url, parameters);
            } else {
                log.debug("[{}-{}] [{}] 开始请求 => URL[{}], 无参数", clientIp, clientPort, clientInfo, url);
            }
        }

        StopWatch stopWatch = new StopWatch();
        invokeTimeTL.set(stopWatch);
        stopWatch.start();

        return true;
    }

    private String getBodyString(final ServletRequest request) {
        StringBuilder sb = new StringBuilder();
        InputStream inputStream = null;
        BufferedReader reader = null;
        try {
            inputStream = request.getInputStream();
            reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = "";
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return sb.toString().replace(" ", "");
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        var clientIp = request.getAttribute("clientIp").toString();
        var clientPort = request.getRemotePort();

        var clientInfo = StrUtil.format(
                "client-token={} client-source={} client-version={} client-brand={} client-model={}",
                request.getHeader("client-token"),
                request.getHeader("client-source"),
                request.getHeader("client-version"),
                request.getHeader("client-brand"),
                request.getHeader("client-model")
        );

        StopWatch stopWatch = invokeTimeTL.get();
        stopWatch.stop();
        log.debug("[{}-{}] [{}] 结束请求 => URL[{}],耗时:[{}]毫秒", clientIp, clientPort, clientInfo, request.getMethod() + " " + request.getRequestURI(), stopWatch.getTime());
        invokeTimeTL.remove();
    }

    /**
     * 判断本次请求的数据类型是否为json
     *
     * @param request request
     * @return boolean
     */
    private boolean isJsonRequest(HttpServletRequest request) {
        String contentType = request.getContentType();
        if (contentType != null) {
            return StringUtils.startsWithIgnoreCase(contentType, MediaType.APPLICATION_JSON_VALUE);
        }
        return false;
    }

}
