package com.gusto.upload.core.swagger;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Field;
import java.util.Arrays;

/**
 * 在线接口文档配置
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
@Slf4j
@Configuration
public class SwaggerConfig implements InitializingBean {

    @Autowired
    private ApplicationContext applicationContext;

    @Bean
    public OpenAPI customOpenAPI() {
//        var info = new Info()
//                .title("第一赛道 match-server 接口文档");
//        var securityRequirement = new SecurityRequirement()
//                .addList(HttpHeaders.AUTHORIZATION);
//        var securityScheme = new SecurityScheme()
//                .name(HttpHeaders.AUTHORIZATION)
//                .type(SecurityScheme.Type.HTTP)
//                .scheme("bearer");
//        var components = new Components()
//                .addSecuritySchemes(HttpHeaders.AUTHORIZATION, securityScheme);
//        return new OpenAPI()
//                .info(info)
//                .addSecurityItem(securityRequirement)
//                .components(components);
        var info = new Info()
                .title("第一赛道 upload-server 接口文档");
        return new OpenAPI()
                .info(info);

    }

    /**
     * 动态得创建Docket bean
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        // ApiConstantVersion 里面定义的每个变量会成为一个docket
        var declaredFields = UploadVersion.class.getDeclaredFields();
        // 动态注入bean
        AutowireCapableBeanFactory autowireCapableBeanFactory = applicationContext.getAutowireCapableBeanFactory();
        if (autowireCapableBeanFactory instanceof DefaultListableBeanFactory) {
            DefaultListableBeanFactory capableBeanFactory = (DefaultListableBeanFactory) autowireCapableBeanFactory;
            for (Field declaredField : declaredFields) {
                // 要注意"工厂名和方法名"，意思是用这个bean的指定方法创建docket
                var beanDefinition = BeanDefinitionBuilder
                        .genericBeanDefinition()
                        // 此处的swaggerConfig首字母要小写否则报找不到bean的方法
                        .setFactoryMethodOnBean("buildGroupedOpenApi", "swaggerConfig")
                        .addConstructorArgValue(declaredField.get(UploadVersion.class)).getBeanDefinition();
                capableBeanFactory.registerBeanDefinition(declaredField.getName(), beanDefinition);
            }
        }
    }

    public GroupedOpenApi buildGroupedOpenApi(String groupName) {
        return GroupedOpenApi.builder()
                .group(groupName)
                .pathsToMatch("/**")
                .addOpenApiMethodFilter(method -> {
                    // 每个方法会进入这里进行判断并归类到不同分组，**请不要调换下面两段代码的顺序，在方法上的注解有优先级**
                    // 该方法上标注了版本
                    if (method.isAnnotationPresent(ApiVersion.class)) {
                        var annotationOnMethod = method.getAnnotation(ApiVersion.class);
                        assert annotationOnMethod != null;
                        if (Arrays.asList(annotationOnMethod.value()).contains(groupName)) return true;
                    }

                    return false;
                })
                .build();
    }

}
