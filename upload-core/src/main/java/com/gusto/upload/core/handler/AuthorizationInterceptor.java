package com.gusto.upload.core.handler;

import com.gusto.framework.core.exception.FrameworkErrorInfo;
import com.gusto.framework.core.exception.FrameworkException;
import com.gusto.upload.core.service.user.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 用户登录状态拦截器
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@Component
public class AuthorizationInterceptor extends HandlerInterceptorAdapter {

    public static final String USER_ID = "userId";

    @Autowired
    private UserService userService;

    @Override
    public boolean preHandle(
            HttpServletRequest request,
            HttpServletResponse response,
            Object handler
    ) throws FrameworkException {
        // 设置跨域--开始
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        if (httpRequest.getMethod().equals(RequestMethod.OPTIONS.name())) {
            setHeader(httpRequest, httpResponse);
            return true;
        }
        // 设置跨域--结束

        CheckLogin annotation;
        if (handler instanceof HandlerMethod) {
            annotation = ((HandlerMethod) handler).getMethodAnnotation(CheckLogin.class);
        } else {
            return true;
        }

        if (annotation == null) {
            return true;
        }

        // 从header中获取client-token(即用户表中的apptoken字段)
        String token = request.getHeader("client-token");
        if (token == null || token.isEmpty()) {
            throw new FrameworkException(FrameworkErrorInfo.USER_TOKEN_NOT_FOUND_ERROR);
        }
        // 查询用户
        var user = userService.getOneByToken(token);
        // 设置userId到request里，后续根据userId，获取用户信息
        request.setAttribute(USER_ID, user.getUserId());

        return true;
    }

    /**
     * 为response设置header，实现跨域
     */
    // @SuppressFBWarnings("HRS_REQUEST_PARAMETER_TO_HTTP_HEADER")
    private void setHeader(HttpServletRequest request, HttpServletResponse response) {
        // 跨域的header设置
        response.setHeader("Access-control-Allow-Origin", request.getHeader("Origin"));
        response.setHeader("Access-Control-Allow-Methods", request.getMethod());
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Headers", request.getHeader("Access-Control-Request-Headers"));
        // 防止乱码，适用于传输json数据
        response.setHeader("Content-Type", "application/json;charset=UTF-8");
    }
}
