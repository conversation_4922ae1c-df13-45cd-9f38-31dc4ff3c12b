package com.gusto.upload.core.handler;

import com.gusto.upload.core.util.IpUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * IP拦截器
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@Component
public class ClientIpInterceptor extends HandlerInterceptorAdapter {
    @Override
    public boolean preHandle(
            HttpServletRequest request,
            HttpServletResponse response,
            Object handler
    ) throws Exception {
        var clientIp = IpUtils.getIpAddr(request);
        request.setAttribute("clientIp", clientIp);
        return true;
    }
}
