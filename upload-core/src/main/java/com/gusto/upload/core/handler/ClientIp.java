package com.gusto.upload.core.handler;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 将此注解加在 handler 方法的参数上，可通过此参数获得客户端 IP
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ClientIp {
}