package com.gusto.upload.core.util;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
public class OppoSportModeUtils {

    /**
     * walk also include outdoor fitness walk 走路（户外健走）
     */
    public static final int WALK = 1;

    /**
     * common out door run 跑步（户外跑步）
     */
    public static final int RUN = 2;

    /**
     * ride by bike 骑行（户外骑行）
     */
    public static final int RIDE = 3;

    /**
     * drive or take bus 乘车
     */
    public static final int BY_BUS = 4;

    /**
     * climb mountains or steps 爬楼
     */
    public static final int CLIMB = 5;

    /**
     * stand or sitting for hours 久坐
     */
    public static final int STANDING = 6;

    /**
     * pool swim 游泳池游泳
     */
    public static final int SWIM = 7;

    /**
     * play golf 高尔夫
     */
    public static final int GOLF = 8;

    /**
     * gym, health build 健身
     */
    public static final int FITNESS = 9;

    /**
     * common indoor run 普通室内跑
     */
    public static final int INDOOR_RUN = 10;

    /**
     * yoga 瑜伽
     */
    public static final int YOGA = 12;

    /**
     * outdoor physical run 户外跑步
     */
    public static final int OUTDOOR_PHYSICAL_RUN = 13;

    /**
     * indoor physical run 室内体能跑
     */
    public static final int INDOOR_PHYSICAL_RUN = 14;

    /**
     * outdoor 5km relaxing run 室外5KM轻松跑
     */
    public static final int OUTDOOR_5KM_RELAX_RUN = 15;

    /**
     * indoor 5km relaxing run 室内5KM轻松跑
     */
    public static final int INDOOR_5KM_RELAX_RUN = 16;

    /**
     * outdoor fat reduce run 室外减脂跑
     */
    public static final int OUTDOOR_FAT_REDUCE_RUN = 17;

    /**
     * indoor fat reduce run 室内减脂跑
     */
    public static final int INDOOR_FAT_REDUCE_RUN = 18;

    /**
     * indoor fitness walk 室内健走
     */
    public static final int INDOOR_FITNESS_WALK = 19;

    /**
     * treadmill run 跑步机
     */
    public static final int TREADMILL_RUN = 21;

    /**
     * marathon 马拉松
     */
    public static final int MARATHON = 22;

    /**
     * badminton 羽毛球
     */
    public static final int BADMINTON = 31;

    /**
     * elliptical machine 椭圆机
     */
    public static final int ELLIPTICAL_MACHINE = 32;

    /**
     * rowing machine 划船机
     */
    public static final int ROWING_MACHINE = 33;

    /**
     * indoor motion bike 室内骑车，动感单车
     */
    public static final int INDOOR_MOTION_BIKE = 34;

    /**
     * free training 自由训练
     */
    public static final int FREE_TRAINING = 35;

    /**
     * mountain climbing 登山
     */
    public static final int MOUNTAIN_CLIMBING = 36;

    /**
     * cross country 越野
     */
    public static final int CROSS_COUNTRY = 37;

    /**
     * cross country 游戏
     */
    public static final int GAME = 38;

    /**
     * 自动识别跑步
     */
    public static final int AUTO_MODE_RUN = 40;
    /**
     * 自动识别健走
     */
    public static final int AUTO_MODE_WALK = 41;

    /**
     * all run state data for searching
     */
    public static final int RUN_ALL = 100;
    /**
     * all walk state data for searching
     */
    public static final int WALK_ALL = 101;
    /**
     * all ride state data for searching
     */
    public static final int RIDE_ALL = 102;
    /**
     * all fitness state data for searching
     */
    public static final int FITNESS_ALL = 103;
    /**
     * all swim state data for searching
     */
    public static final int SWIM_ALL = 104;
    /**
     * all ball game state data for searching 球类运动
     */
    public static final int BALL_ALL = 105;
    /**
     * all outdoor sports state data for searching 户外运动
     */
    public static final int OUTDOOR_ALL = 106;
    /**
     * all yoga state data for searching 瑜伽
     */
    public static final int YOGA_ALL = 107;
    /**
     * all dance state data for searching 舞蹈
     */
    public static final int DANCE_ALL = 108;
    /**
     * all snow sports state data for searching 冰雪运动
     */
    public static final int SNOW_ALL = 109;
    /**
     * all water sports state data for searching 水上运动
     */
    public static final int WATER_ALL = 110;
    /**
     * all leisure sports state data for searching 休闲运动
     */
    public static final int LEISURE_ALL = 111;

    /**
     * all game sports state data for searching 游戏记录
     */
    public static final int GAME_ALL = 112;

    /**
     * all custom sports state data for searching 自定义运动
     */
    public static final int CUSTOM_ALL = 113;

    /**
     * 体能测试类型
     */
    public static final int STAMINAL_TEST = 127;

    //--------------------------------------------------------------健身---------------------------------------------------------------------------
    /**
     * 爆发力训练
     */
    public static final int EXPLOSIVE_TRAINING = 201;
    /**
     * 背部训练
     */
    public static final int BACK_TRAINING = 202;
    /**
     * 单杠
     */
    public static final int HORIZONTAL_BAR = 203;
    /**
     * 登山机
     */
    public static final int CLIMBER = 204;
    /**
     * 腹部训练
     */
    public static final int ABDOMINAL_TRAINING = 205;
    /**
     * 核心训练
     */
    public static final int CORE_TRAINING = 206;
    /**
     * 击剑
     */
    public static final int FENCING = 207;
    /**
     * 肩部训练
     */
    public static final int SHOULDER_TRAINING = 208;
    /**
     * 健身操
     */
    public static final int AEROBICS = 209;
    /**
     * 颈部训练
     */
    public static final int NECK_TRAINING = 210;
    /**
     * 力量训练
     */
    public static final int STRENGTH_TRAINING = 211;
    /**
     * 脸部训练
     */
    public static final int FACE_TRAINING = 212;
    /**
     * 灵敏性训练
     */
    public static final int AGILITY_TRAINING = 213;
    /**
     * 平衡性训练
     */
    public static final int BALANCE_TRAINING = 214;
    /**
     * 拳击
     */
    public static final int BOXING = 215;
    /**
     * 柔道
     */
    public static final int JUDO = 216;
    /**
     * 柔韧性训练
     */
    public static final int FLEXIBILITY_TRAINING = 217;
    /**
     * 上肢训练
     */
    public static final int UPPER_LIMB_TRAINING = 218;
    /**
     * 射击
     */
    public static final int SHOOTING = 219;
    /**
     * 射箭
     */
    public static final int ARCHERY = 220;
    /**
     * 双杠
     */
    public static final int PARALLEL_BARS = 221;
    /**
     * 踏步机
     */
    public static final int STEPPER = 222;
    /**
     * 跆拳道
     */
    public static final int TAEKWONDO = 223;
    /**
     * 太极
     */
    public static final int TAI_CHI = 224;
    /**
     * 体操
     */
    public static final int GYMNASTICS = 225;
    /**
     * 臀部训练
     */
    public static final int HIP_TRAINING = 226;
    /**
     * 武术
     */
    public static final int MARTIAL_ARTS = 227;
    /**
     * 下肢训练
     */
    public static final int LOWER_LIMB_TRAINING = 228;
    /**
     * 胸部训练
     */
    public static final int CHEST_TRAINING = 229;
    /**
     * 腰部训练
     */
    public static final int WAIST_TRAINING = 230;

    //--------------------------------------------------------------瑜伽---------------------------------------------------------------------------
    /**
     * 阿奴萨拉
     */
    public static final int ANUSARA = 301;
    /**
     * 阿斯汤加瑜伽
     */
    public static final int ASHTANGA_YOGA = 302;
    /**
     * 艾扬格瑜伽
     */
    public static final int IYENGAR_YOGA = 303;
    /**
     * 飞行瑜伽
     */
    public static final int FLY_YOGA = 304;
    /**
     * 哈他瑜伽
     */
    public static final int HATHA_YOGA = 305;
    /**
     * 空中瑜伽
     */
    public static final int AERIAL_YOGA = 306;
    /**
     * 理疗瑜伽
     */
    public static final int PHYSIOTHERAPY_YOGA = 307;
    /**
     * 流瑜伽
     */
    public static final int FLOW_YOGA = 308;
    /**
     * 冥想
     */
    public static final int MEDITATION = 309;
    /**
     * 内观流瑜伽
     */
    public static final int VIPASSANA_FLOW_YOGA = 310;
    /**
     * 普拉提
     */
    public static final int PILATES = 311;
    /**
     * 阴瑜伽
     */
    public static final int YIN_YOGA = 312;
    /**
     * 孕瑜伽
     */
    public static final int PREGNANCY_YOGA = 313;

    //--------------------------------------------------------------舞蹈---------------------------------------------------------------------------
    /**
     * 芭蕾舞
     */
    public static final int BALLET = 401;
    /**
     * 迪斯科
     */
    public static final int DISCO = 402;
    /**
     * 肚皮舞
     */
    public static final int BELLY_DANCE = 403;
    /**
     * 广场舞
     */
    public static final int SQUARE_DANCE = 404;
    /**
     * 华尔兹
     */
    public static final int WALTZ = 405;
    /**
     * 街舞
     */
    public static final int STREET_DANCE = 406;
    /**
     * 爵士舞
     */
    public static final int JAZZ = 407;
    /**
     * 拉丁舞
     */
    public static final int LATIN_DANCE = 408;
    /**
     * 探戈
     */
    public static final int TANGO = 409;
    /**
     * 踢踏舞
     */
    public static final int TAP_DANCE = 410;

    //--------------------------------------------------------------球类运动---------------------------------------------------------------------------
    /**
     * 板球
     */
    public static final int CRICKET = 601;
    /**
     * 棒球
     */
    public static final int BASEBALL = 602;
    /**
     * 橄榄球
     */
    public static final int AMERICAN_FOOTBALL = 603;
    /**
     * 篮球
     */
    public static final int BASKETBALL = 604;
    /**
     * 垒球
     */
    public static final int SOFTBALL = 605;
    /**
     * 门球
     */
    public static final int CROQUET = 606;
    /**
     * 排球
     */
    public static final int VOLLEYBALL = 607;
    /**
     * 乒乓球
     */
    public static final int PINGPONG = 608;
    /**
     * 曲棍球
     */
    public static final int HOCKEY = 609;
    /**
     * 网球
     */
    public static final int TENNIS = 610;
    /**
     * 足球
     */
    public static final int FOOTBALL = 611;

    //--------------------------------------------------------------冰雪运动---------------------------------------------------------------------------
    /**
     * 冰壶
     */
    public static final int CURLING = 701;
    /**
     * 冰球
     */
    public static final int PUCK = 702;
    /**
     * 冬季两项
     */
    public static final int BIATHLON = 703;
    /**
     * 滑冰
     */
    public static final int SKATE = 704;
    /**
     * 滑雪
     */
    public static final int SKI = 705;
    /**
     * 雪车
     */
    public static final int SNOW_CAR = 706;
    /**
     * 雪橇
     */
    public static final int SLED = 707;

    //--------------------------------------------------------------休闲运动---------------------------------------------------------------------------
    /**
     * 拔河
     */
    public static final int TUG_OF_WAR = 901;
    /**
     * 放风筝
     */
    public static final int FLY_A_KITE = 902;
    /**
     * 飞镖
     */
    public static final int DARTS = 903;
    /**
     * 飞盘
     */
    public static final int FRISBEE = 904;
    /**
     * 遛狗
     */
    public static final int WALK_THE_DOG = 905;
    /**
     * 骑马
     */
    public static final int HORSE_RIDING = 906;
    /**
     * 踢毽子
     */
    public static final int KICK_THE_SHUTTLECOCK = 907;
    /**
     * 跳绳
     */
    public static final int ROPE_SKIPPING = 908;

    /**
     * 操场跑
     */
    public static final int PLAYGROUND_RUNNING = 909;

    //--------------------------------------------------------------定制专属运动---------------------------------------------------------------------------
    /**
     * 自定义户外运动
     */
    public static final int CUSTOM_OUTDOOR_SPORTS = 1001;
    /**
     * 自定义室内运动
     */
    public static final int CUSTOM_INDOOR_SPORTS = 1002;
    /**
     * 自定义水上运动
     */
    public static final int CUSTOM_WATER_SPORTS = 1003;

}
