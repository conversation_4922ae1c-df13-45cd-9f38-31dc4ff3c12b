package com.gusto.upload.core.util;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.Security;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
public class OppoUtils {

    static {
//        Security.addProvider(Singleton.get(BouncyCastleProvider.class));
//        Security.addProvider(new BouncyCastleProvider());
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    // 加解密算法，每次申请接入OPPO健康服务时，并且需要获取血氧等需要加密的数据时，健康服务都会分配一个秘钥，即下文的passWord。
    // 得到的字符串采用aesDecryptByGCM方法解密接口即可，解密出来的字符串为JSONString。
    public static String aesEncryptByGCM(String passWord, String content) throws Exception {
        byte[] iv = generateSecureBytes(16);
        byte[] input = content.getBytes(StandardCharsets.UTF_8);
        byte[] digest = passWord.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec skc = new SecretKeySpec(digest, "AES");
        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        cipher.init(1, skc, new GCMParameterSpec(128, iv));
        byte[] encrypted = cipher.doFinal(input);
        byte[] data = new byte[16 + encrypted.length];
        System.arraycopy(iv, 0, data, 0, 16);
        System.arraycopy(encrypted, 0, data, 16, encrypted.length);
        return java.util.Base64.getEncoder().encodeToString(data);
    }

    public static String aesDecryptByGCM(String passWord, String content) throws Exception {
        byte[] input = java.util.Base64.getDecoder().decode(content);
        byte[] keys = passWord.getBytes(StandardCharsets.UTF_8);
        byte[] iv = new byte[16];
        System.arraycopy(input, 0, iv, 0, 16);
        SecretKeySpec secretKeySpec = new SecretKeySpec(keys, "AES");
        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        cipher.init(2, secretKeySpec, new GCMParameterSpec(128, iv));
        byte[] encrypted = new byte[input.length - 16];
        System.arraycopy(input, 16, encrypted, 0, encrypted.length);
        byte[] clearByte = cipher.doFinal(encrypted);
        return new String(clearByte, StandardCharsets.UTF_8);
    }

    private static byte[] generateSecureBytes(int num) {
        byte[] bytes = new byte[num];
        new SecureRandom().nextBytes(bytes);
        return bytes;
    }

//    public static void main(String[] args) throws Exception {
//        String key = "1234567890123456";
//        String plaintext = "Hello World";
//
//        String encrypted = aesEncryptByGCM(key, plaintext);
//        String decrypted = aesDecryptByGCM(key, encrypted);
//
//        System.out.println("Original: " + plaintext);
//        System.out.println("Encrypted: " + encrypted);
//        System.out.println("Decrypted: " + decrypted);
//
//        String a = aesEncryptByGCM("Npz24jVWtKSCEThhMTlaF83Zj7dnbgqM", "456549120");
//        System.out.println(a);
//        System.out.println(aesDecryptByGCM("Npz24jVWtKSCEThhMTlaF83Zj7dnbgqM", a));
//    }

}
