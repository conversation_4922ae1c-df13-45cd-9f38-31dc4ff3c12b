package com.gusto.upload.core.service.app;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.Cached;
import com.alicp.jetcache.anno.CreateCache;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gusto.upload.core.dao.app.OnlineConfigDao;
import com.gusto.upload.model.entity.app.OnlineConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 在线参数 服务类
 *
 * <AUTHOR>
 * @since 2021/9/2
 */
@Slf4j
@Service
public class OnlineConfigService extends ServiceImpl<OnlineConfigDao, OnlineConfig> {

    public static final String ONLINE_CONFIG_BY_NAME = "OnlineConfigService.getOneByName.";
    public static final String ONLINE_CONFIG_ALL_MAP = "OnlineConfigService.getAllMap.";

    @SuppressWarnings("deprecation")
    @CreateCache(name = ONLINE_CONFIG_ALL_MAP, expire = 3600 * 12)
    private Cache<String, Map<String, String>> onlineConfigCache;

    @SuppressWarnings("deprecation")
    @CreateCache(name = ONLINE_CONFIG_BY_NAME, expire = 3600 * 12)
    private Cache<String, String> onlineConfigNameCache;

    /**
     * 获取在线参数
     */
    public OnlineConfig getOneByNameOrNull(String name) {
        var query = new QueryWrapper<OnlineConfig>().lambda();
        query.eq(OnlineConfig::getName, name);
        return getOne(query);
    }

    /**
     * 获取在线参数的字符串值
     */
    @Cached(name = ONLINE_CONFIG_BY_NAME, key = "#name", expire = 3600 * 12)
    public String getString(String name, String defaultValue) {
        var config = getOneByNameOrNull(name);
        if (config != null && StringUtils.isNotEmpty(config.getValue())) return config.getValue();
        return defaultValue;
    }

    /**
     * 获取在线参数的布尔值
     */
    @Cached(name = ONLINE_CONFIG_BY_NAME, key = "#name", expire = 3600 * 12)
    public Boolean getBoolean(String name, Boolean defaultValue) {
        var config = getOneByNameOrNull(name);
        if (config != null && StringUtils.isNotEmpty(config.getValue())) {
            return config.getValue().equalsIgnoreCase("true");
        } else {
            return defaultValue;
        }
    }

    /**
     * 获取在线参数的整形值
     */
    @Cached(name = ONLINE_CONFIG_BY_NAME, key = "#name", expire = 3600 * 12)
    public Integer getInteger(String name, Integer defaultValue) {
        var config = getOneByNameOrNull(name);
        if (config != null) return NumberUtils.toInt(config.getValue(), defaultValue);
        return defaultValue;
    }

    /**
     * 获取在线参数的长整形值列表
     */
    @Cached(name = ONLINE_CONFIG_BY_NAME, key = "#name", expire = 3600 * 12)
    public List<Long> getLongList(String name, List<Long> defaultValue) {
        var config = getOneByNameOrNull(name);
        if (config != null && StringUtils.isNotEmpty(config.getValue())) {
            return Arrays.stream(config.getValue().split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }
        return defaultValue;
    }

    /**
     * 获取在线参数的长整形值
     */
    @Cached(name = ONLINE_CONFIG_BY_NAME, key = "#name", expire = 3600 * 12)
    public Long getLong(String name, Long defaultValue) {
        var config = getOneByNameOrNull(name);
        if (config != null) return NumberUtils.toLong(config.getValue(), defaultValue);
        return defaultValue;
    }

    /**
     * 获取在线参数的单精度浮点值
     */
    @Cached(name = ONLINE_CONFIG_BY_NAME, key = "#name", expire = 3600 * 12)
    public Float getFloat(String name, Float defaultValue) {
        var config = getOneByNameOrNull(name);
        if (config != null) return NumberUtils.toFloat(config.getValue(), defaultValue);
        return defaultValue;
    }

    /**
     * 获取在线参数的双精度浮点值
     */
    @Cached(name = ONLINE_CONFIG_BY_NAME, key = "#name", expire = 3600 * 12)
    public Double getDouble(String name, Double defaultValue) {
        var config = getOneByNameOrNull(name);
        if (config != null) return NumberUtils.toDouble(config.getValue(), defaultValue);
        return defaultValue;
    }

    /**
     * 获取在线参数的长整形值列表
     */
    @Cached(name = ONLINE_CONFIG_BY_NAME, key = "#name", expire = 3600 * 12)
    public Map<Integer, String> getIntStringMap(String name, Map<Integer, String> defaultValue) {
        var config = getOneByNameOrNull(name);
        if (config != null && StringUtils.isNotEmpty(config.getValue())) {
            return Arrays.stream(config.getValue().split(","))
                    .map(it -> it.split(":"))
                    .collect(Collectors.toMap(it -> NumberUtils.toInt(it[0]), it -> it[1]));
        }
        return defaultValue;
    }

}
