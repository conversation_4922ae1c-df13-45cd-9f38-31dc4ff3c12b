package com.gusto.upload.core.util;

import com.gusto.upload.model.entity.user.NewUserRun;
import com.gusto.upload.model.entity.user.NewUserRunDetail;
import com.gusto.upload.model.entity.user.req.AppUploadFullNewUserRunReq;
import com.gusto.upload.model.entity.user.req.AppUploadNewUserRunReq;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
        componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface NewUserRunMapUtils {

    NewUserRun toNewUserRun(AppUploadNewUserRunReq model);

    NewUserRun toNewUserRun(AppUploadFullNewUserRunReq model);

    NewUserRunDetail toNewUserRunDetail(NewUserRun model);

}
