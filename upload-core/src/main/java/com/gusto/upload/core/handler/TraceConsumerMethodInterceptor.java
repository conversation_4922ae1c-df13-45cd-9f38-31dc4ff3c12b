package com.gusto.upload.core.handler;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerInterceptor;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.slf4j.MDC;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-09-19
 */
@Slf4j
public class TraceConsumerMethodInterceptor implements ConsumerInterceptor<String, String> {

    @Override
    public ConsumerRecords<String, String> onConsume(ConsumerRecords<String, String> consumerRecords) {
        consumerRecords.forEach(x -> {
            var headers = x.headers().toArray();
            Arrays.asList(headers)
                    .forEach(s -> {
                        if (s.key().equals("trace-id")) {
                            MDC.put(s.key(), new String(s.value()));
                        }
                    });
        });
        return consumerRecords;
    }

    @Override
    public void onCommit(Map<TopicPartition, OffsetAndMetadata> map) {

    }

    @Override
    public void close() {
        MDC.clear();
    }

    @Override
    public void configure(Map<String, ?> map) {

    }

}