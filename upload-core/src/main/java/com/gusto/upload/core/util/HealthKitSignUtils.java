package com.gusto.upload.core.util;

import lombok.Data;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.junit.jupiter.api.Test;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.TreeMap;

@Deprecated
public class HealthKitSignUtils {

    //1、获取用户token"的接口
    @Test
    public void getToken() throws NoSuchAlgorithmException, InvalidKeyException, IOException {
        String secret = "73DFA874C45498436A5044B692639F7A";
        String appId = "5LRZH9KS";
        String code = "6a395795ef1f489787c7667dc0705b25548073b2128fc0f37a7cdd95109fa36f";

        String apiVersion = "1.0";
        //去掉多余的-
        String reqId = "1784912717876994048";
        String reqTime = "1714391328673";

        //封装Header
        //header在put的时候是驼峰的，但是获取的key用来签名的时候是需要entry.getKey().toLowerCase()一下
        TreeMap<String, String> header = new TreeMap<>();
        header.put("apiVersion", apiVersion);
        header.put("appId", appId);
        header.put("reqId", reqId);
        header.put("reqTime", reqTime);
        header.put("accessToken", "0e3202caaae44b82a4e26a38f9e6e609");


        StringBuilder contentBuilder = new StringBuilder();

        for (Map.Entry<String, String> entry : header.entrySet()) {
            //这里包含了签名header中key的转小写
            contentBuilder.append(entry.getKey().toLowerCase()).append("=").append(entry.getValue()).append("&");
        }

//        var req = new VivoGetActivityListReq();
//        req.setStartTime(1713857252949L);
//        req.setEndTime(1713848055000L);

        // fastjson，将Object转换为json字符串;
//        String bodyJson = JSON.toJSONString(req);
        String bodyJson = "{}";
        String content = contentBuilder.substring(0, contentBuilder.length() - 1) + bodyJson;
        String signature = sha256Hmac(content, secret);
        header.put("signature", signature);

        System.out.println("得到的content是：" + content);
        System.out.println("得到的签名是：" + signature);

        getCloudToken(bodyJson, header);
    }


    /**
     * 省略异常与关闭资源
     *
     * @param bodyJson
     * @param map
     * @throws IOException
     */
    public void getCloudToken(String bodyJson, TreeMap<String, String> map) throws IOException {

        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost("https://vivo-health.vivo.com.cn/cloud/token");
        StringEntity entity = new StringEntity(bodyJson, "UTF-8");
        // post请求是将参数放在请求体里面传过去的;这里将entity放入post请求体中
        httpPost.setEntity(entity);
        for (Map.Entry<String, String> entry : map.entrySet()) {
            httpPost.setHeader(entry.getKey(), entry.getValue());
        }

        httpPost.setHeader("Content-Type", "application/json;charset=utf8");

        // 由客户端执行(发送)Post请求
        CloseableHttpResponse response = httpClient.execute(httpPost);

        // 从响应模型中获取响应实体
        HttpEntity responseEntity = response.getEntity();

        System.out.println("响应状态为:" + response.getStatusLine());
        if (responseEntity != null) {
            System.out.println("响应内容长度为:" + responseEntity.getContentLength());
            System.out.println("响应内容为:" + EntityUtils.toString(responseEntity));
        }

    }

    /**
     * sha256_HMAC加密
     *
     * @param content 内容
     * @param secret  秘钥
     * @return 签名
     */
    public static String sha256Hmac(String content, String secret) throws InvalidKeyException, NoSuchAlgorithmException {
        Mac hmacSha256 = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(), "HmacSHA256");
        hmacSha256.init(secretKey);
        byte[] bytes = hmacSha256.doFinal(content.getBytes());
        return org.apache.commons.codec.binary.Base64.encodeBase64URLSafeString(bytes);
    }


    @Data
    class Body {
        private String code;
    }


}