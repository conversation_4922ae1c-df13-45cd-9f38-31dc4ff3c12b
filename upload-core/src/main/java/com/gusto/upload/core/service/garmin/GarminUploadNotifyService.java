package com.gusto.upload.core.service.garmin;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gusto.upload.core.dao.garmin.GarminUploadNotifyDao;
import com.gusto.upload.model.entity.garmin.GarminUploadNotify;
import com.gusto.upload.model.entity.garmin.GarminUserAuthInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 佳明上传通知 服务类
 *
 * <AUTHOR>
 * @since 2023-02-23
 */
@Service
public class GarminUploadNotifyService extends ServiceImpl<GarminUploadNotifyDao, GarminUploadNotify> {

    /**
     * 根据状态获取列表
     */
    public List<GarminUploadNotify> getListByState(Integer state) {
        var wrapper = Wrappers.lambdaQuery(GarminUploadNotify.class)
                .eq(GarminUploadNotify::getState, state);
        return list(wrapper);
    }

    /**
     * 根据AccessToken、开始时间、来源获取
     */
    public GarminUploadNotify getOneOrNull(String accessToken, Long startTime, Integer source) {
        var wrapper = Wrappers.lambdaQuery(GarminUploadNotify.class)
                .eq(GarminUploadNotify::getAccessToken, accessToken)
                .eq(GarminUploadNotify::getStartTime, startTime)
                .eq(GarminUploadNotify::getSource, source);
        return getOne(wrapper);
    }

    /**
     * 创建
     */
    public void create(GarminUserAuthInfo authInfo, String accessToken, Long startTime, Long endTime) {
        var uploadNotify = new GarminUploadNotify();
        uploadNotify.setGarminUserId(authInfo.getGarminUserId());
        uploadNotify.setAccessToken(accessToken);
        uploadNotify.setStartTime(startTime);
        uploadNotify.setEndTime(endTime);
        uploadNotify.setState(1);
        uploadNotify.setSource(authInfo.getSource());
        save(uploadNotify);
    }

}
