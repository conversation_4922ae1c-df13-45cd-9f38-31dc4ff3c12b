package com.gusto.upload.core.service.user;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gusto.upload.core.dao.user.NewUserRunUploadNotifyDao;
import com.gusto.upload.model.entity.user.NewUserRunUploadNotify;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 新用户跑步记录上传通知 服务类
 *
 * <AUTHOR>
 * @since 2023-02-23
 */
@Service
public class NewUserRunUploadNotifyService extends ServiceImpl<NewUserRunUploadNotifyDao, NewUserRunUploadNotify> {

    /**
     * 根据状态获取列表
     */
    public List<NewUserRunUploadNotify> getListByState(Integer state) {
        var wrapper = Wrappers.lambdaQuery(NewUserRunUploadNotify.class)
                .eq(NewUserRunUploadNotify::getState, state);
        return list(wrapper);
    }

}
