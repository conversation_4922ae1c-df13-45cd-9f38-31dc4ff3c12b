spring:
  profiles:
    active: dev

server:
  compression:
    enabled: true
    mime-types:
      - application/json
      - text/plain
    min-response-size: 256

mybatis-plus:
  mapper-locations: classpath*:mybatis-mappings/*.xml
  type-aliases-package: com.gusto.upload.model.entity
  typeEnumsPackage: com.gusto.upload.model.enums

springdoc:
  swagger-ui:
    tags-sorter: alpha
  #    operations-sorter: alpha
#  default-flat-param-object: true
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.gusto.upload
knife4j:
  enable: true
  basic:
    enable: true
    username: gusto
    password: gusto123$
  setting:
    enableVersion: true

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: gutoken
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: -1
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: simple-uuid
  # 是否输出操作日志
  is-log: false
  # 是否尝试从header里读取token
  is-read-head: true
  is-read-body: false
  is-read-cookie: false