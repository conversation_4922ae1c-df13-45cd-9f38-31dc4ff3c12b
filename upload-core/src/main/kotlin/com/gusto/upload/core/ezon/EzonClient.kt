package com.gusto.upload.core.ezon

import com.dtflys.forest.annotation.BaseRequest
import com.dtflys.forest.annotation.Body
import com.dtflys.forest.annotation.Get
import com.dtflys.forest.annotation.LogEnabled
import com.dtflys.forest.annotation.Post
import com.dtflys.forest.annotation.Query
import com.dtflys.forest.annotation.Retry
import com.dtflys.forest.http.ForestResponse
import com.gusto.upload.model.entity.ezon.EzonActivity
import com.gusto.upload.model.entity.ezon.EzonActivityDetail
import com.gusto.upload.model.entity.ezon.rsp.EzonCommonRsp
import com.gusto.upload.model.entity.ezon.rsp.EzonCommonWithDataListRsp
import com.gusto.upload.model.entity.ezon.rsp.EzonCommonWithDataRsp
import com.gusto.upload.model.entity.ezon.rsp.EzonGetAccessTokenRsp
import com.gusto.upload.model.entity.ezon.rsp.EzonUserInfo

/**
 * 宜准-开放平台
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@BaseRequest(
    baseURL = "#{upload.ezon.apiHost}"
)
@Retry(maxRetryCount = "5", maxRetryInterval = "100")
interface EzonClient {

    /**
     * 使用授权码Code获取AT
     */
    @Post("api/accessToken")
    fun getAccessToken(
        @Body("client_id") clientId: String,
        @Body("client_secret") clientSecret: String,
        @Body("grant_type") grantType: String = "authorization_code",
        @Body("code") code: String,
        @Body("redirect_uri") redirectUri: String
    ): ForestResponse<EzonCommonWithDataRsp<EzonGetAccessTokenRsp>>

    /**
     * 取消授权
     */
    @Post("api/unbinding")
    fun cancelAuth(
        @Query("access_token") accessToken: String
    ): ForestResponse<EzonCommonRsp>

    /**
     * 获取指定⽇期区间的活动列表，日期最大范围30天
     */
    @Get("api/locusList")
    fun getActivityListByDate(
        @Query("access_token") accessToken: String,
        @Query("start_time") startDate: String,
        @Query("end_time") endDate: String,
        @Query("movement_type_id") movementTypeId: String = "ALL"
    ): ForestResponse<EzonCommonWithDataListRsp<EzonActivity>>

    /**
     * 根据运动记录ID获取详情
     */
    @LogEnabled(logResponseContent = false)
    @Get("api/locusDetail")
    fun getActivityDetail(
        @Query("access_token") accessToken: String,
        @Query("id") activityId: Long
    ): ForestResponse<EzonCommonWithDataRsp<EzonActivityDetail>>

    /**
     * 获取用户信息
     */
    @Get("api/userInfo")
    fun getUserInfo(
        @Query("access_token") accessToken: String
    ): ForestResponse<EzonCommonWithDataRsp<EzonUserInfo>>

}
