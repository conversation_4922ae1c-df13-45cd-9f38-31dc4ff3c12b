package com.gusto.upload.core.oppo

import cn.hutool.core.util.StrUtil
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwError
import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.oppo.OppoProperties
import com.gusto.upload.model.entity.oppo.OppoUserAuthInfo
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.concurrent.Executor
import javax.annotation.Resource

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Service
class OppoCommonService {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authInfoService: OppoUserAuthInfoService

    @Autowired
    lateinit var config: OppoProperties

    @Autowired
    lateinit var oppoClient: OppoClient

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    /**
     * 检查用户AT是否过期，如果过期则刷新
     */
    fun checkOrRefreshAccessToken(authInfo: OppoUserAuthInfo) {
        val result = refreshAccessToken(authInfo)
        if (!result) {
            authInfoService.removeById(authInfo)
            throwError(
                ServiceException(
                    UploadErrorCode.OPPO_REFRESH_ACCESS_TOKEN_ERROR.code,
                    "获取授权失败，请返回并重新绑定"
                )
            )
        }
    }

    /**
     * 刷新AT
     */
    private fun refreshAccessToken(authInfo: OppoUserAuthInfo): Boolean {
        val rsp = oppoClient.refreshAccessToken(
            clientId = config.clientId,
            clientSecret = config.clientSecret,
            refreshToken = authInfo.refreshToken,
            headers = buildHeaders(null)
        )
        if (rsp.isError) {
            log.warn("[refreshAccessToken] refresh AT error, error = ${rsp.content}")
            return false
        }
        if (rsp.result.errorCode != 0) {
            log.warn("[refreshAccessToken] refresh AT error, rsp = ${rsp.result.formatToJson()}")
            return false
        }
        authInfo.accessToken = rsp.result.body.accessToken
        authInfo.accessTokenUpdateTime = Instant.now()

        if (StrUtil.isEmpty(authInfo.nickname)) {
            val userInfoRsp = oppoClient.getUserInfo(buildHeaders(authInfo.accessToken))
            if (userInfoRsp.isSuccess && userInfoRsp.result.errorCode == 0) {
                authInfo.nickname = userInfoRsp.result.body?.userName ?: ""
            }
        }

        authInfoService.updateById(authInfo)
        return true
    }

    protected fun buildHeaders(accessToken: String?): Map<String, Any> {
        val headers = HashMap<String, Any>()
        headers["Content-type"] = "application/json"
        headers["Accept"] = "application/json"
        if (StrUtil.isNotEmpty(accessToken)) {
            headers["access-token"] = accessToken!!
        }
        return headers
    }

}
