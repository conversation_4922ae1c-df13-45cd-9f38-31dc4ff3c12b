package com.gusto.upload.core.huawei

import com.alicp.jetcache.Cache
import com.alicp.jetcache.anno.CreateCache
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwIfNull
import com.gusto.upload.core.common.CacheKeyPrefix.HUAWEI_USER_ID_BY_OPEN_ID
import com.gusto.upload.core.common.DeleteInterface
import com.gusto.upload.core.dao.HuaweiUserAuthInfoDao
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.huawei.HuaweiUserAuthInfo
import org.springframework.stereotype.Service
import java.time.Instant

/**
 * 华为-用户授权信息 服务类
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Service
class HuaweiUserAuthInfoService
    : ServiceImpl<HuaweiUserAuthInfoDao, HuaweiUserAuthInfo>(), DeleteInterface<HuaweiUserAuthInfo> {

    @CreateCache(name = HUAWEI_USER_ID_BY_OPEN_ID, expire = 7 * 24 * 3600)
    lateinit var userIdCache: Cache<String, Long>

    /**
     * 根据openId获取用户ID
     */
    //@Cached(name = HUAWEI_USER_ID_BY_OPEN_ID, key = "#openId", expire = 7 * 24 * 3600)
    fun getUserIdByOpenId(openId: String): Long {
        val query = createNotDeletedQueryWrapper()
        query.eq(HuaweiUserAuthInfo.OPEN_ID_FIELD, openId)
        query.orderByDesc(HuaweiUserAuthInfo.UPDATE_TIME_FIELD)
        val authInfo = list(query).firstOrNull()
        throwIfNull(authInfo, ServiceException(UploadErrorCode.HUAWEI_USER_AUTH_INFO_NOT_FOUND))
        return authInfo!!.userId
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserIdOrNull(userId: Long): HuaweiUserAuthInfo? {
        val query = createNotDeletedQueryWrapper()
        query.eq(HuaweiUserAuthInfo.USER_ID_FIELD, userId)
        query.orderByDesc(HuaweiUserAuthInfo.UPDATE_TIME_FIELD)
        return list(query).firstOrNull()
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserId(userId: Long): HuaweiUserAuthInfo {
        val query = createNotDeletedQueryWrapper()
        query.eq(HuaweiUserAuthInfo.USER_ID_FIELD, userId)
        val authInfo = getOneByUserIdOrNull(userId)
        throwIfNull(authInfo, ServiceException(UploadErrorCode.HUAWEI_USER_AUTH_INFO_NOT_FOUND))
        return authInfo!!
    }

    /**
     * 插入并添加缓存
     */
    fun create(authInfo: HuaweiUserAuthInfo): HuaweiUserAuthInfo {
        authInfo.accessTokenUpdateTime = Instant.now()
        authInfo.deleted = false
        save(authInfo)
        return authInfo
    }

    /**
     * 更新并清除缓存
     */
    fun updateWithCache(authInfo: HuaweiUserAuthInfo) {
        authInfo.updateTime = Instant.now()
        updateById(authInfo)
        userIdCache.REMOVE(authInfo.openId)
    }

    /**
     * 删除并清除缓存
     */
    fun deleteWithCache(authInfo: HuaweiUserAuthInfo) {
        authInfo.deleted = true
        authInfo.updateTime = Instant.now()
        updateById(authInfo)
        userIdCache.REMOVE(authInfo.openId)
    }

    /**
     * 根据unionId获取列表
     */
    fun getListByUnionId(unionId: String): List<HuaweiUserAuthInfo> {
        val query = createNotDeletedQueryWrapper()
        query.eq(HuaweiUserAuthInfo.UNION_ID_FIELD, unionId)
        return list(query)
    }
}