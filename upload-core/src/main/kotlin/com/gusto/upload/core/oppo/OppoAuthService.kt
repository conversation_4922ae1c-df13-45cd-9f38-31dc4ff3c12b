package com.gusto.upload.core.oppo

import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.model.entity.oppo.OppoUserAuthInfo
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.Instant

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Service
class OppoAuthService : OppoCommonService() {

    private val log = LoggerFactory.getLogger(javaClass)

    /**
     * 授权回调
     */
    fun upsertAccessTokenByCode(authorizeCode: String, openId: String, userId: Long): Boolean {
        val rsp = oppoClient.getAccessToken(
            clientId = config.clientId,
            clientSecret = config.clientSecret,
            authorizationCode = authorizeCode,
            redirectUri = config.redirectUri,
            headers = buildHeaders(null)
        )
        if (rsp.isError) {
            log.error("[upsertAccessTokenByCode] get AT error, error = ${rsp.content}")
            return false
        }
        if (rsp.result.errorCode != 0) {
            log.error("[upsertAccessTokenByCode] get AT error, error = ${rsp.result.formatToJson()}")
            return false
        }
        val authInfo = OppoUserAuthInfo().apply {
            this.userId = userId
            refreshToken = rsp.result.body.refreshToken
            accessToken = rsp.result.body.accessToken
            accessTokenUpdateTime = Instant.now()
            this.openId = openId
        }

        val userInfoRsp = oppoClient.getUserInfo(buildHeaders(authInfo.accessToken))
        if (userInfoRsp.isSuccess && userInfoRsp.result.errorCode == 0) {
            authInfo.nickname = userInfoRsp.result.body?.userName ?: ""
        }

        // 查询此OPPO账号是否绑定了多个第一赛道账号
        val dbAuthInfoListByOpenId = authInfoService.getListByOpenId(authInfo.openId)
        if (dbAuthInfoListByOpenId.isNotEmpty()) {
            dbAuthInfoListByOpenId.forEach {
                // 踢掉此OPPO账号的其他第一赛道账号授权，不用请求接口，因为OPPO是根据openId推送运动记录的
                authInfoService.removeById(it)
            }
        }
        // 查询此第一赛道是否绑定了多个OPPO账号
        val dbAuthInfoListByUserId = authInfoService.getListByUserId(authInfo.userId)
        if (dbAuthInfoListByUserId.isNotEmpty()) {
            dbAuthInfoListByUserId.forEach {
                // 踢掉此OPPO账号的其他第一赛道账号授权，不用请求接口，因为OPPO是根据openId推送运动记录的
                authInfoService.removeById(it)
            }
        }
        // 插入新的授权信息
        authInfoService.save(authInfo)
        return true
    }

    /**
     * 根据用户ID取消授权
     */
    fun cancelAuthByUserId(userId: Long): Boolean {
        val authInfo = authInfoService.getOneByUserId(userId)
        checkOrRefreshAccessToken(authInfo)
        val rsp = oppoClient.cancelAuth(authInfo.accessToken, config.clientSecret, buildHeaders(null))
        if (rsp.isError) {
            log.error("[cancelAuthByUserId] cancel error, error = ${rsp.content}")
            return false
        }
        if (rsp.result.errorCode != 0) {
            log.error("[cancelAuthByUserId] cancel error, rsp = ${rsp.result.formatToJson()}")
            return false
        }
        authInfoService.removeById(authInfo)
        return true
    }

}
