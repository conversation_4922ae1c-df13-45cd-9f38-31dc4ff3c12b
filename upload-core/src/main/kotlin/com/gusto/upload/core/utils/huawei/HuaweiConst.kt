package com.gusto.upload.core.utils.huawei

/**
 * 华为-常量
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
object HuaweiConst {
    const val HUAWEI_SUMMARY_TOTAL_DISTANCE = "com.huawei.continuous.distance.total"
    const val HUAWEI_SUMMARY_TOTAL_STEPS = "com.huawei.continuous.steps.total"
    const val HUAWEI_SUMMARY_TOTAL_CALORIES = "com.huawei.continuous.calories.burnt.total"
    const val HUAWEI_SUMMARY_HEART_RATE_STATS = "com.huawei.continuous.heart_rate.statistics"
    const val HUAWEI_SUMMARY_SPEED_STATS = "com.huawei.continuous.speed.statistics"
    const val HUAWEI_SUMMARY_STEPS_RATE_STATS = "com.huawei.continuous.steps.rate.statistics"
    const val HUAWEI_SUMMARY_ALTITUDE = "com.huawei.continuous.altitude.statistics"

    const val HUAWEI_DATA_TYPE_LOCATION = "com.huawei.instantaneous.location.sample"
    const val HUAWEI_DATA_TYPE_EXERCISE_HEART_RATE = "com.huawei.instantaneous.exercise_heart_rate"
    const val HUAWEI_DATA_TYPE_SPEED = "com.huawei.instantaneous.speed"
    const val HUAWEI_DATA_TYPE_STEPS_RATE = "com.huawei.instantaneous.steps.rate"
    const val HUAWEI_DATA_TYPE_ALTITUDE = "com.huawei.instantaneous.altitude"

    const val HUAWEI_DATA_ID_LOCATION = "derived:com.huawei.instantaneous.location.sample:com.huawei.hwid:merged"
    const val HUAWEI_DATA_ID_EXERCISE_HEART_RATE =
        "derived:com.huawei.instantaneous.exercise_heart_rate:com.huawei.hwid:merged"
    const val HUAWEI_DATA_ID_SPEED = "derived:com.huawei.instantaneous.speed:com.huawei.hwid:merged"
    const val HUAWEI_DATA_ID_STEPS_RATE = "derived:com.huawei.instantaneous.steps.rate:com.huawei.hwid:merged"
    const val HUAWEI_DATA_ID_ALTITUDE = "derived:com.huawei.instantaneous.altitude:com.huawei.hwid:merged"
}