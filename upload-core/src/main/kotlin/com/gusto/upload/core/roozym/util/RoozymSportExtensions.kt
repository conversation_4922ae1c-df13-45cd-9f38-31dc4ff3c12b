package com.gusto.upload.core.roozym.util

import cn.hutool.core.date.LocalDateTimeUtil
import com.gusto.upload.model.entity.roozym.RoozymSport
import com.gusto.upload.model.entity.roozym.RoozymSportType
import com.gusto.upload.model.entity.user.old.UserRun
import java.time.ZoneOffset

/**
 *
 * <AUTHOR>
 * @since 2022/5/26
 */
fun RoozymSport.toUserRun(userId: Long): UserRun {
    val startTime = LocalDateTimeUtil.parse(this.startDate, "yyyy-MM-dd HH:mm:ss")
    val endTime = LocalDateTimeUtil.parse(this.endDate, "yyyy-MM-dd HH:mm:ss")
    val run = UserRun()
    run.userId = userId
    run.year = startTime.year
    run.month = startTime.monthValue
    run.day = startTime.dayOfMonth
    run.runId = startTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()
    run.duration = this.actTime
    run.distance = (this.distance.toFloat() / 100.0).toFloat()
    run.stepCount = this.step
    run.calorie = this.calories.toFloat() / 1000
    run.averageHeartRate = this.avgHeart
    if (this.step != 0) {
        run.stepStride = this.distance.toFloat() / this.step.toFloat()
    } else {
        run.stepStride = 0.0f
    }
    run.stepFrequency = this.avgFrequency
    run.startTime = startTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()
    run.endTime = endTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()
    run.event = this.sportType.toString()
    run.eventId = this.sportId
    run.abnormal = 10
    run.subSource = 306 // 如骏
    run.sportType = RoozymSportTypeUtils.formatSportType(this.sportType)
    run.ascent = this.upHeight.toFloat()
    run.descent = this.downHeight.toFloat()
    if (this.avgSpeed == 0) run.averagePace = 0.0f
    else {
        when (this.sportType) {
            RoozymSportType.CYCLING.number -> {
                run.averagePace = (1000.0 / (this.avgSpeed.toDouble() / 10.0 / 3.6)).toFloat()
            }

            RoozymSportType.MOUNTAIN_CLIMBING.number -> {
                run.averagePace = (1000.0 / (this.avgSpeed.toDouble() / 3600)).toFloat()
            }

            RoozymSportType.SWIMMING_POOL.number, RoozymSportType.SWIMMING_OPEN_WATER.number -> {
                run.averagePace = this.avgSpeed.toFloat() * 10.0f
            }

            else -> {
                run.averagePace = this.avgSpeed.toFloat()
            }
        }
    }
    return run
}
