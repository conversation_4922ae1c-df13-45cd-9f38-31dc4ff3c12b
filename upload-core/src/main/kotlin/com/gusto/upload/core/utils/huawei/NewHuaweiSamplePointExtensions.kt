package com.gusto.upload.core.utils.huawei

import com.gusto.upload.model.entity.huawei.HuaweiSamplePoint
import com.gusto.upload.model.entity.user.NewUserRunDetailAltitudeItem
import com.gusto.upload.model.entity.user.NewUserRunDetailHeartRateItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLocationItem
import com.gusto.upload.model.entity.user.NewUserRunDetailSpeedItem
import com.gusto.upload.model.entity.user.NewUserRunDetailStepRateItem

/**
 * 新 将华为采样点转化为轨迹明细
 */
fun HuaweiSamplePoint.buildLocationOrNull(): NewUserRunDetailLocationItem? {
    val location = NewUserRunDetailLocationItem()
    location.timestamp = startTime / 1000000
    value.forEach {
        when (it.fieldName) {
            "latitude" -> location.latitude = it.floatValue
            "longitude" -> location.longitude = it.floatValue
        }
    }
    if (location.latitude.toString().length <= 5) return null
    if (location.longitude.toString().length <= 5) return null
    return location
}

/**
 * 新 将华为采样点转化为心率明细
 */
fun HuaweiSamplePoint.buildHeartRate(): NewUserRunDetailHeartRateItem {
    val heartRate = NewUserRunDetailHeartRateItem()
    heartRate.timestamp = startTime / 1000000
    value.forEach {
        when (it.fieldName) {
            "bpm" -> heartRate.heartRate = it.floatValue.toInt()
        }
    }
    return heartRate
}

/**
 * 新 将华为采样点转化为配速明细
 */
fun HuaweiSamplePoint.buildSpeed(): NewUserRunDetailSpeedItem {
    val speed = NewUserRunDetailSpeedItem()
    speed.timestamp = startTime / 1000000
    value.forEach {
        when (it.fieldName) {
            "speed" -> speed.speed = it.floatValue
        }
    }
    return speed
}

/**
 * 将华为采样点转化为步频明细
 */
fun HuaweiSamplePoint.buildStepRate(): NewUserRunDetailStepRateItem {
    val step = NewUserRunDetailStepRateItem()
    step.timestamp = startTime / 1000000
    value.forEach {
        when (it.fieldName) {
            "step_rate" -> step.stepRate = it.floatValue.toInt()
        }
    }
    return step
}

/**
 * 将华为采样点转化为海拔明细
 */
fun HuaweiSamplePoint.buildAltitude(): NewUserRunDetailAltitudeItem {
    val altitude = NewUserRunDetailAltitudeItem()
    altitude.timestamp = startTime / 1000000
    value.forEach {
        when (it.fieldName) {
            "altitude" -> altitude.altitude = it.floatValue
        }
    }
    return altitude
}
