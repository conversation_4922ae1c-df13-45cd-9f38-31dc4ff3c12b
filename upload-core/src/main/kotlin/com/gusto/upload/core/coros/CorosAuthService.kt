package com.gusto.upload.core.coros

import cn.hutool.core.util.StrUtil
import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.model.entity.coros.CorosUserAuthInfo
import com.gusto.upload.model.entity.user.User
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.Instant

/**
 * 高驰-授权
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Service
class CorosAuthService : CorosCommonService() {

    private val log = LoggerFactory.getLogger(javaClass)

    /**
     * 授权回调
     */
    fun upsertAccessTokenByCode(code: String, user: User): <PERSON><PERSON>an {
        val rsp = corosClient.getAccessToken(
//            code = URLEncoder.encode(code, "UTF-8"),
            code = code,
            clientId = config.clientId,
            clientSecret = config.clientSecret,
            redirectUri = config.redirectUri
        )
        if (rsp.isError) {
            log.error("[upsertAccessTokenByCode] get AT error, error = ${rsp.content}")
            return false
        }
        val authInfo = CorosUserAuthInfo().apply {
            userId = user.userId
            refreshToken = rsp.result.refreshToken
            accessToken = rsp.result.accessToken
            accessTokenUpdateTime = Instant.now()
            openId = rsp.result.openId
        }

        val userInfoRsp = corosClient.getUserInfo(authInfo.accessToken, authInfo.openId)
        if (userInfoRsp.isSuccess) {
            authInfo.nickname = userInfoRsp.result.data.nick
            if (StrUtil.isNotEmpty(userInfoRsp.result.data.profilePhoto)) {
                val avatar = "coros_avatar_${authInfo.userId}_${Instant.now().epochSecond}.jpg"
                qiniuService.uploadUrl(userInfoRsp.result.data.profilePhoto, avatar)
                authInfo.avatar = "https://file.gusto.cn/$avatar"
            }
        }

        // 查询此高驰账号是否绑定了多个第一赛道账号
        val dbAuthInfoListByOpenId = authInfoService.getListByOpenId(authInfo.openId ?: "")
        if (dbAuthInfoListByOpenId.isNotEmpty()) {
            dbAuthInfoListByOpenId.forEach {
                // 踢掉此高驰账号的其他第一赛道账号授权，不用请求接口，因为高驰是根据openId推送运动记录的
                authInfoService.removeById(it)
            }
        }
        // 查询此第一赛道是否绑定了多个高驰账号
        val dbAuthInfoListByUserId = authInfoService.getListByUserId(authInfo.userId)
        if (dbAuthInfoListByUserId.isNotEmpty()) {
            dbAuthInfoListByUserId.forEach {
                // 踢掉此高驰账号的其他第一赛道账号授权，不用请求接口，因为高驰是根据openId推送运动记录的
                authInfoService.removeById(it)
            }
        }
        // 插入新的授权信息
        authInfoService.save(authInfo)
        return true
    }

    /**
     * 根据用户ID取消授权
     */
    fun cancelAuthByUserId(userId: Long): Boolean {
        val authInfo = authInfoService.getOneByUserId(userId)
        checkOrRefreshAccessToken(authInfo)
        val rsp = corosClient.cancelAuth(authInfo.accessToken)
        if (rsp.isError) {
            log.error("[cancelAuthByUserId] cancel error, error = ${rsp.content}")
            return false
        }
        if (rsp.result.message == "OK") {
            authInfoService.removeById(authInfo)
            return true
        }
        log.error("[cancelAuthByUserId] cancel error, rsp = ${rsp.result.formatToJson()}")
        if (rsp.result.result == "5006" || rsp.result.message.contains("Token expired or invalid")) {
            authInfoService.removeById(authInfo)
            return true
        }
        return false
    }

}
