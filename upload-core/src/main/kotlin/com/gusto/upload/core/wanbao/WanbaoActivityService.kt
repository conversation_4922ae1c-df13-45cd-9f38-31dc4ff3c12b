package com.gusto.upload.core.wanbao

import com.gusto.framework.core.util.time.minusDays
import com.gusto.upload.core.application.user.NewUserRunApplicationService
import com.gusto.upload.core.common.PushService
import com.gusto.upload.core.qiniu.QiniuService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.util.DistanceUtils
import com.gusto.upload.core.util.NewUserRunMapUtils
import com.gusto.upload.core.util.formatDateHourMinuteSecond
import com.gusto.upload.core.utils.JsonUtils
import com.gusto.upload.core.utils.formatPaceForWanBao
import com.gusto.upload.core.utils.formatSpeedForWanBao
import com.gusto.upload.core.utils.formatTimestamp
import com.gusto.upload.core.utils.round
import com.gusto.upload.model.entity.response.ManualSyncRsp
import com.gusto.upload.model.entity.user.NewUserRun
import com.gusto.upload.model.entity.user.NewUserRunDetailAltitudeItem
import com.gusto.upload.model.entity.user.NewUserRunDetailHeartRateItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLapItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLocationItem
import com.gusto.upload.model.entity.user.NewUserRunDetailStepRateItem
import com.gusto.upload.model.entity.wanbao.WanbaoUserAuthInfo
import com.gusto.upload.model.entity.wanbao.rsp.WanbaoGetActivityDetailByFileRsp
import com.gusto.upload.model.entity.wanbao.rsp.WanbaoGetActivityDetailCycleRsp
import com.gusto.upload.model.entity.wanbao.rsp.WanbaoGetActivityDetailPointRsp
import com.gusto.upload.model.entity.wanbao.rsp.WanbaoGetActivityDetailRsp
import com.gusto.upload.model.entity.wanbao.rsp.WanbaoGetActivityRsp
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import kotlin.math.max


/**
 * <AUTHOR>
 * @since 2024-09-12
 */
@Service
class WanbaoActivityService : WanbaoCommonService() {

    private val log = LoggerFactory.getLogger(javaClass)
    private val objectMapper = JsonUtils.initObjectMapper(lowerCamelCase = true)
    private val halfMarathon = 21.0975
    private val marathon = 42.195

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var newRunApplicationService: NewUserRunApplicationService

    @Autowired
    lateinit var qiniuService: QiniuService

    @Autowired
    lateinit var pushService: PushService

    @Autowired
    lateinit var mapUtils: NewUserRunMapUtils

    /**
     * 根据订阅事件同步活动
     */
    fun syncActivityBySubscription(openId: String, countId: Long, sportType: String, fileUrl: String) {
        val authInfo = authInfoService.getOneByOpenIdOrNull(openId) ?: return
        checkOrRefreshAccessToken(authInfo)
        // 创建跑步记录头部和明细
        createUserRunAndDetail(authInfo, fileUrl)
    }

    /**
     * 手动同步活动列表
     */
    fun syncActivityListByManual(userId: Long): ManualSyncRsp {
        val authInfo = authInfoService.getOneByUserId(userId)
        checkOrRefreshAccessToken(authInfo)
        val endTime = Instant.now()
        val startTime = max(authInfo.createTime.toEpochMilli(), endTime.minusDays(7).toEpochMilli())
        val rsp = wanbaoClient.getActivityListByDate(
            current = 1,
            size = 100,
            startTime = Instant.ofEpochMilli(startTime).formatDateHourMinuteSecond(),
            endTime = endTime.formatDateHourMinuteSecond(),
            headers = buildHeaders(authInfo.accessToken)
        )
        val outRsp = ManualSyncRsp()
        outRsp.result = true
        outRsp.reason = "同步成功，请稍后在跑步记录页查看"
        if (rsp.isError) {
            log.error("[syncActivityListByManual] userId = $userId, get activity list error, response content = ${rsp.content}")
            outRsp.result = false
            outRsp.reason = "同步失败，请稍后再试"
            return outRsp
        }
        if (rsp.result == null || rsp.result.data.records.isEmpty()) {
            outRsp.reason = "暂无运动记录可同步"
            return outRsp
        }
        // 创建跑步记录头部和明细
//        createUserRunAndDetail(authInfo, rsp.result.data.records)
        asyncExecutor.execute {
            log.info("[syncActivityListByManual] userId = $userId, sync async start")
            createUserRunAndDetail(authInfo, rsp.result.data.records)
            log.info("[syncActivityListByManual] userId = $userId, sync async success")
        }
        return outRsp
    }

    /**
     * 创建跑步记录并上传到APP
     */
    private fun createUserRunAndDetail(authInfo: WanbaoUserAuthInfo, activityList: List<WanbaoGetActivityRsp>) {
        var syncSuccessCount = 0
        val runList = mutableListOf<NewUserRun>()
        activityList.forEach { activity ->
            val activityDetail = getActivityDetail(authInfo, activity.sportType, activity.countId) ?: return@forEach
            val run = buildNewUserRun(authInfo, activity)
            val createSuccess = uploadRunAndDetail(
                run,
                activity,
                activityDetail.sportDetailList ?: emptyList(),
                activityDetail.sportCircleList ?: emptyList()
            )
            if (!createSuccess) return@forEach
            syncSuccessCount++
            runList.add(run)
        }
        log.info("[createUserRunAndDetail] userId = ${authInfo.userId}, sync success, count = $syncSuccessCount")
        if (syncSuccessCount == 0) return
        asyncExecutor.execute {
            val latestRun = runList.maxByOrNull { it.startTime }
            if (latestRun != null) {
                pushService.notifySyncSuccess(latestRun, syncSuccessCount, authInfo.userId)
            }
        }
    }

    /**
     * 创建跑步记录并上传到APP
     */
    private fun createUserRunAndDetail(authInfo: WanbaoUserAuthInfo, fileUrl: String) {
        val activityDetail = getActivityDetailByFileUrl(fileUrl) ?: return
        val run = buildNewUserRun(authInfo, activityDetail.sportCount)
        val createSuccess = uploadRunAndDetail(
            run,
            activityDetail.sportCount,
            activityDetail.detailList ?: emptyList(),
            activityDetail.sportCircleList ?: emptyList()
        )
        if (!createSuccess) return
        log.info("[createUserRunAndDetail] userId = ${authInfo.userId}, sync success, count = 1")
        asyncExecutor.execute {
            pushService.notifySyncSuccess(run, 1, authInfo.userId)
        }
    }

    /**
     * 上传跑步记录和明细
     */
    private fun uploadRunAndDetail(
        run: NewUserRun,
        activity: WanbaoGetActivityRsp,
        detailList: List<WanbaoGetActivityDetailPointRsp>,
        cycleList: List<WanbaoGetActivityDetailCycleRsp>
    ): Boolean {
        run.apply {
            if (detailList.isNotEmpty()) {
                minAltitude = detailList.minOfOrNull { it.altitude } ?: 0.0
                val pair = buildAscentAndDescent(detailList.map { it.altitude ?: 0.0 })
                totalAscent = pair.first
                totalDescent = pair.second
            }
            partTimeKmList = buildPartTimeMap(detailList)
        }
        val createSuccess = newRunApplicationService.handlerAndCreate(run)
        if (createSuccess) {
            val locationList = mutableListOf<NewUserRunDetailLocationItem>()
            val heartRateList = mutableListOf<NewUserRunDetailHeartRateItem>()
            val stepRateList = mutableListOf<NewUserRunDetailStepRateItem>()
            val altitudeList = mutableListOf<NewUserRunDetailAltitudeItem>()
            detailList.forEach {
                locationList.add(buildLocation(it, run.activityType))
                heartRateList.add(buildHeartRate(it))
                stepRateList.add(buildStepRate(it))
                altitudeList.add(buildAltitude(it))
            }
            val lapList = cycleList
                .map { buildLap(it) }
                .toMutableList()
            val sumLapDistance = lapList.sumOf { it.distance }
            if (run.totalDistance - sumLapDistance > 0) {
                val lastLap = NewUserRunDetailLapItem()
                lastLap.distance = run.totalDistance - sumLapDistance
                lastLap.duration = run.totalDuration - lapList.sumOf { it.duration }
                lapList.add(lastLap)
            }
            val detail = mapUtils.toNewUserRunDetail(run)
            detail.lapList = lapList
            detail.locationList = locationList
            detail.heartRateList = heartRateList
            detail.stepRateList = stepRateList
            detail.altitudeList = altitudeList
            detail.partTimeKmList = run.partTimeKmList
            val rawData = WanbaoGetActivityDetailByFileRsp()
            rawData.sportCount = activity
            rawData.detailList = detailList
            rawData.sportCircleList = cycleList
            asyncExecutor.execute {
                // 上传到七牛
                qiniuService.uploadObject(
                    detail,
                    "${run.trackFile}.gzip"
                )
                qiniuService.uploadObject(
                    rawData,
                    "wanbao_activity_detail_${run.trackFile}.gzip"
                )
            }
        }
        return createSuccess
    }

    /**
     * 创建每公里用时
     */
    private fun buildPartTimeMap(gpsList: List<WanbaoGetActivityDetailPointRsp>): Map<String, Int> {
        val partTimeKmMap = mutableMapOf<String, Int>()
        if (gpsList.isEmpty()) {
            return partTimeKmMap
        }
        var lastTimestamp = gpsList.first().recordTime.formatTimestamp()
        var halfMarathonFlag = false
        var marathonFlag = false
        var currentDuration = 0L
        var currentDistance = 0.0
        var currentLap = 1
        gpsList.forEachIndexed { index, gps ->
            val currentTimestamp = gps.recordTime.formatTimestamp()
            val timeDiff = currentTimestamp - lastTimestamp
            currentDuration += timeDiff / 1000
            val distanceDiff =
                if (index > 1 && gps.latitude > 0 && gps.longitude > 0 && gpsList[index - 1].latitude > 0 && gpsList[index - 1].longitude > 0) {
                    DistanceUtils.getDistance(
                        gps.latitude,
                        gps.longitude,
                        gpsList[index - 1].latitude,
                        gpsList[index - 1].longitude
                    )
                } else {
                    0.0
                }
            currentDistance += distanceDiff
            lastTimestamp = currentTimestamp

            // 计算半马、全马累计用时、每公里用时
            if (!halfMarathonFlag && currentDistance >= halfMarathon * 1000) {
                val halfMarathonItem = partTimeKmMap[halfMarathon.toString()]
                if (halfMarathonItem == null) {
                    partTimeKmMap[halfMarathon.toString()] = currentDuration.toInt()
                    halfMarathonFlag = true
                }
            } else if (!marathonFlag && currentDistance >= marathon * 1000) {
                val marathonItem = partTimeKmMap[marathon.toString()]
                if (marathonItem == null) {
                    partTimeKmMap[marathon.toString()] = currentDuration.toInt()
                    marathonFlag = true
                }
            } else {
                if (currentDistance >= 1000) {
                    if (currentDistance / 1000 >= currentLap) {
                        partTimeKmMap[currentLap.toDouble().toString()] = currentDuration.toInt()
                        currentLap++
                    }
                }
            }
        }
        return partTimeKmMap.toSortedMap(compareBy { it.toDouble() })
    }

    /**
     * 创建新跑步记录
     */
    private fun buildNewUserRun(authInfo: WanbaoUserAuthInfo, activity: WanbaoGetActivityRsp): NewUserRun {
        val startTime = activity.startTime.formatTimestamp()
        val endTime = activity.endTime.formatTimestamp()
        val run = NewUserRun().apply {
            activityId = startTime
            userId = authInfo.userId
            deviceId = 3
            deviceType = 310
            deviceModel = "腕宝"
            activityType = formatActivityType(activity.sportType)
            this.startTime = startTime
            this.endTime = endTime
            totalDistance = activity.totalDistance?.toDouble() ?: 0.0
            totalDuration = activity.totalTime ?: 0
            totalStep = activity.totalSteps ?: 0
            totalCalorie = activity.totalCalorie?.round(0) ?: 0.0
            averagePace = activity.avgSpeed.formatPaceForWanBao(activityType)
            maxPace = activity.maxSpeed.formatPaceForWanBao(activityType)
            averageHeartRate = activity.avgHeartRate?.toInt() ?: 0
            maxHeartRate = activity.maxHeartRate?.toInt() ?: 0
            averageStepRate = activity.avgFrequency?.toInt() ?: 0
            maxStepRate = activity.maxFrequency?.toInt() ?: 0
            averageStride = activity.avgStride ?: 0.0
            minAltitude = 0.0
            maxAltitude = activity.maxAltitude ?: 0.0
//            totalAscent = sportCount.totalAscend ?: 0.0
//            totalDescent = sportCount.totalDescend ?: 0.0
            totalAscent = 0.0
            totalDescent = 0.0
            weather = ""
            province = ""
            city = ""
            temperature = ""
            humidity = ""
            partTimeKmList = mutableMapOf()
            thirdActivityId = activity.countId.toString()
            thirdActivityType = activity.sportType
            abnormal = 13
            externalSportType = activity.sportType
            mapTrackImage = ""
            trackImage = ""
            fitUrl = ""
        }
        return run
    }

    /**
     * 转换活动类型
     *
     * 运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它
     */
    private fun formatActivityType(sportType: String): Int {
        return when (sportType) {
            // 1 - 户外跑
            "run_outside" /* 室外跑步 */ -> 1
            "triation_run" /* 铁人三项-跑步 */ -> 1
            "marathon_run_outside" /* 马拉松运动课程跑 */ -> 1
            "marathon_trail_run" /* 马拉松运动 */ -> 1

            // 2 - 室内跑
            "run_inside" /* 室内跑步 */ -> 2
            "marathon_run_inside" /* 马拉松课程计划跑 */ -> 2

            // 3 - 徒步
            "walk_outside" /* 室外行走 */ -> 3
            "walk_inside" /* 室内行走 */ -> 3
            "hiking" /* 户外徒步 */ -> 3

            // 4 - 越野
            "trail_run" /* 越野跑 */ -> 4
            "climb" /* 登山 */ -> 4

            // 5 - 骑车
            "rider_outside" /* 室外骑行 */ -> 5
            "rider_inside" /* 室内骑行 */ -> 5
            "triation_rider" /* 铁人三项-自行车 */ -> 5

            // 6 - 游泳
            "swim_inside" /* 泳池游泳 */ -> 6
            "swim_outside" /* 户外游泳 */ -> 6
            "triation_swim" /* 铁人三项-游泳 */ -> 6

            else -> 100
        }
    }

    /**
     * 获取运动详情，测试链接：https://osstest.9n1m.com/evoc1-diving-test/third_sport_json/8e6f6f0d927531a6f8d5a55237388684.json
     */
    fun getActivityDetailByFileUrl(fileUrl: String): WanbaoGetActivityDetailByFileRsp? {
        val rsp = wanbaoClient.getActivityDetailByFileUrl(fileUrl)
        if (rsp.isError || rsp.result == null) {
            log.error("[getActivityDetail] get activity detail error, error = ${rsp.content}")
            return null
        }
        return objectMapper.readValue(rsp.result, WanbaoGetActivityDetailByFileRsp::class.java)
    }

    /**
     * 获取运动详情
     */
    fun getActivityDetail(authInfo: WanbaoUserAuthInfo, sportType: String, countId: Long): WanbaoGetActivityDetailRsp? {
        val rsp = wanbaoClient.getActivityDetail(sportType, countId, buildHeaders(authInfo.accessToken))
        if (rsp.isError || rsp.result == null) {
            log.error("[getActivityDetail] get activity detail error, error = ${rsp.content}")
            return null
        }
        return rsp.result.data
    }

    /**
     * 获取累计爬升和下降
     */
    private fun buildAscentAndDescent(eleList: List<Double>): Pair<Double, Double> {
        var ascent = 0.0
        var descent = 0.0
        for (i in 1 until eleList.size) {
            val prevElevation = eleList[i - 1]
            val currentElevation = eleList[i]
            if (currentElevation > prevElevation) {
                ascent += currentElevation - prevElevation
            } else if (currentElevation < prevElevation) {
                descent += prevElevation - currentElevation
            }
        }
        return Pair(ascent, descent)
    }

    /**
     * 创建轨迹
     */
    private fun buildLocation(
        detail: WanbaoGetActivityDetailPointRsp,
        activityType: Int
    ): NewUserRunDetailLocationItem {
        val item = NewUserRunDetailLocationItem()
        item.timestamp = detail.recordTime.formatTimestamp()
        item.latitude = detail.latitude ?: 0.0
        item.longitude = detail.longitude ?: 0.0
        item.speed = detail.speed?.formatSpeedForWanBao(activityType) ?: 0.0
        return item
    }

    /**
     * 创建海拔
     */
    private fun buildAltitude(detail: WanbaoGetActivityDetailPointRsp): NewUserRunDetailAltitudeItem {
        val item = NewUserRunDetailAltitudeItem()
        item.timestamp = detail.recordTime.formatTimestamp()
        item.altitude = detail.altitude ?: 0.0
        return item
    }

    /**
     * 创建心率
     */
    private fun buildHeartRate(detail: WanbaoGetActivityDetailPointRsp): NewUserRunDetailHeartRateItem {
        val item = NewUserRunDetailHeartRateItem()
        item.timestamp = detail.recordTime.formatTimestamp()
        item.heartRate = detail.heartRate?.toInt() ?: 0
        return item
    }

    /**
     * 创建步频
     */
    private fun buildStepRate(detail: WanbaoGetActivityDetailPointRsp): NewUserRunDetailStepRateItem {
        val item = NewUserRunDetailStepRateItem()
        item.timestamp = detail.recordTime.formatTimestamp()
        item.stepRate = detail.sportFrequency?.toInt() ?: 0
        return item
    }

    /**
     * 创建每圈信息
     */
    private fun buildLap(cycle: WanbaoGetActivityDetailCycleRsp): NewUserRunDetailLapItem {
        val item = NewUserRunDetailLapItem()
        item.distance = cycle.singleDistance?.toDouble() ?: 0.0
        item.duration = cycle.singleTime ?: 0
        return item
    }

}