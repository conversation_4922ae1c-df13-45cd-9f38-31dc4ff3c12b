package com.gusto.upload.core.coros

import com.dtflys.forest.annotation.BaseRequest
import com.dtflys.forest.annotation.Body
import com.dtflys.forest.annotation.Get
import com.dtflys.forest.annotation.Header
import com.dtflys.forest.annotation.Post
import com.dtflys.forest.annotation.Query
import com.dtflys.forest.annotation.Retry
import com.dtflys.forest.http.ForestResponse
import com.gusto.upload.model.entity.coros.rsp.CorosCommonRsp
import com.gusto.upload.model.entity.coros.rsp.CorosGetAccessTokenRsp
import com.gusto.upload.model.entity.coros.rsp.CorosGetActivityListRsp
import com.gusto.upload.model.entity.coros.rsp.CorosGetUserInfoRsp

/**
 * 高驰-开放平台
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@BaseRequest(
    baseURL = "#{upload.coros.apiHost}"
)
@Retry(maxRetryCount = "5", maxRetryInterval = "100")
interface CorosClient {

    /**
     * 使用授权码Code获取AT
     */
    @Post("oauth2/accesstoken")
    fun getAccessToken(
        @Body("client_id") clientId: String,
        @Body("redirect_uri") redirectUri: String,
        @Body("code") code: String,
        @Body("client_secret") clientSecret: String,
        @Body("grant_type") grantType: String = "authorization_code"
    ): ForestResponse<CorosGetAccessTokenRsp>

    /**
     * 通过RT刷新AT
     */
    @Post("oauth2/refresh-token")
    fun refreshAccessToken(
        @Body("client_id") clientId: String,
        @Body("refresh_token") refreshToken: String,
        @Body("client_secret") clientSecret: String,
        @Body("grant_type") grantType: String = "refresh_token"
    ): ForestResponse<CorosCommonRsp>

    /**
     * 取消授权
     */
    @Post("oauth2/deauthorize")
    fun cancelAuth(
        @Header("token") accessToken: String
    ): ForestResponse<CorosCommonRsp>

    /**
     * 获取指定⽇期区间的活动列表，日期最大范围30天
     */
    @Get("v2/coros/sport/list")
    fun getActivityListByDate(
        @Query("token") token: String,
        @Query("openId") openId: String,
        @Query("startDate") startDate: Int,
        @Query("endDate") endDate: Int
    ): ForestResponse<CorosGetActivityListRsp>

    /**
     * 获取⽤户信息
     */
    @Get("coros/userinfosim?")
    fun getUserInfo(
        @Query("token") token: String,
        @Query("openId") openId: String
    ): ForestResponse<CorosGetUserInfoRsp>

}
