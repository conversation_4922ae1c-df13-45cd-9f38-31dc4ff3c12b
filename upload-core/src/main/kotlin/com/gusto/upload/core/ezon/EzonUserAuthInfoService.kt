package com.gusto.upload.core.ezon

import com.baomidou.mybatisplus.core.toolkit.Wrappers
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwError
import com.gusto.upload.core.dao.ezon.EzonUserAuthInfoDao
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.ezon.EzonUserAuthInfo
import org.springframework.stereotype.Service

/**
 * 宜准-用户授权信息
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Service
class EzonUserAuthInfoService : ServiceImpl<EzonUserAuthInfoDao, EzonUserAuthInfo>() {


    /**
     * 根据宜准用户ID获取列表
     */
    fun getListByOpenId(openId: String): List<EzonUserAuthInfo> {
        val wrapper = Wrappers.lambdaQuery<EzonUserAuthInfo>()
            .eq(EzonUserAuthInfo::getOpenId, openId)
        return list(wrapper)
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserId(userId: Long): EzonUserAuthInfo {
        val wrapper = Wrappers.lambdaQuery<EzonUserAuthInfo>()
            .eq(EzonUserAuthInfo::getUserId, userId)
        val authInfo = getOne(wrapper)
        if (authInfo == null) {
            log.error("[getOneByUserId] userId = $userId not found")
            throwError(ServiceException(UploadErrorCode.EZON_USER_AUTH_INFO_NOT_FOUND))
        }
        return authInfo!!
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserIdOrNull(userId: Long): EzonUserAuthInfo? {
        val wrapper = Wrappers.lambdaQuery<EzonUserAuthInfo>()
            .eq(EzonUserAuthInfo::getUserId, userId)
        return getOne(wrapper)
    }

}
