package com.gusto.upload.core.common

/**
 * 缓存键值前缀常量
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
object CacheKeyPrefix {
    const val USER_BY_TOKEN = "UserService.getOneByToken."
    const val USER_BY_ID = "UserService.getOneById."

    const val HUAWEI_USER_ID_BY_OPEN_ID = "HuaweiUserAuthInfoService.getUserIdByOpenId."

    const val ROOZYM_USER_ID_BY_OPEN_ID = "RoozymUserAuthInfoService.getUserIdByOpenId."

    const val HONOR_USER_ID_BY_OPEN_ID = "HonorUserAuthInfoService.getUserIdByOpenId."
}
