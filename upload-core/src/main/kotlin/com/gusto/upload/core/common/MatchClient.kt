package com.gusto.upload.core.common

import com.dtflys.forest.annotation.BaseRequest
import com.dtflys.forest.annotation.JSONBody
import com.dtflys.forest.annotation.Post
import com.dtflys.forest.annotation.Retry
import com.dtflys.forest.http.ForestResponse

/**
 * 新第一赛道 API调用入口
 */
@BaseRequest(
    baseURL = "\${matchApiUrl}"
)
interface MatchClient {
    /**
     * 上传跑步记录通知
     */
    @Post("/user/run/uploadNotify")
    @Retry(maxRetryCount = "5", maxRetryInterval = "1000")
    fun userRunUploadNotify(
        @JSONBody("userId") userId: Long,
        @JSONBody("id") id: Long
    ): ForestResponse<String>
}