package com.gusto.upload.core.roozym

import com.dtflys.forest.annotation.Body
import com.dtflys.forest.annotation.Get
import com.dtflys.forest.annotation.Header
import com.dtflys.forest.annotation.JSONBody
import com.dtflys.forest.annotation.LogEnabled
import com.dtflys.forest.annotation.Post
import com.dtflys.forest.annotation.Retry
import com.dtflys.forest.http.ForestResponse
import com.gusto.upload.model.entity.roozym.RoozymAccessToken
import com.gusto.upload.model.entity.roozym.RoozymSport
import com.gusto.upload.model.entity.roozym.req.RoozymQuerySportReq
import com.gusto.upload.model.entity.roozym.req.RoozymUpsertSubscriptionReq
import com.gusto.upload.model.entity.roozym.rsp.RoozymCancelAuthRsp

/**
 *
 * <AUTHOR>
 * @since 2022/5/26
 */
@Retry(maxRetryCount = "0", maxRetryInterval = "0")
interface RoozymClient {
    /**
     * 运动数据查询接口
     */
    @LogEnabled(logResponseContent = false)
    @Post("https://rj-api-test.iipii.net/api/v1/data/sport/query")
    fun querySport(
        @JSONBody req: RoozymQuerySportReq,
        @Header headers: Map<String, Any>
    ): ForestResponse<List<RoozymSport>>

    /**
     * 使用授权码Code获取AT
     */
    @Post("https://rj-api-test.iipii.net/oauth/token")
    fun getAccessToken(
        @Body("grant_type") grantType: String = "authorization_code",
        @Body("code") code: String,
        @Body("client_id") clientId: String,
        @Body("client_secret") clientSecret: String,
        @Body("redirect_uri") redirectUri: String
    ): ForestResponse<RoozymAccessToken>

    /**
     * 新增/更新订阅记录
     */
    @Post("https://rj-api-test.iipii.net/api/v1/data/subscriptions")
    fun upsertSubscription(
        @JSONBody req: RoozymUpsertSubscriptionReq,
        @Header headers: Map<String, Any>
    ): ForestResponse<String>


    /**
     * rt刷新at
     */
    @Post("https://rj-api-test.iipii.net/oauth/token")
    fun refreshAccessToken(
        @Body("grant_type") grantType: String = "refresh_token",
        @Body("refresh_token") refreshToken: String,
        @Body("client_id") clientId: String,
        @Body("client_secret") clientSecret: String
    ): ForestResponse<RoozymAccessToken>

    /**
     * 取消授权
     */
    @Get("https://rj-api-test.iipii.net/oauth/cancel")
    fun cancelAuth(
        @Header headers: Map<String, Any>
    ): ForestResponse<RoozymCancelAuthRsp>
}
