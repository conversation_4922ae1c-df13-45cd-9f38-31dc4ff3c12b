package com.gusto.upload.core.kafka.comsumer

import com.alicp.jetcache.Cache
import com.alicp.jetcache.anno.CreateCache
import com.gusto.upload.core.garmin.GarminActivityService
import com.gusto.upload.core.service.NotifyRecordService
import com.gusto.upload.model.entity.message.GarminActivityMessage
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component
import java.time.Instant

/**
 * 佳明-活动
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Component
class GarminActivityConsumer {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var activityService: GarminActivityService

    @Autowired
    lateinit var notifyRecordService: NotifyRecordService

    @CreateCache(name = "GarminActivityConsumer.upsertUserRunCache.", expire = 60 * 3)
    private lateinit var upsertUserRunCache: Cache<String, String>

    /**
     * 处理订阅事件
     */
    @KafkaListener(
        topics = [GarminActivityMessage.NOTIFY_ACTIVITY_TOPIC],
        groupId = GarminActivityMessage.GROUP + "#UpsertUserRun"
    )
    fun upsertUserRun(message: GarminActivityMessage) {
        log.info("[upsertUserRun] get message = {}", message)
        val cache = upsertUserRunCache.GET(message.buildUniqueKey())
        if (cache.isSuccess) {
            log.info("[upsertUserRun] cache exist, return")
            return
        } else {
            upsertUserRunCache.PUT(message.buildUniqueKey(), Instant.now().toEpochMilli().toString())
        }
        activityService.syncActivityListBySubscription(message)
        notifyRecordService.removeByUniqueKey(message.buildUniqueKey())
        upsertUserRunCache.REMOVE(message.buildUniqueKey())
        log.info("[upsertUserRun] handle success")
    }

    /**
     * 处理海外订阅事件
     */
    @KafkaListener(
        topics = [GarminActivityMessage.NOTIFY_GLOBAL_ACTIVITY_TOPIC],
        groupId = GarminActivityMessage.GROUP + "#UpsertGlobalUserRun"
    )
    fun upsertGlobalUserRun(message: GarminActivityMessage) {
        log.info("[upsertGlobalUserRun] get message = {}", message)
        val cache = upsertUserRunCache.GET(message.buildUniqueKey())
        if (cache.isSuccess) {
            log.info("[upsertGlobalUserRun] cache exist, return")
            return
        } else {
            upsertUserRunCache.PUT(message.buildUniqueKey(), Instant.now().toEpochMilli().toString())
        }
        activityService.syncActivityListBySubscription(message)
        notifyRecordService.removeByUniqueKey(message.buildUniqueKey())
        upsertUserRunCache.REMOVE(message.buildUniqueKey())
        log.info("[upsertGlobalUserRun] handle success")
    }

}
