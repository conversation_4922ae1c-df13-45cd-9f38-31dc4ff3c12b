package com.gusto.upload.core.honor

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwIfNull
import com.gusto.upload.core.common.DeleteInterface
import com.gusto.upload.core.dao.honor.HonorUserAuthInfoDao
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.honor.HonorUserAuthInfo
import org.springframework.stereotype.Service
import java.time.Instant

/**
 * 荣耀-用户授权信息 服务类
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Service
class HonorUserAuthInfoService
    : ServiceImpl<HonorUserAuthInfoDao, HonorUserAuthInfo>(), DeleteInterface<HonorUserAuthInfo> {

    /**
     * 根据openId获取用户ID
     */
    fun getUserIdByOpenId(openId: String): Long {
        val query = createNotDeletedQueryWrapper()
        query.eq(HonorUserAuthInfo.OPEN_ID_FIELD, openId)
        query.orderByDesc(HonorUserAuthInfo.UPDATE_TIME_FIELD)
        val authInfo = list(query).firstOrNull()
        throwIfNull(authInfo, ServiceException(UploadErrorCode.HONOR_USER_AUTH_INFO_NOT_FOUND))
        return authInfo!!.userId
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserIdOrNull(userId: Long): HonorUserAuthInfo? {
        val query = createNotDeletedQueryWrapper()
        query.eq(HonorUserAuthInfo.USER_ID_FIELD, userId)
        query.orderByDesc(HonorUserAuthInfo.UPDATE_TIME_FIELD)
        return list(query).firstOrNull()
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserId(userId: Long): HonorUserAuthInfo {
        val query = createNotDeletedQueryWrapper()
        query.eq(HonorUserAuthInfo.USER_ID_FIELD, userId)
        val authInfo = getOneByUserIdOrNull(userId)
        throwIfNull(authInfo, ServiceException(UploadErrorCode.HONOR_USER_AUTH_INFO_NOT_FOUND))
        return authInfo!!
    }

    /**
     * 插入并添加缓存
     */
    fun create(authInfo: HonorUserAuthInfo): HonorUserAuthInfo {
        authInfo.accessTokenUpdateTime = Instant.now()
        authInfo.deleted = false
        save(authInfo)
        return authInfo
    }

    /**
     * 更新并清除缓存
     */
    fun updateWithCache(authInfo: HonorUserAuthInfo) {
        authInfo.updateTime = Instant.now()
        updateById(authInfo)
    }

    /**
     * 删除并清除缓存
     */
    fun deleteWithCache(authInfo: HonorUserAuthInfo) {
        authInfo.deleted = true
        authInfo.updateTime = Instant.now()
        updateById(authInfo)
    }

    /**
     * 根据unionId获取列表
     */
    fun getListByUnionId(unionId: String): List<HonorUserAuthInfo> {
        val query = createNotDeletedQueryWrapper()
        query.eq(HonorUserAuthInfo.UNION_ID_FIELD, unionId)
        return list(query)
    }

}