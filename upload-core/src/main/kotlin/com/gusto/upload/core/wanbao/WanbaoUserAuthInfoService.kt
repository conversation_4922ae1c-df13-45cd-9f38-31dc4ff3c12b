package com.gusto.upload.core.wanbao

import cn.hutool.core.util.StrUtil
import com.baomidou.mybatisplus.core.toolkit.Wrappers
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwError
import com.gusto.upload.core.dao.wanbao.WanbaoUserAuthInfoDao
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.wanbao.WanbaoUserAuthInfo
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @since 2024-09-12
 */
@Service
class WanbaoUserAuthInfoService : ServiceImpl<WanbaoUserAuthInfoDao, WanbaoUserAuthInfo>() {

    /**
     * 根据腕宝用户ID获取列表
     */
    fun getListByOpenId(openId: String): List<WanbaoUserAuthInfo> {
        if (StrUtil.isEmpty(openId)) {
            return emptyList()
        }
        val wrapper = Wrappers.lambdaQuery<WanbaoUserAuthInfo>()
            .eq(WanbaoUserAuthInfo::getOpenId, openId)
        return list(wrapper)
    }

    /**
     * 根据用户ID获取列表
     */
    fun getListByUserId(userId: Long): List<WanbaoUserAuthInfo> {
        val wrapper = Wrappers.lambdaQuery<WanbaoUserAuthInfo>()
            .eq(WanbaoUserAuthInfo::getUserId, userId)
        return list(wrapper)
    }

    /**
     * 根据腕宝用户ID获取
     */
    fun getOneByOpenIdOrNull(openId: String): WanbaoUserAuthInfo? {
        val wrapper = Wrappers.lambdaQuery<WanbaoUserAuthInfo>()
            .eq(WanbaoUserAuthInfo::getOpenId, openId)
        val authInfo = getOne(wrapper)
        if (authInfo == null) {
            log.warn("[getOneByOpenId] openId = $openId not found")
            // throwError(ServiceException(UploadErrorCode.COROS_USER_AUTH_INFO_NOT_FOUND))
        }
        return authInfo
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserId(userId: Long): WanbaoUserAuthInfo {
        val wrapper = Wrappers.lambdaQuery<WanbaoUserAuthInfo>()
            .eq(WanbaoUserAuthInfo::getUserId, userId)
        val authInfo = getOne(wrapper)
        if (authInfo == null) {
            log.error("[getOneByUserId] userId = $userId not found")
            throwError(ServiceException(UploadErrorCode.WANBAO_USER_AUTH_INFO_NOT_FOUND))
        }
        return authInfo!!
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserIdOrNull(userId: Long): WanbaoUserAuthInfo? {
        val wrapper = Wrappers.lambdaQuery<WanbaoUserAuthInfo>()
            .eq(WanbaoUserAuthInfo::getUserId, userId)
        return getOne(wrapper)
    }

}
