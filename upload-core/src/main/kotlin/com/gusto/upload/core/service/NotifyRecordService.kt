package com.gusto.upload.core.service

import cn.hutool.core.util.StrUtil
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.gusto.upload.core.dao.NotifyRecordDao
import com.gusto.upload.model.entity.NotifyRecord
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @since 2024-05-27
 */
@Service
class NotifyRecordService : ServiceImpl<NotifyRecordDao, NotifyRecord>() {

    /**
     * 根据唯一键删除列表
     */
    fun removeByUniqueKey(uniqueKey: String) {
        if (StrUtil.isEmpty(uniqueKey)) return
        lambdaUpdate()
            .eq(NotifyRecord::getUniqueKey, uniqueKey)
            .remove()
    }

    /**
     * 根据唯一键删除列表
     */
    fun getOneByUniqueKeyOrNull(uniqueKey: String): NotifyRecord? {
        if (StrUtil.isEmpty(uniqueKey)) return null
        return lambdaQuery()
            .eq(NotifyRecord::getUniqueKey, uniqueKey)
            .one()
    }

}
