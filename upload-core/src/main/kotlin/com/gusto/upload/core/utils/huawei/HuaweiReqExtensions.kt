package com.gusto.upload.core.utils.huawei

import com.gusto.upload.model.entity.huawei.request.HuaweiNotificationReq
import com.gusto.upload.model.entity.message.HuaweiActivityMessage

fun HuaweiNotificationReq.toMessageOrNull(userId: Long): HuaweiActivityMessage? {
    val metaDataMap = metaData.associateBy { it.metaKey }
    val message = HuaweiActivityMessage()
    message.startTime = metaDataMap["startTime"]?.metaValue ?: return null
    message.endTime = metaDataMap["endTime"]?.metaValue ?: return null
    message.activityType = metaDataMap["activityType"]?.metaValue?.toInt() ?: return null
    message.userId = userId
    return message
}