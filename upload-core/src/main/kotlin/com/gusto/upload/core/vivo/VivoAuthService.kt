package com.gusto.upload.core.vivo

import cn.hutool.json.JSONUtil
import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.model.entity.vivo.VivoUserAuthInfo
import com.gusto.upload.model.entity.vivo.req.VivoGetAccessTokenReq
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.Instant

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Service
class VivoAuthService : VivoCommonService() {

    private val log = LoggerFactory.getLogger(javaClass)

    /**
     * 授权回调
     */
    fun upsertAccessTokenByCode(code: String, userId: Long): Boolean {
        val req = VivoGetAccessTokenReq().apply {
            this.code = code
        }
        val reqJson = JSONUtil.toJsonStr(req)
        val rsp = vivoClient.getAccessToken(
            req = req,
            headers = buildHeaders(null, reqJson)
        )
        if (rsp.isError) {
            log.error("[upsertAccessTokenByCode] get AT error, error = ${rsp.content}")
            return false
        }
        if (rsp.result.code != 200) {
            log.error("[upsertAccessTokenByCode] get AT error, error = ${rsp.result.formatToJson()}")
            return false
        }
        val rspData = rsp.result.data
        val authInfo = VivoUserAuthInfo().apply {
            this.userId = userId
            refreshToken = rspData.refreshToken
            refreshTokenExpireTime = Instant.ofEpochMilli(rspData.refreshTokenExpireTime)
            accessToken = rspData.accessToken
            accessTokenExpireTime = Instant.ofEpochMilli(rspData.accessTokenExpireTime)
            openId = rspData.healthOpenId
        }

        val userInfoRsp = vivoClient.getUserInfo(Any(), buildHeaders(authInfo.accessToken, null))
        if (userInfoRsp.isSuccess) {
            authInfo.nickname = userInfoRsp.result.data.firstOrNull()?.nickName ?: ""
        }

        // 查询此VIVO账号是否绑定了多个第一赛道账号
        val dbAuthInfoListByOpenId = authInfoService.getListByOpenId(authInfo.openId)
        if (dbAuthInfoListByOpenId.isNotEmpty()) {
            dbAuthInfoListByOpenId.forEach {
                // 踢掉此VIVO账号的其他第一赛道账号授权，不用请求接口，因为VIVO是根据openId推送运动记录的
                authInfoService.removeById(it)
            }
        }
        // 查询此第一赛道是否绑定了多个VIVO账号
        val dbAuthInfoListByUserId = authInfoService.getListByUserId(authInfo.userId)
        if (dbAuthInfoListByUserId.isNotEmpty()) {
            dbAuthInfoListByUserId.forEach {
                // 踢掉此VIVO账号的其他第一赛道账号授权，不用请求接口，因为VIVO是根据openId推送运动记录的
                authInfoService.removeById(it)
            }
        }
        // 插入新的授权信息
        authInfoService.save(authInfo)
        return true
    }

    /**
     * 根据用户ID取消授权
     */
    fun cancelAuthByUserId(userId: Long): Boolean {
        val authInfo = authInfoService.getOneByUserId(userId)
        checkOrRefreshAccessToken(authInfo)
        val rsp = vivoClient.cancelAuth(Any(), buildHeaders(authInfo.accessToken, null))
        if (rsp.isError) {
            log.error("[cancelAuthByUserId] cancel error, error = ${rsp.content}")
            return false
        }
        authInfoService.removeById(authInfo)
        if (rsp.result.code != 200) {
            log.error("[cancelAuthByUserId] cancel error, rsp = ${rsp.result.formatToJson()}")
//            return false
        }
        return true
    }

}
