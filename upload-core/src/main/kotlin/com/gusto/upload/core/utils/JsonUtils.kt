package com.gusto.upload.core.utils

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.gusto.framework.core.util.json.sharedObjectMapper

/**
 * Json工具类
 *
 * <AUTHOR>
 * @since 2021-11-04
 */
object JsonUtils {
    /**
     * 构造Jackson的Json序列化工具
     *
     * @param lowerCamelCase 是否首字母小写驼峰，false则为小写下划线
     */
    fun initObjectMapper(lowerCamelCase: Boolean = false): ObjectMapper {
        val objectMapper = sharedObjectMapper
        return if (lowerCamelCase) {
            sharedObjectMapper.propertyNamingStrategy = PropertyNamingStrategies.LOWER_CAMEL_CASE
            objectMapper
        } else {
            objectMapper
        }
    }
}