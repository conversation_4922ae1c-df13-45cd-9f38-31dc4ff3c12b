package com.gusto.upload.core.utils.honor

import com.gusto.upload.model.entity.honor.req.HonorNotificationReq
import com.gusto.upload.model.entity.message.HonorActivityMessage

fun HonorNotificationReq.toMessageOrNull(userId: Long): HonorActivityMessage {
    val message = HonorActivityMessage()
    message.startTime = startTime
    message.dataType = datatype
    message.userId = userId
    return message
}