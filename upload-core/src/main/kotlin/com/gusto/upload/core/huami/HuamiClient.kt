package com.gusto.upload.core.huami

import com.dtflys.forest.annotation.Body
import com.dtflys.forest.annotation.Get
import com.dtflys.forest.annotation.Header
import com.dtflys.forest.annotation.Post
import com.dtflys.forest.annotation.Query
import com.dtflys.forest.annotation.Retry
import com.dtflys.forest.http.ForestResponse
import com.gusto.upload.model.entity.huami.rsp.HuamiGetAccessTokenRsp
import com.gusto.upload.model.entity.huami.rsp.HuamiGetActivityDetailFileRsp
import com.gusto.upload.model.entity.huami.rsp.HuamiGetActivityDetailRsp
import com.gusto.upload.model.entity.huami.rsp.HuamiGetActivityListRsp
import com.gusto.upload.model.entity.huami.rsp.HuamiGetUserInfoRsp

/**
 * 华米-开放平台
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Retry(maxRetryCount = "5", maxRetryInterval = "100")
interface HuamiClient {

    /**
     * 使用授权码Code获取AT
     */
    @Post("https://auth.huami.com/v2/oauth2/access_token")
    fun getAccessToken(
        @Body("client_id") clientId: String,
        @Body("redirect_uri") redirectUri: String,
        @Body("code") code: String,
        @Body("client_secret") clientSecret: String,
        @Body("grant_type") grantType: String = "authorization_code"
    ): ForestResponse<HuamiGetAccessTokenRsp>

    /**
     * 通过RT刷新AT
     */
    @Post("https://auth.huami.com/v2/oauth2/refresh_token")
    fun refreshAccessToken(
        @Header("Authorization") authorization: String,
        @Body("client_id") clientId: String,
        @Body("client_secret") clientSecret: String,
        @Body("grant_type") grantType: String = "refresh_token"
    ): ForestResponse<HuamiGetAccessTokenRsp>

    /**
     * 获取用户信息
     */
    @Get("https://api-open.zepp.com/users/-/profile")
    fun getUserInfo(
        @Header("Authorization") authorization: String
    ): ForestResponse<HuamiGetUserInfoRsp>

    /**
     * 获取指定日期区间的活动列表
     */
    @Get("https://api-open.zepp.com/users/-/sports")
    fun getActivityListByDate(
        @Header("Authorization") authorization: String,
        @Query("startDate") startDate: String,
        @Query("endDate") endDate: String,
        @Query("sportCategory") sportCategory: String = "all"
    ): ForestResponse<HuamiGetActivityListRsp>

    /**
     * 获取运动详情
     */
    @Get("https://api-open.zepp.com/users/-/sportDetail")
    fun getActivityDetail(
        @Header("Authorization") authorization: String,
        @Query("trackId") trackId: String
    ): ForestResponse<HuamiGetActivityDetailRsp>

    /**
     * 获取运动详情文件链接
     */
    @Get("https://api-open.zepp.com/users/-/sportDetailFile")
    fun getActivityDetailFile(
        @Header("Authorization") authorization: String,
        @Query("userId") userId: String,
        @Query("params") params: String,
        @Query("fileType") fileType: String = "fit"
    ): ForestResponse<HuamiGetActivityDetailFileRsp>

}
