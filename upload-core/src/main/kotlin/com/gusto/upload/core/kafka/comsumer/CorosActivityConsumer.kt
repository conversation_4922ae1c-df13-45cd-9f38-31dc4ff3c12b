package com.gusto.upload.core.kafka.comsumer

import com.alicp.jetcache.Cache
import com.alicp.jetcache.anno.CreateCache
import com.gusto.upload.core.coros.CorosActivityService
import com.gusto.upload.core.service.NotifyRecordService
import com.gusto.upload.core.service.user.NewUserRunService
import com.gusto.upload.core.utils.roundTwo
import com.gusto.upload.model.entity.message.CorosActivityMessage
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component
import java.time.Instant

/**
 * 高驰-活动
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
@Component
class CorosActivityConsumer {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var activityService: CorosActivityService

    @Autowired
    lateinit var notifyRecordService: NotifyRecordService

    @Autowired
    lateinit var runService: NewUserRunService

    @CreateCache(name = "CorosActivityConsumer.upsertUserRunCache.", expire = 60 * 3)
    private lateinit var upsertUserRunCache: Cache<String, String>

    /**
     * 处理订阅事件
     */
    @KafkaListener(
        topics = [CorosActivityMessage.NOTIFY_ACTIVITY_TOPIC],
        groupId = CorosActivityMessage.GROUP + "#UpsertUserRun"
    )
    fun upsertUserRun(message: CorosActivityMessage) {
        log.info("[upsertUserRun] get message = {}", message)
        val cache = upsertUserRunCache.GET(message.buildUniqueKey())
        if (cache.isSuccess) {
            log.info("[upsertUserRun] cache exist, return")
            checkExist(message)
            return
        } else {
            upsertUserRunCache.PUT(message.buildUniqueKey(), Instant.now().toEpochMilli().toString())
        }
        if (checkExist(message)) return

        val runtime = Runtime.getRuntime()
        val totalMemory = runtime.totalMemory() / 1024 / 1024 // 总内存
        val maxMemory = runtime.maxMemory() / 1024 / 1024 // 最大内存
        val freeMemory = runtime.freeMemory() / 1024 / 1024 // 可用内存
        val usedMemory = totalMemory - freeMemory // 已使用内存
        // 内存使用率
        val memoryRate = (usedMemory.toDouble() / maxMemory.toDouble() * 100).roundTwo()
        log.debug("[upsertUserRun] 总内存 = $maxMemory MB | 最大内存 = $totalMemory MB | 已使用内存 = $usedMemory MB | 可用内存 = $freeMemory MB | 内存使用率 =  $memoryRate%")

        activityService.syncActivityBySubscription(message.activity)
        notifyRecordService.removeByUniqueKey(message.buildUniqueKey())
        upsertUserRunCache.REMOVE(message.buildUniqueKey())
        log.info("[upsertUserRun] handle success")
    }

    private fun checkExist(message: CorosActivityMessage): Boolean {
        val dbRun =
            runService.getListByUserIdAndRunId(message.userId, message.activity.startTime.toLong() * 1000)
                .firstOrNull { it.deviceType == 303 }
        if (dbRun != null) {
            notifyRecordService.removeByUniqueKey(message.buildUniqueKey())
            upsertUserRunCache.REMOVE(message.buildUniqueKey())
            val logPre = "[checkExist] " +
                    "userId=${message.userId}, " +
                    "deviceId=5, " +
                    "deviceType=303, " +
                    "activityId=${message.activity.startTime.toLong() * 1000}"
            log.debug("$logPre, has been inserted into the database, return false")
            return true
        } else {
            return false
        }
    }

}
