package com.gusto.upload.core.huami

import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwError
import com.gusto.framework.core.util.time.plusDays
import com.gusto.upload.core.qiniu.QiniuService
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.huami.HuamiProperties
import com.gusto.upload.model.entity.huami.HuamiUserAuthInfo
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.concurrent.Executor
import javax.annotation.Resource

/**
 * 华米-通用
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
class HuamiCommonService {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authInfoService: HuamiUserAuthInfoService

    @Autowired
    lateinit var config: HuamiProperties

    @Autowired
    lateinit var huamiClient: HuamiClient

    @Autowired
    lateinit var qiniuService: QiniuService

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    /**
     * 检查用户AT是否过期，如果过期则刷新
     */
    fun checkOrRefreshAccessToken(authInfo: HuamiUserAuthInfo, fromSub: Boolean = false) {
        val now = Instant.now()
        // AT有效期默认90天
        val expireTime = authInfo.accessTokenUpdateTime.plusDays(85)
        val expire = now.isAfter(expireTime)
        // val expire = true
        if (expire) {
            val result = refreshAccessToken(authInfo)
            if (!result) {
                if (!fromSub) {
                    // 从订阅过来的不删除授权
                    authInfoService.removeById(authInfo)
                    throwError(
                        ServiceException(
                            UploadErrorCode.HUAMI_REFRESH_ACCESS_TOKEN_ERROR.code,
                            "获取授权失败，请返回并重新绑定"
                        )
                    )
                } else {
                    // do nothing
                }
            }
        }
    }

    /**
     * 刷新AT
     */
    private fun refreshAccessToken(authInfo: HuamiUserAuthInfo): Boolean {
        val rsp = huamiClient.refreshAccessToken(
            authorization = buildAuthorization(authInfo.refreshToken),
            clientId = config.clientId,
            clientSecret = config.clientSecret
        )
        if (rsp.isError) {
            log.warn("[refreshAccessToken] refresh AT error, error = ${rsp.content}")
            return false
        }
        authInfo.refreshToken = rsp.result.refreshToken
        authInfo.accessToken = rsp.result.accessToken
        authInfo.accessTokenUpdateTime = Instant.now()
        authInfoService.updateById(authInfo)
        return true
    }

    protected fun buildAuthorization(accessToken: String): String {
        return "Bearer $accessToken"
    }

}
