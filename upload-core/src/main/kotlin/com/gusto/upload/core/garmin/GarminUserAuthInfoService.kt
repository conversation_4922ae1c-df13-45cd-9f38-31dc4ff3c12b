package com.gusto.upload.core.garmin

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
import com.baomidou.mybatisplus.core.toolkit.Wrappers
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwIfNull
import com.gusto.upload.core.dao.garmin.GarminUserAuthInfoDao
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.garmin.GarminUserAuthInfo
import org.springframework.stereotype.Service

/**
 * 佳明-用户授权信息
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Service
class GarminUserAuthInfoService : ServiceImpl<GarminUserAuthInfoDao, GarminUserAuthInfo>() {

    private fun buildQueryWrapper(source: Int): LambdaQueryWrapper<GarminUserAuthInfo> {
        return Wrappers.lambdaQuery<GarminUserAuthInfo>()
            .eq(GarminUserAuthInfo::getSource, source)
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserIdOrNull(userId: Long, source: Int): GarminUserAuthInfo? {
        val wrapper = buildQueryWrapper(source)
            .eq(GarminUserAuthInfo::getUserId, userId)
        return getOne(wrapper)
    }

    /**
     * 根据RequestToken获取
     */
    fun getOneByRequestToken(requestToken: String, source: Int): GarminUserAuthInfo {
        val wrapper = buildQueryWrapper(source)
            .eq(GarminUserAuthInfo::getRequestToken, requestToken)
        val authInfo = getOne(wrapper)
        throwIfNull(authInfo, ServiceException(UploadErrorCode.GARMIN_USER_AUTH_INFO_NOT_FOUND))
        return authInfo
    }

    /**
     * 根据AccessToken获取
     */
    fun getOneByAccessTokenOrNull(accessToken: String, source: Int): GarminUserAuthInfo? {
        val query = buildQueryWrapper(source)
        query.eq(GarminUserAuthInfo::getAccessToken, accessToken)
        return getOne(query)
    }

    /**
     * 根据AccessToken获取
     */
    fun getOneByAccessToken(accessToken: String, source: Int): GarminUserAuthInfo? {
        val wrapper = buildQueryWrapper(source)
            .eq(GarminUserAuthInfo::getAccessToken, accessToken)
        val authInfo = getOne(wrapper)
        if (authInfo == null) {
            log.warn("[getOneByAccessToken] accessToken = $accessToken, source = $source, can not found authInfo")
//            throw ServiceException(UploadErrorCode.GARMIN_USER_AUTH_INFO_NOT_FOUND)
        }
        return authInfo
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserId(userId: Long, source: Int): GarminUserAuthInfo {
        val wrapper = buildQueryWrapper(source)
            .eq(GarminUserAuthInfo::getUserId, userId)
        val authInfo = getOne(wrapper)
        throwIfNull(authInfo, ServiceException(UploadErrorCode.GARMIN_USER_AUTH_INFO_NOT_FOUND))
        return getOne(wrapper)
    }

    /**
     * 根据佳明用户ID获取列表
     */
    fun getListByGarminUserId(garminUserId: String, source: Int): List<GarminUserAuthInfo> {
        val wrapper = buildQueryWrapper(source)
            .eq(GarminUserAuthInfo::getGarminUserId, garminUserId)
        return list(wrapper)
    }

    /**
     * 删除数据
     */
    fun delete(authInfo: GarminUserAuthInfo) {
        removeById(authInfo.recordId)
    }

}
