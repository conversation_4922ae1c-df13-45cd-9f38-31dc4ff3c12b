package com.gusto.upload.core.kafka.producer

import com.gusto.upload.model.entity.message.HonorActivityMessage
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.support.SendResult
import org.springframework.stereotype.Component
import org.springframework.util.concurrent.ListenableFutureCallback

/**
 * 荣耀-活动生产者
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Component
class HonorActivityProducer {
    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var kafkaTemplate: KafkaTemplate<String, Any>

    /**
     * 发送到订阅事件消费队列
     */
    fun sendSubscriptionMessage(message: HonorActivityMessage) {
        val feature = kafkaTemplate.send(HonorActivityMessage.SUBSCRIPTION_TOPIC, message)
        feature.addCallback(object : ListenableFutureCallback<SendResult<String, Any>> {
            override fun onFailure(ex: Throwable) {
                log.error("[sendSubscriptionMessage] [onFailure] message = ${message}, cause = ${ex.message}")
            }

            override fun onSuccess(result: SendResult<String, Any>) {
                log.info(
                    "[sendSubscriptionMessage] [onSuccess] topic = ${HonorActivityMessage.SUBSCRIPTION_TOPIC}, message = ${result.producerRecord.value()}"
                )
            }
        })
    }
}