package com.gusto.upload.core.roozym.consumer

import com.gusto.upload.core.roozym.RoozymActivityService
import com.gusto.upload.model.entity.message.RoozymActivityMessage
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component

/**
 *
 * <AUTHOR>
 * @since 2022/6/1
 */
@Component
class RoozymActivityConsumer {
    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var activityService: RoozymActivityService

    /**
     * 处理订阅事件
     */
    @KafkaListener(
        topics = [RoozymActivityMessage.SUBSCRIPTION_TOPIC],
        groupId = RoozymActivityMessage.SUBSCRIPTION_TOPIC + "#UpsertUserRun"
    )
    fun upsertUserRun(message: RoozymActivityMessage) {
        log.info("[upsertUserRun] get message = {}", message)
        activityService.syncUserActivityRecordListBySubscription(
            message.openId,
            message.startTime,
            message.endTime,
            message.activityType,
            message.eventType
        )
        log.info("[upsertUserRun] handle success")
    }
}
