package com.gusto.upload.core.huawei

import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.isNotNullOrEmpty
import com.gusto.upload.core.utils.huawei.toUserAuthInfo
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.huawei.HuaweiUserAuthInfo
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.net.URLEncoder.encode
import java.time.Instant
import java.util.concurrent.Executor
import javax.annotation.Resource


/**
 * 华为-鉴权 服务类
 *
 * <AUTHOR>
 * @since 2021-10-28
 */
@Service
class HuaweiAuthService : HuaweiService() {
    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var subscriptionService: HuaweiSubscriptionService

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    /**
     * 使用授权码Code获取AT
     * PS: AT一个小时有效
     */
    fun upsertAccessTokenByCode(code: String, userId: Long): Boolean {
        val rsp = huaweiClient.getAccessToken(
            code = encode(code, "UTF-8"),
            clientId = config.clientId,
            clientSecret = config.clientSecret,
            redirectUri = config.redirectUri
        )
        if (rsp.isError) {
            log.error("[upsertAccessTokenByCode] get AT error, error = ${rsp.content}")
            throw ServiceException(UploadErrorCode.HUAWEI_GET_ACCESS_TOKEN_ERROR)
        }
        val authInfo = rsp.result.toUserAuthInfo(userId)
        val avatar = "huawei_avatar_${authInfo.userId}_${Instant.now().epochSecond}.jpg"
        qiniuService.uploadUrl(authInfo.avatar, avatar)
        authInfo.avatar = "https://file.gusto.cn/$avatar"

        // 查询此华为账号是否绑定了多个第一赛道账号
        val dbAuthInfoList = authInfoService.getListByUnionId(authInfo.unionId)
        if (dbAuthInfoList.isNotEmpty()) {
            dbAuthInfoList.forEach {
                // 踢掉此华为账号的其他第一赛道账号授权，不用请求接口，因为华为是根据openId推送运动记录的
                authInfoService.deleteWithCache(it)
            }
        }

        // 插入新的授权信息
        authInfoService.create(authInfo)

        // 异步新增订阅记录
        asyncExecutor.execute {
            subscriptionService.upsert(authInfo)
        }

        return true
    }

    /**
     * 根据用户ID取消授权
     */
    fun cancelAuthByUserId(userId: Long): Boolean {
        val uncheckAuthInfo = authInfoService.getOneByUserId(userId)
        return cancelAuth(uncheckAuthInfo)
    }

    /**
     * 取消授权
     */
    fun cancelAuth(uncheckAuthInfo: HuaweiUserAuthInfo): Boolean {
        val authInfo = checkOrRefreshAccessToken(uncheckAuthInfo)!!

        // 删除订阅记录
        subscriptionService.deleteAll(authInfo)

        // 取消授权
        val rsp = huaweiClient.cancelAuth(
            clientId = config.clientId,
            headers = getHttpsHeaders(authInfo)
        )
        if (rsp.isError && rsp.statusIsNot(200)) {
            log.error("[cancelAuth] req error, error = ${rsp.content}")
            return false
        }

        // 更新数据库
        authInfoService.deleteWithCache(authInfo)

        return true
    }

    /**
     * 取消授权（被动）
     */
    fun cancelAuthPassive(userId: Long): Boolean {
        val uncheckAuthInfo = authInfoService.getOneByUserId(userId)
        val authInfo = checkOrRefreshAccessToken(uncheckAuthInfo)!!

        try {
            // 删除订阅记录
            subscriptionService.deleteAll(authInfo)
            // 取消授权
            val rsp = huaweiClient.cancelAuth(
                clientId = config.clientId,
                headers = getHttpsHeaders(authInfo)
            )
            if (rsp.isError) {
                log.warn("[cancelAuthPassive] req error, error = ${rsp.content}")
                // throw BusinessException(UploadErrorInfo.HUAWEI_CANCEL_AUTH_ERROR)
            }
        } catch (e: Exception) {
            log.warn("[cancelAuthPassive] req error, already cancel")
        } finally {
            // 更新数据库
            authInfoService.deleteWithCache(authInfo)
        }

        return true
    }

    /**
     * 查询隐私授权状态
     */
    fun getPrivacyAuthStateByUserId(userId: Long, uncheckAuthInfo: HuaweiUserAuthInfo): Int {
        val authInfo = checkOrRefreshAccessToken(uncheckAuthInfo)!!
        val rsp = huaweiClient.getPrivacyAuthState(getHttpsHeaders(authInfo))
        if (rsp.isError && rsp.statusIsNot(200)) {
            log.warn("[getPrivacyAuthStateByUserId] req error, error = ${rsp.content}")
            return 0
        }
        log.debug("[getPrivacyAuthStateByUserId] userId = $userId, ${rsp.result}")
        return rsp.result.firstOrNull()?.get("opinion") ?: 0
    }

    /**
     * 查询用户授予某个应用的权限信息
     */
    fun getScopeListByUserId(userId: Long, uncheckAuthInfo: HuaweiUserAuthInfo): List<String> {
        val authInfo: HuaweiUserAuthInfo
        try {
            authInfo = checkOrRefreshAccessToken(uncheckAuthInfo)!!
        } catch (e: Exception) {
            log.error(e.message)
            return emptyList()
        }
        val rsp = huaweiClient.getScopeByClientId(config.clientId, getHttpsHeaders(authInfo))
        if (rsp.isError && rsp.statusIsNot(200)) {
            log.error("[getScopeListByUserId] req error, error = ${rsp.content}")
            return emptyList()
        }
        return if (rsp.content.isNotNullOrEmpty()) {
            rsp.result.url2Desc.keys.toList()
        } else {
            emptyList()
        }
    }
}
