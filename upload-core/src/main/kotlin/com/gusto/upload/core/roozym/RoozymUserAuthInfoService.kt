package com.gusto.upload.core.roozym

import com.alicp.jetcache.Cache
import com.alicp.jetcache.anno.CreateCache
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwIf
import com.gusto.upload.core.common.CacheKeyPrefix
import com.gusto.upload.core.common.DeleteInterface
import com.gusto.upload.core.dao.RoozymAuthInfoDao
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.roozym.RoozymAuthInfo
import org.springframework.stereotype.Service
import java.time.Instant

/**
 *
 * <AUTHOR>
 * @since 2022/5/26
 */
@Service
class RoozymUserAuthInfoService : ServiceImpl<RoozymAuthInfoDao, RoozymAuthInfo>(),
    DeleteInterface<RoozymAuthInfo> {

    @CreateCache(name = CacheKeyPrefix.ROOZYM_USER_ID_BY_OPEN_ID, expire = 7 * 24 * 3600)
    lateinit var userIdCache: Cache<String, Long>

    /**
     * 根据unionId获取列表
     */
    fun getListByOpenId(openId: String): List<RoozymAuthInfo> {
        val query = createNotDeletedQueryWrapper()
        query.eq(RoozymAuthInfo.OPEN_ID_FIELD, openId)
        return list(query)
    }

    /**
     * 删除并清除缓存
     */
    fun deleteWithCache(authInfo: RoozymAuthInfo) {
        val updateWrapper = UpdateWrapper<RoozymAuthInfo>()
        updateWrapper.set(RoozymAuthInfo.DELETED_FIELD, true)
        updateWrapper.eq(RoozymAuthInfo.USER_ID_FIELD, authInfo.userId)
        update(updateWrapper)
        userIdCache.REMOVE(authInfo.openId)
    }

    /**
     * 根据用户ID获取（不过滤已删除的）
     */
    fun getOneByUserIdOrNullWithoutFilter(userId: Long, openId: String): RoozymAuthInfo? {
        val query = QueryWrapper<RoozymAuthInfo>()
        query.eq(RoozymAuthInfo.USER_ID_FIELD, userId)
        query.eq(RoozymAuthInfo.OPEN_ID_FIELD, openId)
        return getOne(query)
    }

    /**
     * 根据用户ID获取（过滤已删除的）
     */
    fun getOneByUserIdOrNull(userId: Long): RoozymAuthInfo? {
        val query = QueryWrapper<RoozymAuthInfo>()
        query.eq(RoozymAuthInfo.USER_ID_FIELD, userId)
        query.eq(RoozymAuthInfo.DELETED_FIELD, false)
        return getOne(query)
    }

    /**
     * 插入并添加缓存
     */
    fun create(authInfo: RoozymAuthInfo): RoozymAuthInfo {
        authInfo.accessTokenUpdateTime = Instant.now()
        authInfo.refreshTokenExpires = false
        authInfo.deleted = false
        save(authInfo)
        return authInfo
    }

    /**
     * 更新并清除缓存
     */
    fun updateWithCache(authInfo: RoozymAuthInfo) {
        authInfo.updateTime = Instant.now()
        updateById(authInfo)
        userIdCache.REMOVE(authInfo.openId)
    }

    /**
     * 根据第三方标识获取
     */
    fun getOneByOpenId(openId: String?): RoozymAuthInfo? {
        val query = createNotDeletedQueryWrapper()
        query.eq(RoozymAuthInfo.OPEN_ID_FIELD, openId)
        return getOne(query)
    }

    /**
     * 根据userId获取授权信息
     */
    fun getOneByUserId(userId: Long?): RoozymAuthInfo {
        val query = createNotDeletedQueryWrapper()
        query.eq(RoozymAuthInfo.USER_ID_FIELD, userId)
        val authInfo = getOne(query)
        throwIf(authInfo == null, ServiceException(UploadErrorCode.ROOZYM_USER_AUTH_INFO_NOT_FOUND))
        return authInfo
    }
}
