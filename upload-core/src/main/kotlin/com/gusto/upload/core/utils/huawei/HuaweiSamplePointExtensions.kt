package com.gusto.upload.core.utils.huawei

import com.gusto.upload.model.entity.huawei.HuaweiSamplePoint
import com.gusto.upload.model.entity.user.old.UserRunDetailAltitudeItemTemp
import com.gusto.upload.model.entity.user.old.UserRunDetailHeartRateItemTemp
import com.gusto.upload.model.entity.user.old.UserRunDetailLocationItemTemp
import com.gusto.upload.model.entity.user.old.UserRunDetailSpeedItemTemp
import com.gusto.upload.model.entity.user.old.UserRunDetailStepItemTemp

/**
 * 将华为采样点转化为轨迹明细
 */
fun HuaweiSamplePoint.toLocationOrNull(): UserRunDetailLocationItemTemp? {
    val location = UserRunDetailLocationItemTemp()
    location.isTooFast = false
    location.runindex = 1
    location.time = this.startTime
    this.value.forEach {
        when (it.fieldName) {
            "latitude" -> location.latitude = it.floatValue
            "longitude" -> location.longitude = it.floatValue
            "altitude" -> location.altitude = it.floatValue
        }
    }
    if (location.latitude.toString().length <= 5) return null
    if (location.longitude.toString().length <= 5) return null
    return location
}

/**
 * 将华为采样点转化为心率明细
 */
fun HuaweiSamplePoint.toHeartRate(): UserRunDetailHeartRateItemTemp {
    val heartRate = UserRunDetailHeartRateItemTemp()
    this.value.forEach {
        when (it.fieldName) {
            "bpm" -> heartRate.heartRate = it.floatValue.toInt()
        }
    }
    return heartRate
}

/**
 * 将华为采样点转化为配速明细
 */
fun HuaweiSamplePoint.toSpeed(): UserRunDetailSpeedItemTemp {
    val speed = UserRunDetailSpeedItemTemp()
    this.value.forEach {
        when (it.fieldName) {
            "speed" -> speed.curSpeed = it.floatValue
        }
    }
    return speed
}

/**
 * 将华为采样点转化为步频明细
 */
fun HuaweiSamplePoint.toStep(): UserRunDetailStepItemTemp {
    val step = UserRunDetailStepItemTemp()
    this.value.forEach {
        when (it.fieldName) {
            "step_rate" -> step.stepfrequency = it.floatValue
        }
    }
    return step
}

/**
 * 将华为采样点转化为海拔明细
 */
fun HuaweiSamplePoint.toAltitude(): UserRunDetailAltitudeItemTemp {
    val altitude = UserRunDetailAltitudeItemTemp()
    this.value.forEach {
        when (it.fieldName) {
            "altitude" -> altitude.altitude = it.floatValue
        }
    }
    return altitude
}
