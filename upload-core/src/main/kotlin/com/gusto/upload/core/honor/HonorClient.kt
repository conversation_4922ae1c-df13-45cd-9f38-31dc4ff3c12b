package com.gusto.upload.core.honor

import com.dtflys.forest.annotation.Body
import com.dtflys.forest.annotation.JSONBody
import com.dtflys.forest.annotation.LogEnabled
import com.dtflys.forest.annotation.Post
import com.dtflys.forest.annotation.Retry
import com.dtflys.forest.http.ForestResponse
import com.gusto.upload.model.entity.honor.HonorAccessToken
import com.gusto.upload.model.entity.honor.HonorAccessTokenDetail
import com.gusto.upload.model.entity.honor.req.HonorGetActivityRecordListReq
import com.gusto.upload.model.entity.honor.rsp.HonorCommonRsp
import com.gusto.upload.model.entity.honor.rsp.HonorGetActivityRecordListRsp

/**
 * 荣耀-REST API调用入口
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Retry(maxRetryCount = "0", maxRetryInterval = "0")
interface HonorClient {

    /**
     * 使用授权码Code获取AT
     */
    @Post("https://hnoauth-login.cloud.honor.com/oauth2/v3/token")
    fun getAccessToken(
        @Body("grant_type") grantType: String = "authorization_code",
        @Body("code") code: String,
        @Body("redirect_uri") redirectUri: String,
        @Body("client_id") clientId: String,
        @Body("client_secret") clientSecret: String
    ): ForestResponse<HonorAccessToken>

    /**
     * 解析Access Token
     */
    @Post("https://hnoauth-login.cloud.honor.com/rest.php?nsp_svc=hihonor.oauth2.user.getTokenInfo")
    fun parseAccessToken(
        @Body("access_token") accessToken: String,
        @Body("open_id") grantType: String = "OPENID"
    ): ForestResponse<HonorAccessTokenDetail>

    /**
     * 通过RT刷新AT
     */
    @Post("https://hnoauth-login.cloud.honor.com/oauth2/v3/token")
    fun refreshAccessToken(
        @Body("grant_type") grantType: String = "refresh_token",
        @Body("refresh_token") refreshToken: String,
        @Body("client_id") clientId: String,
        @Body("client_secret") clientSecret: String
    ): ForestResponse<HonorAccessToken>

    /**
     * 取消授权
     */
    @Post("https://hnoauth-login.cloud.honor.com/oauth2/v3/revoke")
    fun cancelAuth(
        @Body("token") accessToken: String,
    ): ForestResponse<HonorCommonRsp<Any>>

    /**
     * 获取运动记录
     */
    @Post("https://health-api.cloud.hihonor.com/health/data/bridge/open/sportRecord")
    @LogEnabled(logResponseContent = false)
    fun getActivityRecordList(
        @JSONBody req: HonorGetActivityRecordListReq
    ): ForestResponse<HonorCommonRsp<HonorGetActivityRecordListRsp>>

}
