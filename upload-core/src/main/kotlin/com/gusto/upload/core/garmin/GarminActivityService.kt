package com.gusto.upload.core.garmin

import cn.hutool.core.util.StrUtil
import com.gusto.upload.core.application.user.NewUserRunApplicationService
import com.gusto.upload.core.common.PushService
import com.gusto.upload.core.kafka.producer.GarminActivityProducer
import com.gusto.upload.core.qiniu.QiniuService
import com.gusto.upload.core.service.NotifyRecordService
import com.gusto.upload.core.service.garmin.GarminUploadNotifyService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.util.NewUserRunMapUtils
import com.gusto.upload.core.util.formatDateHourMinute
import com.gusto.upload.core.utils.JsonUtils
import com.gusto.upload.core.utils.run.roundTwo
import com.gusto.upload.model.entity.garmin.GarminUserAuthInfo
import com.gusto.upload.model.entity.garmin.rsp.GarminActivityDetailRsp
import com.gusto.upload.model.entity.garmin.rsp.GarminActivityDetailSampleRsp
import com.gusto.upload.model.entity.message.GarminActivityMessage
import com.gusto.upload.model.entity.response.ManualSyncRsp
import com.gusto.upload.model.entity.user.NewUserRun
import com.gusto.upload.model.entity.user.NewUserRunDetailAltitudeItem
import com.gusto.upload.model.entity.user.NewUserRunDetailHeartRateItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLapItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLocationItem
import com.gusto.upload.model.entity.user.NewUserRunDetailSpeedItem
import com.gusto.upload.model.entity.user.NewUserRunDetailStepRateItem
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import kotlin.math.roundToInt

/**
 * 佳明-活动
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Service
class GarminActivityService : GarminCommonService() {

    private val log = LoggerFactory.getLogger(javaClass)
    private val objectMapper = JsonUtils.initObjectMapper(lowerCamelCase = true)
    private val halfMarathon = 21.0975
    private val marathon = 42.195

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var newRunApplicationService: NewUserRunApplicationService

    @Autowired
    lateinit var qiniuService: QiniuService

    @Autowired
    lateinit var pushService: PushService

    @Autowired
    lateinit var producer: GarminActivityProducer

    @Autowired
    lateinit var mapUtils: NewUserRunMapUtils

    @Autowired
    lateinit var uploadNotifyService: GarminUploadNotifyService

    @Autowired
    lateinit var notifyRecordService: NotifyRecordService

    /**
     * 根据订阅事件同步活动列表
     */
    fun syncActivityListBySubscription(message: GarminActivityMessage) {
        val accessToken = message.accessToken
        val startTime = message.startTime
        val endTime = message.endTime
        val source = message.source
        val notifyRecord = uploadNotifyService.getOneOrNull(accessToken, startTime, source)
        if (notifyRecord != null && notifyRecord.state == 2) {
            log.debug("[syncActivityListBySubscription-$source] already success")
            return
        }
        val authInfo = authInfoService.getOneByAccessToken(accessToken, source) ?: return
        if (StrUtil.isEmpty(authInfo.garminUserId)) {
            fillGarminUserId(authInfo)
        }
        val rsp = getActivityDetailList(authInfo, startTime, endTime)
        if (rsp.isSuccess) {
            upsertUploadNotifyForSuccess(authInfo, startTime, endTime)
            // 创建跑步记录头部和明细
            val uploadCacheKey = message.buildUniqueKey()
            createUserRunAndDetail(rsp.result, authInfo.userId, message.source, uploadCacheKey)
        } else {
            if (StrUtil.isNotEmpty(rsp.content)) {
                log.error("[syncActivityListBySubscription-$source] userId = ${authInfo.userId}, sync error, message = ${rsp.content}")
            } else {
                log.error("[syncActivityListBySubscription-$source] userId = ${authInfo.userId}, sync error, timeout = ${rsp.isTimeout} ,exception = ${rsp.exception.message}")
                upsertUploadNotifyForError(authInfo, startTime, endTime)
            }
        }
    }

    /**
     * 插入或更新成功的上传通知
     */
    private fun upsertUploadNotifyForSuccess(authInfo: GarminUserAuthInfo, startTime: Long, endTime: Long) {
        upsertUploadNotify(authInfo, startTime, endTime, 2)
    }

    /**
     * 插入或更新失败的上传通知
     */
    private fun upsertUploadNotifyForError(authInfo: GarminUserAuthInfo, startTime: Long, endTime: Long) {
        upsertUploadNotify(authInfo, startTime, endTime, 1)
    }

    /**
     * 插入上传通知
     */
    private fun upsertUploadNotify(authInfo: GarminUserAuthInfo, startTime: Long, endTime: Long, state: Int) {
        val dbUploadNotify =
            uploadNotifyService.getOneOrNull(authInfo.accessToken, startTime, authInfo.source)
        if (dbUploadNotify != null) {
            dbUploadNotify.state = state
            if (state == 2) {
                uploadNotifyService.removeById(dbUploadNotify)
            } else {
                uploadNotifyService.updateById(dbUploadNotify)
            }
        } else {
            if (state == 1) {
                uploadNotifyService.create(authInfo, authInfo.accessToken, startTime, endTime)
            }
        }
    }

    /**
     * 填充佳明用户ID
     */
    private fun fillGarminUserId(authInfo: GarminUserAuthInfo) {
        asyncExecutor.execute {
            val userIdRsp = getGarminUserId(authInfo)
            if (userIdRsp.isSuccess) {
                authInfo.garminUserId = userIdRsp.result.userId
                authInfoService.updateById(authInfo)
            }
        }
    }

    /**
     * 手动同步活动列表
     */
    fun syncActivityListByManual(
        userId: Long,
        source: Int,
        reqStartTime: Instant? = null,
        reqEndTime: Instant? = null
    ): ManualSyncRsp {
        val authInfo = authInfoService.getOneByUserId(userId, source)
        if (StrUtil.isEmpty(authInfo.garminUserId)) {
            fillGarminUserId(authInfo)
        }
        val outRsp = ManualSyncRsp()
        outRsp.result = true
        outRsp.reason = "同步成功，请稍后在跑步记录页查看"
        val currentTime = Instant.now()
        val startTime = reqStartTime?.epochSecond
            ?: currentTime.minusSeconds(86400).epochSecond
        val endTime = reqEndTime?.epochSecond
            ?: currentTime.epochSecond
        log.debug(
            "[syncActivityListByManual-${authInfo.source}] userId = $userId, ${
                Instant.ofEpochSecond(startTime).formatDateHourMinute()
            }"
        )
        val rsp = getActivityDetailList(authInfo, startTime, endTime)
        if (rsp.isSuccess) {
            upsertUploadNotifyForSuccess(authInfo, startTime, endTime)
            if (rsp.result.isEmpty()) {
                log.info("[syncActivityListByManual-${authInfo.source}] userId = $userId, sync async start")
                outRsp.reason = "暂无运动记录可同步"
            } else {
                // 创建跑步记录头部和明细
                asyncExecutor.execute {
                    log.info("[syncActivityListByManual-${authInfo.source}] userId = $userId, sync async start")
                    val uploadCacheKey = "${startTime * 1000}-${endTime * 1000}-${authInfo.userId}"
                    createUserRunAndDetail(rsp.result, userId, source, uploadCacheKey)
                    log.info("[syncActivityListByManual] userId = $userId, sync async success")
                }
            }
        } else {
            if (StrUtil.isNotEmpty(rsp.content)) {
                log.error("[syncActivityListByManual-${authInfo.source}] userId = ${authInfo.userId}, sync error, message = ${rsp.content}")
            } else {
                log.error("[syncActivityListByManual-${authInfo.source}] userId = ${authInfo.userId}, sync error, timeout = ${rsp.isTimeout}, exception = ${rsp.exception.message}")
                upsertUploadNotifyForError(authInfo, startTime, endTime)
            }
            outRsp.result = false
            outRsp.reason = "同步失败，请稍后再试"
        }
        return outRsp
    }

    /**
     * 创建跑步记录并上传到APP
     */
    private fun createUserRunAndDetail(
        activityDetailList: List<GarminActivityDetailRsp>,
        userId: Long,
        source: Int,
        uploadCacheKey: String
    ) {
        var syncSuccessCount = 0
        val runList = mutableListOf<NewUserRun>()
        activityDetailList.forEach {
            val run = buildNewUserRun(it, userId, source)
            val createSuccess = newRunApplicationService.handlerAndCreate(run)
            notifyRecordService.removeByUniqueKey(uploadCacheKey)
            if (!createSuccess) return@forEach
            syncSuccessCount++
            getPolymerizeSampleSet(it, run)
            runList.add(run)
        }
        log.info("[createUserRunAndDetail] userId = $userId, sync success, count = $syncSuccessCount")
        if (syncSuccessCount == 0) return
        asyncExecutor.execute {
            val latestRun = runList.maxByOrNull { it.startTime }
            if (latestRun != null) {
                pushService.notifySyncSuccess(latestRun, syncSuccessCount, userId)
            }
        }
    }

    /**
     * 创建新跑步记录
     */
    private fun buildNewUserRun(rsp: GarminActivityDetailRsp, userId: Long, source: Int): NewUserRun {
        val summary = rsp.summary
        val startTimeTimestamp = summary.startTimeInSeconds * 1000
        val run = NewUserRun().apply {
            this.userId = userId
            deviceId = 3
            deviceType = if (source == 1) {
                301
            } else {
                307
            }
            deviceModel = summary.deviceName
            activityType = formatActivityType(summary.activityType)
            startTime = startTimeTimestamp
            endTime = startTimeTimestamp + summary.durationInSeconds * 1000
            totalDistance = summary.distanceInMeters
            totalDuration = summary.durationInSeconds
//            totalStep = summary.steps
            totalCalorie = summary.activeKilocalories.toDouble()
            averagePace =
                if (summary.averageSpeedInMetersPerSecond != 0.0) 1000 / summary.averageSpeedInMetersPerSecond else 0.0
            maxPace =
                if (summary.maxSpeedInMetersPerSecond != 0.0) 1000 / summary.maxSpeedInMetersPerSecond else 0.0
            averageHeartRate = summary.averageHeartRateInBeatsPerMinute
            maxHeartRate = summary.maxHeartRateInBeatsPerMinute.roundToInt()
            averageStepRate = summary.averageRunCadenceInStepsPerMinute.roundToInt()
            maxStepRate = summary.maxRunCadenceInStepsPerMinute.roundToInt()
            // 步数=分钟数*平均步频
            totalStep = (totalDuration / 60.0 * averageStepRate).roundToInt()
            averageStride =
                if (totalStep != 0) totalDistance * 100 / totalStep else 0.0
            minAltitude = rsp.samples.minOfOrNull { it.elevationInMeters } ?: 0.0
            maxAltitude = rsp.samples.maxOfOrNull { it.elevationInMeters } ?: 0.0
            totalAscent = summary.totalElevationGainInMeters
            totalDescent = summary.totalElevationLossInMeters
            weather = ""
            province = ""
            city = ""
            temperature = ""
            humidity = ""
            thirdActivityId = rsp.summaryId
            thirdActivityType = summary.activityType
            abnormal = 10
            externalSportType = summary.activityType
            mapTrackImage = ""
            trackImage = ""
            val partTimeKmMap = mutableMapOf<String, Int>()
            var halfMarathonFlag = false
            var marathonFlag = false
            var currentLap = 1
            rsp.samples
                .sortedBy { it.startTimeInSeconds }
                .forEach {
                    val currentDuration = it.timerDurationInSeconds.toInt()
                    if (!halfMarathonFlag && it.totalDistanceInMeters >= halfMarathon * 1000) {
                        val halfMarathonItem = partTimeKmMap[halfMarathon.toString()]
                        if (halfMarathonItem == null) {
                            partTimeKmMap[halfMarathon.toString()] = currentDuration
                            halfMarathonFlag = true
                        }
                    } else if (!marathonFlag && it.totalDistanceInMeters >= marathon * 1000) {
                        val marathonItem = partTimeKmMap[marathon.toString()]
                        if (marathonItem == null) {
                            partTimeKmMap[marathon.toString()] = currentDuration
                            marathonFlag = true
                        }
                    } else {
                        if (it.totalDistanceInMeters >= 1000) {
                            if (it.totalDistanceInMeters / 1000 >= currentLap) {
                                partTimeKmMap[currentLap.toDouble().toString()] = currentDuration
                                currentLap++
                            }
                        }
                    }
                }
            partTimeKmList = partTimeKmMap.entries
                .sortedBy { it.key.toDouble() }
                .associateBy({ it.key }, { it.value })
        }
        return run
    }

    /**
     * 转换活动类型
     *
     * 运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它
     */
    private fun formatActivityType(activityType: String): Int {
        return when (activityType) {
            "RUNNING",
            "OBSTACLE_RUN",
            "STREET_RUNNING",
            "TRACK_RUNNING",
            "ULTRA_RUN",
            "VIRTUAL_RUN" -> 1

            "INDOOR_RUNNING",
            "TREADMILL_RUNNING" -> 2

            "HIKING",
            "WALKING",
            "CASUAL_WALKING",
            "SPEED_WALKING" -> 3

            "TRAIL_RUNNING",
            "MOUNTAINEERING" -> 4

            "BMX",
            "CYCLING",
            "CYCLOCROSS",
            "DOWNHILL_BIKING",
            "E_BIKE_FITNESS",
            "E_BIKE_MOUNTAIN",
            "GRAVEL_CYCLING",
            "INDOOR_CYCLING",
            "MOUNTAIN_BIKING",
            "RECUMBENT_CYCLING",
            "ROAD_BIKING",
            "TRACK_CYCLING",
            "VIRTUAL_RIDE" -> 5

            "SWIMMING",
            "LAP_SWIMMING",
            "OPEN_WATER_SWIMMING" -> 6

            else -> 100
        }
    }

    /**
     * 创建和上传新跑步记录明细
     */
    private fun getPolymerizeSampleSet(
        activityDetail: GarminActivityDetailRsp,
        run: NewUserRun
    ) {
        val locationList = mutableListOf<NewUserRunDetailLocationItem>()
        val speedList = mutableListOf<NewUserRunDetailSpeedItem>()
        val heartRateList = mutableListOf<NewUserRunDetailHeartRateItem>()
        val stepRateList = mutableListOf<NewUserRunDetailStepRateItem>()
        val altitudeList = mutableListOf<NewUserRunDetailAltitudeItem>()
        activityDetail.samples
            .sortedBy { it.startTimeInSeconds }
            .forEach {
                val currentTime = run.startTime + it.timerDurationInSeconds * 1000
                locationList.add(buildLocation(it, currentTime))
                speedList.add(buildSpeed(it, currentTime))
                heartRateList.add(buildHeartRate(it, currentTime))
                stepRateList.add(buildStepRate(it, currentTime))
                altitudeList.add(buildAltitude(it, currentTime))
            }
        // 获取每圈信息
        val lapMap = mutableMapOf<Int, NewUserRunDetailLapItem>()
        if (activityDetail.laps != null && activityDetail.laps.isNotEmpty()) {
            var currentLap = 1
            var currentTime = 0
            activityDetail.samples
                .sortedBy { it.startTimeInSeconds }
                .forEach {
                    if (it.totalDistanceInMeters >= 1000) {
                        if (it.totalDistanceInMeters / 1000 >= currentLap) {
                            lapMap[currentLap] = buildLap(1000.0, it.timerDurationInSeconds.toInt() - currentTime)
                            currentLap++
                            currentTime = it.timerDurationInSeconds.toInt()
                        }
                    }
                }

            // 最后不足一公里
            val sumDistance = lapMap.values.sumOf { it.distance }
            val sumDuration = lapMap.values.sumOf { it.duration }
            if (run.totalDistance > sumDistance) {
                lapMap[currentLap] = buildLap(run.totalDistance - sumDistance, run.totalDuration - sumDuration)
            }
        }
        val detail = mapUtils.toNewUserRunDetail(run)
        detail.lapList = lapMap.values.toList()
        detail.locationList = locationList
        detail.heartRateList = heartRateList
        detail.stepRateList = stepRateList
        detail.altitudeList = altitudeList
        detail.partTimeKmList = run.partTimeKmList
        detail.roundTwo()

        // 上传到七牛
//        qiniuService.uploadString(
//            objectMapper.writeValueAsString(detail),
//            "${run.trackFile}.gzip"
//        )
//        qiniuService.uploadString(
//            objectMapper.writeValueAsString(activityDetail),
//            "garmin_activity_detail_${run.trackFile}.gzip"
//        )
        qiniuService.uploadObject(
            detail,
            "${run.trackFile}.gzip"
        )
        qiniuService.uploadObject(
            activityDetail,
            "garmin_activity_detail_${run.trackFile}.gzip"
        )
    }

    /**
     * 创建轨迹
     */
    private fun buildLocation(sample: GarminActivityDetailSampleRsp, currentTime: Long): NewUserRunDetailLocationItem {
        val item = NewUserRunDetailLocationItem().apply {
            timestamp = currentTime
            latitude = sample.latitudeInDegree
            longitude = sample.longitudeInDegree
            speed = sample.speedMetersPerSecond
        }
        return item
    }

    /**
     * 创建速度
     */
    private fun buildSpeed(sample: GarminActivityDetailSampleRsp, currentTime: Long): NewUserRunDetailSpeedItem {
        val item = NewUserRunDetailSpeedItem().apply {
            timestamp = currentTime
            speed = sample.speedMetersPerSecond
        }
        return item
    }

    /**
     * 创建心率
     */
    private fun buildHeartRate(
        sample: GarminActivityDetailSampleRsp,
        currentTime: Long
    ): NewUserRunDetailHeartRateItem {
        val item = NewUserRunDetailHeartRateItem().apply {
            timestamp = currentTime
            heartRate = sample.heartrate
        }
        return item
    }

    /**
     * 创建步频
     */
    private fun buildStepRate(sample: GarminActivityDetailSampleRsp, currentTime: Long): NewUserRunDetailStepRateItem {
        val item = NewUserRunDetailStepRateItem().apply {
            timestamp = currentTime
            stepRate = sample.stepsPerMinute.roundToInt()
        }
        return item
    }

    /**
     * 创建海拔
     */
    private fun buildAltitude(sample: GarminActivityDetailSampleRsp, currentTime: Long): NewUserRunDetailAltitudeItem {
        val item = NewUserRunDetailAltitudeItem().apply {
            timestamp = currentTime
            altitude = sample.elevationInMeters
        }
        return item
    }

    /**
     * 创建圈数
     */
    private fun buildLap(currentDistance: Double, currentDuration: Int): NewUserRunDetailLapItem {
        val item = NewUserRunDetailLapItem().apply {
            duration = currentDuration
            distance = currentDistance
        }
        return item
    }

}
