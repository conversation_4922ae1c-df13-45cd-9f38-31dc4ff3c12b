package com.gusto.upload.core.roozym.util

import com.gusto.upload.model.entity.roozym.RoozymAuthInfo
import java.time.Instant

/**
 *
 * <AUTHOR>
 * @since 2022/6/9
 */
/**
 * 检查用户AT是否过期，过期返回true
 */
fun RoozymAuthInfo.isExpires(): Boolean {
    val now = Instant.now()
    // 过期时间 = AT更新时间 + AT有效时长
    val expiresTime = this.accessTokenUpdateTime.plusSeconds(this.expiresIn)
    return now.isAfter(expiresTime)
}
