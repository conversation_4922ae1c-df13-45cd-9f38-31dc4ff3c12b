package com.gusto.upload.core.service.app

import com.alicp.jetcache.Cache
import com.alicp.jetcache.anno.CreateCache
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.gusto.upload.core.dao.app.AppMessageDao
import com.gusto.upload.model.entity.app.AppMessage
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * 用户消息 服务类
 *
 * <AUTHOR>
 * @since 2021-07-08
 */
@Service
class AppMessageService : ServiceImpl<AppMessageDao, AppMessage>() {

    @CreateCache(name = "AppMessageService.huaweiCache.", expire = 3600 * 24 * 7)
    lateinit var huaweiCache: Cache<Long, List<String>>

    private val allScopeList = listOf(
        "查看华为运动健康服务中的距离、海拔和爬高数据",
        "查看华为运动健康服务中的活动数据(如活动打点、锻炼、力量、跑步姿势、骑行等)",
        "查看华为运动健康服务中的用户活动记录",
        "查看华为运动健康服务中的心率数据",
        "查看华为运动健康服务中的位置数据(包括轨迹)",
        "查看华为运动健康服务中的步数",
        "查看华为运动健康服务中的速度"
    )

    /**
     * 通知华为权限不足
     */
    fun notifyHuaweiScopeError(userId: Long, scopeList: List<String>) {
        val title = "华为运动健康服务权限不足"
        val cacheValueList = listOf(title)
        val cache = huaweiCache.GET(userId)
        if (cache.isSuccess) {
            if (cache.value.contains(title)) {
                return
            } else {
                val newList = cache.value.toMutableList()
                newList.add(title)
                huaweiCache.PUT(userId, newList)
            }
        } else {
            huaweiCache.PUT(userId, cacheValueList)
        }
        val msg = AppMessage()
        msg.userId = userId
        msg.typeId = 1L
        msg.createTime = LocalDateTime.now()
        msg.title = "华为运动健康服务权限不足"
        val scopeListString = StringBuilder()
        scopeList.forEach {
            scopeListString.append(it).append("<br>")
        }
        val needScopeList = StringBuilder()
        allScopeList.forEach {
            if (!scopeList.contains(it)) {
                needScopeList.append(it).append("<br>")
            }
        }

        msg.content = """
            您好，华为运动健康服务授权权限不足。<br>
            <br>
            当前授予权限：<br>
            $scopeListString
            <br>
            成功同步的所需权限为：<br>
            $needScopeList
            <br>
            请<b>取消绑定并重新绑定</b>，并在权限选择页面中选择<b>【全选】</b>，以保证同步成功。<br>
        """.trimIndent()
        save(msg)
    }

}
