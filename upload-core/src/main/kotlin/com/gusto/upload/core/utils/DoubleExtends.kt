package com.gusto.upload.core.utils

/**
 * 将秒转换成时分秒
 */
fun Double.formatPaceString(): String {
    val minutes = (this / 60).toInt()
    val seconds = (this - minutes * 60).toInt()
    val secondsString = if (seconds <= 9) "0${seconds}" else seconds.toString()
    return "$minutes'$secondsString''"
}

/**
 * 腕宝配速转换
 */
fun Double.formatPaceForWanBao(activityType: Int): Double {
    if (this == 0.0) return 0.0
    if (activityType != 5) {
        // 将分钟/公里转换为秒/公里
        return (this * 60).roundTwo()
    } else {
        // 将公里/小时转换为秒/公里
        return (3600 / this).roundTwo()
    }
}

/**
 * 腕宝速度转换
 */
fun Double.formatSpeedForWanBao(activityType: Int): Double {
    if (this == 0.0) return 0.0
    if (activityType != 5) {
        // 将分钟/千米转换为米/秒
        return (1000 / (this * 60)).roundTwo()
    } else {
        // 将公里/小时转换为米/秒
        return (this / 3.6).roundTwo()
    }
}

/**
 * 保留两位小数
 */
fun Double.roundTwo(): Double {
    return String.format("%.2f", this).toDouble()
}

/**
 * 保留n位小数
 */
fun Double.round(n: Int): Double {
    return String.format("%.${n}f", this).toDouble()
}