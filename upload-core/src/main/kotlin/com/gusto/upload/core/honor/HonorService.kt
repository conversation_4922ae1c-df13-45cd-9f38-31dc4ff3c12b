package com.gusto.upload.core.honor

import cn.hutool.core.util.StrUtil
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.isNotNullOrEmpty
import com.gusto.upload.core.qiniu.QiniuService
import com.gusto.upload.core.utils.honor.toUserAuthInfo
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.honor.HonorProperties
import com.gusto.upload.model.entity.honor.HonorUserAuthInfo
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import java.net.URLEncoder
import java.time.Instant

/**
 * 荣耀 通用 服务类
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
abstract class HonorService {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var config: HonorProperties

    @Autowired
    lateinit var honorClient: HonorClient

    @Autowired
    lateinit var authInfoService: HonorUserAuthInfoService

    @Autowired
    lateinit var qiniuService: QiniuService

    /**
     * 检查用户授权信息是否过期，如果过期则刷新
     */
    fun checkOrRefreshAccessToken(authInfo: HonorUserAuthInfo, fromSub: Boolean = false): HonorUserAuthInfo? {
        return refreshAccessToken(authInfo, fromSub)
    }

    /**
     * 刷新AT
     */
    private fun refreshAccessToken(authInfo: HonorUserAuthInfo, fromSub: Boolean = false): HonorUserAuthInfo? {
        val rsp = honorClient.refreshAccessToken(
            refreshToken = URLEncoder.encode(authInfo.refreshToken, "UTF-8"),
            clientId = config.clientId,
            clientSecret = config.clientSecret
        )
        if (rsp.isError) {
            log.warn("[refreshAccessToken] refresh AT error, error = ${rsp.content}")
            authInfo.deleted = true
            authInfoService.updateWithCache(authInfo)
            if (fromSub) {
                return null
            } else {
                throw ServiceException(UploadErrorCode.HONOR_REFRESH_ACCESS_TOKEN_ERROR) // TODO 报错后续如何提示用户
            }
        }
        val newAuthInfo = rsp.result.toUserAuthInfo(authInfo.userId, authInfo.openId)
        authInfo.updateAccessToken(newAuthInfo.accessToken)
        if (newAuthInfo.refreshToken.isNotNullOrEmpty()) {
            authInfo.refreshToken = newAuthInfo.refreshToken
        }
        authInfo.nickname = newAuthInfo.nickname
        if (StrUtil.isEmpty(authInfo.avatar) && StrUtil.isNotEmpty(newAuthInfo.avatar)) {
            val avatar = "honor_avatar_${authInfo.userId}_${Instant.now().epochSecond}.jpg"
            qiniuService.uploadUrl(newAuthInfo.avatar, avatar)
            authInfo.avatar = "https://file.gusto.cn/$avatar"
        }
        authInfoService.updateWithCache(authInfo)
        return authInfo
    }

}