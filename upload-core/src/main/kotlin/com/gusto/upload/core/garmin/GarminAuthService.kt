package com.gusto.upload.core.garmin

import cn.hutool.core.util.StrUtil
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwIfNull
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.garmin.GarminUserAuthInfo
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * 佳明-鉴权 服务类
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@Service
class GarminAuthService : GarminCommonService() {

    private val log = LoggerFactory.getLogger(javaClass)

    /**
     * 获取授权确认链接
     *
     * 获取未授权的RequestToken，并返回授权确认链接
     */
    fun getOAuthConfirmUrl(userId: Long, source: Int): String {
        // 获取RequestToken
        val rsp = getRequestToken(source)
        throwIfNull(rsp.result, ServiceException(UploadErrorCode.GARMIN_GET_AUTH_URL_ERROR))
        val resultList = rsp.result.split("&")
        val requestToken = resultList.first().split("=").last()
        val requestTokenSecret = resultList.last().split("=").last()

        // 新增/更新授权信息
        val dbAuthInfo = authInfoService.getOneByUserIdOrNull(userId, source)
        if (dbAuthInfo == null) {
            val newAuthInfo = GarminUserAuthInfo()
            newAuthInfo.userId = userId
            newAuthInfo.requestToken = requestToken
            newAuthInfo.requestTokenSecret = requestTokenSecret
            newAuthInfo.oauthVerifier = ""
            newAuthInfo.accessToken = ""
            newAuthInfo.accessTokenSecret = ""
            newAuthInfo.source = source
            authInfoService.save(newAuthInfo)
        } else {
            dbAuthInfo.requestToken = requestToken
            dbAuthInfo.requestTokenSecret = requestTokenSecret
            authInfoService.updateById(dbAuthInfo)
        }
        return getOAuthConfirmUrl(requestToken, source)
    }

    /**
     * 接收授权确认的回调
     */
    fun confirmCallback(requestToken: String, oauthVerifier: String, source: Int): Boolean {
        val authInfo = authInfoService.getOneByRequestToken(requestToken, source)
        authInfo.oauthVerifier = oauthVerifier
        // 获取AccessToken
        val rsp = getAccessToken(authInfo)
        if (rsp.isError) {
            if (StrUtil.isNotEmpty(rsp.content)) {
                log.warn("[callback-$source] requestToken = ${requestToken}, userId = ${authInfo.userId}, sync error, message = ${rsp.content}")
            } else {
                log.warn("[callback-$source] requestToken = ${requestToken}, userId = ${authInfo.userId}, sync error, exception = ${rsp.exception.message}")
            }
            return false
        }
        val resultList = rsp.result.split("&")
        val accessToken = resultList.first().split("=").last()
        val accessTokenSecret = resultList.last().split("=").last()
        authInfo.accessToken = accessToken
        authInfo.accessTokenSecret = accessTokenSecret
        // 获取佳明用户ID
        val userIdRsp = getGarminUserId(authInfo)
        if (userIdRsp.isSuccess) {
            // 填充佳明用户ID
            authInfo.garminUserId = userIdRsp.result.userId
            // 取消其他同一个佳明用户ID的其他第一赛道账号授权
            val dbAuthInfoList = authInfoService.getListByGarminUserId(authInfo.garminUserId, source)
                .filter { it.recordId != authInfo.recordId }
            if (dbAuthInfoList.isNotEmpty()) {
                asyncExecutor.execute {
                    dbAuthInfoList.forEach {
                        try {
                            cancelAuthByAuthInfo(it)
                        } catch (e: Exception) {
                            log.error(e.message)
                        }
                    }
                }
            }
        }
        authInfoService.updateById(authInfo)
        return true
    }

    /**
     * 取消授权
     */
    fun cancelAuthByUserId(userId: Long, source: Int): Boolean {
        val authInfo = authInfoService.getOneByUserIdOrNull(userId, source)
        throwIfNull(authInfo, ServiceException(UploadErrorCode.GARMIN_USER_AUTH_INFO_NOT_FOUND))
        return cancelAuthByAuthInfo(authInfo!!)
    }

    /**
     * 取消授权
     */
    private fun cancelAuthByAuthInfo(authInfo: GarminUserAuthInfo): Boolean {
        val rsp = cancelAuth(authInfo)
        if (rsp.isError) {
            if (StrUtil.isNotEmpty(rsp.content)) {
                log.error("[cancelAuthByAuthInfo-${authInfo.source}] userId = ${authInfo.userId}, sync error, message = ${rsp.content}")
            } else {
                log.error("[cancelAuthByAuthInfo-${authInfo.source}] userId = ${authInfo.userId}, sync error, exception = ${rsp.exception.message}")
            }
            return false
        }
        authInfoService.delete(authInfo)
        return true
    }

}
