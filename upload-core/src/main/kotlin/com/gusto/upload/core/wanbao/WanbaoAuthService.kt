package com.gusto.upload.core.wanbao

import com.gusto.upload.model.entity.wanbao.WanbaoUserAuthInfo
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.Instant

/**
 * <AUTHOR>
 * @since 2024-09-12
 */
@Service
class WanbaoAuthService : WanbaoCommonService() {

    private val log = LoggerFactory.getLogger(javaClass)

    /**
     * 授权回调
     */
    fun upsertAccessTokenByCode(code: String, userId: Long): Boolean {
        val rsp = wanbaoClient.getAccessToken(
            code = code,
            redirectUri = config.redirectUri,
            headers = buildHeaders(null)
        )
        if (rsp.isError) {
            log.error("[upsertAccessTokenByCode] get AT error, error = ${rsp.content}")
            return false
        }
        val authInfo = WanbaoUserAuthInfo().apply {
            this.userId = userId
            refreshToken = rsp.result.refreshToken
            accessToken = rsp.result.accessToken
            accessTokenUpdateTime = Instant.now()
            openId = rsp.result.userId
        }
        // 查询此腕宝账号是否绑定了多个第一赛道账号
        val dbAuthInfoListByOpenId = authInfoService.getListByOpenId(authInfo.openId)
        if (dbAuthInfoListByOpenId.isNotEmpty()) {
            dbAuthInfoListByOpenId.forEach {
                // 踢掉此腕宝账号的其他第一赛道账号授权，不用请求接口，因为腕宝是根据openId推送运动记录的
                authInfoService.removeById(it)
            }
        }
        // 查询此第一赛道是否绑定了多个腕宝账号
        val dbAuthInfoListByUserId = authInfoService.getListByUserId(authInfo.userId)
        if (dbAuthInfoListByUserId.isNotEmpty()) {
            dbAuthInfoListByUserId.forEach {
                // 踢掉此腕宝账号的其他第一赛道账号授权，不用请求接口，因为腕宝是根据openId推送运动记录的
                authInfoService.removeById(it)
            }
        }
        // 插入新的授权信息
        authInfoService.save(authInfo)
        return true
    }

    /**
     * 根据用户ID取消授权
     */
    fun cancelAuthByUserId(userId: Long): Boolean {
        val authInfo = authInfoService.getOneByUserId(userId)
        checkOrRefreshAccessToken(authInfo)
        val rsp = wanbaoClient.cancelAuth(headers = buildHeaders(authInfo.accessToken))
        if (rsp.isError || rsp.result.code != 200) {
            log.error("[cancelAuthByUserId] cancel error, error = ${rsp.content}")
            return false
        }
        authInfoService.removeById(authInfo)
        return true
    }

}
