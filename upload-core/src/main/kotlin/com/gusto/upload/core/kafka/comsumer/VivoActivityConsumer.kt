package com.gusto.upload.core.kafka.comsumer

import com.gusto.upload.core.vivo.VivoActivityService
import com.gusto.upload.model.entity.message.VivoActivityMessage
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @since 2024-03-05
 */
@Component
class VivoActivityConsumer {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var activityService: VivoActivityService

    /**
     * 处理订阅事件
     */
    @KafkaListener(
        topics = [VivoActivityMessage.NOTIFY_ACTIVITY_TOPIC],
        groupId = VivoActivityMessage.GROUP + "#UpsertUserRun"
    )
    fun upsertUserRun(message: VivoActivityMessage) {
        log.info("[upsertUserRun] get message = {}", message)
        activityService.syncActivityBySubscription(message.openId, message.exerciseId)
        log.info("[upsertUserRun] handle success")
    }

}
