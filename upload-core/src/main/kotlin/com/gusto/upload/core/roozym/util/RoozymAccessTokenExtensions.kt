package com.gusto.upload.core.roozym.util

import com.gusto.upload.model.entity.roozym.RoozymAccessToken
import com.gusto.upload.model.entity.roozym.RoozymAuthInfo

/**
 *
 * <AUTHOR>
 * @since 2022/6/9
 */
fun RoozymAccessToken.toUserAuthInfo(userId: Long): RoozymAuthInfo {
    val info = RoozymAuthInfo()
    info.userId = userId
    info.accessToken = this.accessToken
    info.refreshToken = this.refreshToken
    info.expiresIn = this.expiresIn
    info.openId = this.openId
    return info
}
