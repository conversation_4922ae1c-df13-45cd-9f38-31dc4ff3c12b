package com.gusto.upload.core.garmin

import cn.hutool.core.net.URLEncoder
import cn.hutool.core.util.CharsetUtil
import cn.hutool.core.util.RandomUtil
import cn.hutool.crypto.digest.HMac
import cn.hutool.crypto.digest.HmacAlgorithm
import com.dtflys.forest.http.ForestResponse
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwIfNull
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.garmin.GarminProperties
import com.gusto.upload.model.entity.garmin.GarminUserAuthInfo
import com.gusto.upload.model.entity.garmin.rsp.CancelAuthRsp
import com.gusto.upload.model.entity.garmin.rsp.GarminActivityDetailRsp
import com.gusto.upload.model.entity.garmin.rsp.GarminUserIdRsp
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.concurrent.Executor
import javax.annotation.Resource

/**
 * <AUTHOR>
 * @since 2022/5/24
 */
@Service
class GarminCommonService {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var garminProperties: GarminProperties

    @Autowired
    lateinit var authInfoService: GarminUserAuthInfoService

    @Autowired
    lateinit var garminClient: GarminClient

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    /**
     * 获取授权确认链接
     */
    fun getOAuthConfirmUrl(requestToken: String, source: Int): String {
        return when (source) {
            2 -> "https://connect.garmin.com/oauthConfirm?oauth_token=$requestToken&oauth_callback=${garminProperties.globalRedirectUri}"
            else -> "https://connect.garmin.cn/oauthConfirm?oauth_token=$requestToken&oauth_callback=${garminProperties.redirectUri}"
        }
    }

    /**
     * 获取RequestToken
     */
    fun getRequestToken(source: Int): ForestResponse<String> {
        val reqPair = when (source) {
            2 -> Pair("https://connectapi.garmin.com/oauth-service/oauth/request_token", "POST")
            else -> Pair("https://connectapi.garmin.cn/oauth-service/oauth/request_token", "POST")
        }
        val headers = buildHeader(1, reqPair, source)
        return when (source) {
            2 -> garminClient.getRequestTokenForGlobal(headers)
            else -> garminClient.getRequestToken(headers)
        }
    }

    /**
     * 获取AccessToken
     */
    fun getAccessToken(authInfo: GarminUserAuthInfo): ForestResponse<String> {
        val reqPair = when (authInfo.source) {
            2 -> Pair("https://connectapi.garmin.com/oauth-service/oauth/access_token", "POST")
            else -> Pair("https://connectapi.garmin.cn/oauth-service/oauth/access_token", "POST")
        }
        val headers = buildHeader(2, reqPair, authInfo.source, authInfo)
        return when (authInfo.source) {
            2 -> garminClient.getAccessTokenForGlobal(headers)
            else -> garminClient.getAccessToken(headers)
        }
    }

    /**
     * 获取佳明用户ID
     */
    fun getGarminUserId(authInfo: GarminUserAuthInfo): ForestResponse<GarminUserIdRsp> {
        val reqPair = when (authInfo.source) {
            2 -> Pair("https://apis.garmin.com/wellness-api/rest/user/id", "GET")
            else -> Pair("https://gcs-wellness.garmin.cn/wellness-api/rest/user/id", "GET")
        }
        val headers = buildHeader(3, reqPair, authInfo.source, authInfo)
        return when (authInfo.source) {
            2 -> garminClient.getGarminUserIdForGlobal(headers)
            else -> garminClient.getGarminUserId(headers)
        }
    }

    /**
     * 取消授权
     */
    fun cancelAuth(authInfo: GarminUserAuthInfo): ForestResponse<CancelAuthRsp> {
        val reqPair = when (authInfo.source) {
            2 -> Pair("https://apis.garmin.com/wellness-api/rest/user/registration", "DELETE")
            else -> Pair("https://gcs-wellness.garmin.cn/wellness-api/rest/user/registration", "DELETE")
        }
        val headers = buildHeader(3, reqPair, authInfo.source, authInfo)
        return when (authInfo.source) {
            2 -> garminClient.cancelAuthForGlobal(headers)
            else -> garminClient.cancelAuth(headers)
        }
    }

    /**
     * 获取活动详情列表
     */
    fun getActivityDetailList(
        authInfo: GarminUserAuthInfo,
        startTime: Long,
        endTime: Long
    ): ForestResponse<List<GarminActivityDetailRsp>> {
        val otherParamMap = mutableMapOf<String, Any>()
        otherParamMap["uploadStartTimeInSeconds"] = startTime
        otherParamMap["uploadEndTimeInSeconds"] = endTime
        val reqPair = when (authInfo.source) {
            2 -> Pair("https://apis.garmin.com/wellness-api/rest/activityDetails", "GET")
            else -> Pair("https://gcs-wellness.garmin.cn/wellness-api/rest/activityDetails", "GET")
        }
        val headers = buildHeader(3, reqPair, authInfo.source, authInfo, otherParamMap)
        return when (authInfo.source) {
            2 -> garminClient.getActivityDetailListForGlobal(startTime, endTime, headers)
            else -> garminClient.getActivityDetailList(startTime, endTime, headers)
        }
    }

    /**
     * 生成请求头
     *
     * type:
     *  1-获取RequestToken
     *  2-获取AccessToken
     *  3-请求API
     */
    private fun buildHeader(
        type: Int,
        req: Pair<String, String>,
        source: Int,
        authInfo: GarminUserAuthInfo? = null,
        otherParamMap: Map<String, Any> = emptyMap()
    ): Map<String, String> {
        val (url, method) = req
        val requestMap = mutableMapOf<String, Any>()
        requestMap["oauth_consumer_key"] = when (source) {
            2 -> garminProperties.globalConsumerKey
            else -> garminProperties.consumerKey
        }
        requestMap["oauth_signature_method"] = "HMAC-SHA1"
        requestMap["oauth_nonce"] = RandomUtil.randomString(10)
        requestMap["oauth_timestamp"] = Instant.now().epochSecond
        requestMap["oauth_version"] = "1.0"
        var tokenSecret = ""
        when (type) {
            // 用RequestToken换AccessToken
            2 -> {
                throwIfNull(authInfo, ServiceException(UploadErrorCode.GARMIN_USER_AUTH_INFO_NOT_FOUND))
                requestMap["oauth_token"] = authInfo!!.requestToken
                requestMap["oauth_verifier"] = authInfo.oauthVerifier
                tokenSecret = authInfo.requestTokenSecret
            }

            // 请求API
            3 -> {
                throwIfNull(authInfo, ServiceException(UploadErrorCode.GARMIN_USER_AUTH_INFO_NOT_FOUND))
                requestMap["oauth_token"] = authInfo!!.accessToken
                requestMap["uploadStartTimeInSeconds"] = otherParamMap["uploadStartTimeInSeconds"] ?: ""
                requestMap["uploadEndTimeInSeconds"] = otherParamMap["uploadEndTimeInSeconds"] ?: ""
                tokenSecret = authInfo.accessTokenSecret
            }
        }
        val paramString = requestMap
            .filter { it.value.toString() != "" }
            .toSortedMap()
            .map { "${it.key}=${it.value}" }
            .joinToString("&")
        requestMap["oauth_signature"] = buildSignature(method, url, paramString, tokenSecret, source)
        val authorization = "OAuth " + requestMap
            .filterNot { it.key.contains("TimeInSeconds") }
            .toSortedMap()
            .map { """${it.key}="${it.value}"""" }
            .joinToString(", ")
        return mapOf(Pair("Authorization", authorization))
    }

    /**
     * 创建签名
     */
    protected fun buildSignature(
        method: String,
        url: String,
        parameter: String,
        tokenSecret: String,
        source: Int
    ): String {
        val baseString = "${method.uppercase()}&${urlEncode(url)}&${urlEncode(parameter)}"
        val secret = when (source) {
            2 -> garminProperties.globalConsumerSecret
            else -> garminProperties.consumerSecret
        }
        val key = "$secret&$tokenSecret"
        val hmac = HMac(HmacAlgorithm.HmacSHA1, key.toByteArray())
        val sign = hmac.digestBase64(baseString, false)
        return urlEncode(sign)
    }

    /**
     * URL ENCODE
     */
    private fun urlEncode(string: String): String {
        return URLEncoder.ALL.encode(string, CharsetUtil.CHARSET_UTF_8)
    }

}
