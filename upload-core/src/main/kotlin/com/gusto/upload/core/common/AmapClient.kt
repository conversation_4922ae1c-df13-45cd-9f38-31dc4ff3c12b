package com.gusto.upload.core.common

import com.dtflys.forest.annotation.BaseRequest
import com.dtflys.forest.annotation.Get
import com.dtflys.forest.annotation.Query
import com.dtflys.forest.http.ForestResponse
import com.gusto.upload.model.entity.amap.AmapReGeo
import com.gusto.upload.model.entity.amap.AmapWeatherInfo

/**
 * <p>
 * 高德 REST API调用入口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@BaseRequest(
    baseURL = "https://restapi.amap.com/v3",
    connectTimeout = 3000,
    readTimeout = 3000,
    retryCount = 3
)
interface AmapClient {

    /**
     * 逆地理编码 https://lbs.amap.com/api/webservice/guide/api/georegeo#t5
     *
     * @param key 在线参数获取key
     * @param location 经纬度坐标，传入内容规则：经度在前，纬度在后，经纬度间以“,”分割，经纬度小数点后不要超过6位
     */
    @Get("/geocode/regeo")
    fun getReGeo(
        @Query("key") key: String,
        @Query("location") location: String
    ): ForestResponse<AmapReGeo>

    /**
     * 天气查询 https://lbs.amap.com/api/webservice/guide/api-advanced/weatherinfo
     *
     * @param key 在线参数获取key
     * @param city 城市编码adcode
     */
    @Get("/weather/weatherInfo")
    fun getWeatherInfo(
        @Query("key") key: String,
        @Query("city") city: String
    ): ForestResponse<AmapWeatherInfo>

}
