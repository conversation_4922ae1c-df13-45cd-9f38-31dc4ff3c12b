package com.gusto.upload.core.vivo

import cn.hutool.core.util.StrUtil
import com.baomidou.mybatisplus.core.toolkit.Wrappers
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwError
import com.gusto.upload.core.dao.vivo.VivoUserAuthInfoDao
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.vivo.VivoUserAuthInfo
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Service
class VivoUserAuthInfoService : ServiceImpl<VivoUserAuthInfoDao, VivoUserAuthInfo>() {

    /**
     * 根据VIVO用户ID获取列表
     */
    fun getListByOpenId(openId: String): List<VivoUserAuthInfo> {
        if (StrUtil.isEmpty(openId)) {
            return emptyList()
        }
        val wrapper = Wrappers.lambdaQuery<VivoUserAuthInfo>()
            .eq(VivoUserAuthInfo::getOpenId, openId)
        return list(wrapper)
    }

    /**
     * 根据用户ID获取列表
     */
    fun getListByUserId(userId: Long): List<VivoUserAuthInfo> {
        val wrapper = Wrappers.lambdaQuery<VivoUserAuthInfo>()
            .eq(VivoUserAuthInfo::getUserId, userId)
        return list(wrapper)
    }

    /**
     * 根据VIVO用户ID获取
     */
    fun getOneByOpenIdOrNull(openId: String): VivoUserAuthInfo? {
        val wrapper = Wrappers.lambdaQuery<VivoUserAuthInfo>()
            .eq(VivoUserAuthInfo::getOpenId, openId)
        val authInfo = getOne(wrapper)
        if (authInfo == null) {
            log.warn("[getOneByOpenId] openId = $openId not found")
        }
        return authInfo
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserId(userId: Long): VivoUserAuthInfo {
        val wrapper = Wrappers.lambdaQuery<VivoUserAuthInfo>()
            .eq(VivoUserAuthInfo::getUserId, userId)
        val authInfo = getOne(wrapper)
        if (authInfo == null) {
            log.error("[getOneByUserId] userId = $userId not found")
            throwError(ServiceException(UploadErrorCode.VIVO_USER_AUTH_INFO_NOT_FOUND))
        }
        return authInfo!!
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserIdOrNull(userId: Long): VivoUserAuthInfo? {
        val wrapper = Wrappers.lambdaQuery<VivoUserAuthInfo>()
            .eq(VivoUserAuthInfo::getUserId, userId)
        return getOne(wrapper)
    }

}
