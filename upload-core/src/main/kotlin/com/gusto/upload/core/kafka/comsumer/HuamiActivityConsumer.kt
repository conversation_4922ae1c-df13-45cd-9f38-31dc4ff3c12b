package com.gusto.upload.core.kafka.comsumer

import com.alicp.jetcache.Cache
import com.alicp.jetcache.anno.CreateCache
import com.gusto.upload.core.huami.HuamiActivityService
import com.gusto.upload.core.service.NotifyRecordService
import com.gusto.upload.core.service.user.NewUserRunService
import com.gusto.upload.core.utils.roundTwo
import com.gusto.upload.model.entity.message.HuamiActivityMessage
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component
import java.time.Instant

/**
 * 华米-活动
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Component
class HuamiActivityConsumer {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var activityService: HuamiActivityService

    @Autowired
    lateinit var notifyRecordService: NotifyRecordService

    @Autowired
    lateinit var runService: NewUserRunService

    @Suppress("DEPRECATION")
    @CreateCache(name = "HuamiActivityConsumer.upsertUserRunCache.", expire = 60 * 3)
    private lateinit var upsertUserRunCache: Cache<String, String>

    /**
     * 处理订阅事件
     */
    @KafkaListener(
        topics = [HuamiActivityMessage.NOTIFY_ACTIVITY_TOPIC],
        groupId = HuamiActivityMessage.GROUP + "#UpsertUserRun"
    )
    fun upsertUserRun(message: HuamiActivityMessage) {
        log.info("[upsertUserRun] get message = {}", message)
        val cache = upsertUserRunCache.GET(message.buildUniqueKey())
        if (cache.isSuccess) {
            log.info("[upsertUserRun] cache exist, return")
            return
        } else {
            upsertUserRunCache.PUT(message.buildUniqueKey(), Instant.now().toEpochMilli().toString())
        }

        val runtime = Runtime.getRuntime()
        val totalMemory = runtime.totalMemory() / 1024 / 1024 // 总内存
        val maxMemory = runtime.maxMemory() / 1024 / 1024 // 最大内存
        val freeMemory = runtime.freeMemory() / 1024 / 1024 // 可用内存
        val usedMemory = totalMemory - freeMemory // 已使用内存
        // 内存使用率
        val memoryRate = (usedMemory.toDouble() / maxMemory.toDouble() * 100).roundTwo()
        log.debug("[upsertUserRun] 总内存 = $maxMemory MB | 最大内存 = $totalMemory MB | 已使用内存 = $usedMemory MB | 可用内存 = $freeMemory MB | 内存使用率 =  $memoryRate%")

        activityService.syncActivityBySubscription(message.openId, message.data, message.trackId)
        notifyRecordService.removeByUniqueKey(message.buildUniqueKey())
        upsertUserRunCache.REMOVE(message.buildUniqueKey())
        log.info("[upsertUserRun] handle success")
    }

}
