package com.gusto.upload.core.huami

import cn.hutool.core.util.StrUtil
import com.gusto.upload.model.entity.huami.HuamiUserAuthInfo
import com.gusto.upload.model.entity.user.User
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.Instant

/**
 * 华米-授权服务
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
class HuamiAuthService : HuamiCommonService() {

    private val log = LoggerFactory.getLogger(javaClass)

    /**
     * 授权回调
     */
    fun upsertAccessTokenByCode(code: String, user: User): Boolean {
        val rsp = huamiClient.getAccessToken(
            code = code,
            clientId = config.clientId,
            clientSecret = config.clientSecret,
            redirectUri = config.redirectUri
        )
        if (rsp.isError) {
            log.error("[upsertAccessTokenByCode] get AT error, error = ${rsp.content}")
            return false
        }
        val authInfo = HuamiUserAuthInfo().apply {
            userId = user.userId
            refreshToken = rsp.result.refreshToken
            accessToken = rsp.result.accessToken
            accessTokenUpdateTime = Instant.now()
        }

        val userInfoRsp = huamiClient.getUserInfo(buildAuthorization(authInfo.accessToken))
        if (userInfoRsp.isSuccess) {
            val userInfoResult = userInfoRsp.result
            authInfo.openId = userInfoResult.userId ?: ""
            authInfo.avatar = userInfoResult.avatar ?: ""
            authInfo.nickname = userInfoResult.nickName ?: ""
            // 处理头像
            if (StrUtil.isNotEmpty(authInfo.avatar)) {
                val avatar = "huami_avatar_${authInfo.userId}_${Instant.now().epochSecond}.jpg"
                qiniuService.uploadUrl(authInfo.avatar, avatar)
                authInfo.avatar = "https://file.gusto.cn/$avatar"
            }
        } else {
            log.error("[upsertAccessTokenByCode] get user info error, error = ${userInfoRsp.content}")
            return false
        }

        // 查询此华米账号是否绑定了多个第一赛道账号
        val dbAuthInfoListByOpenId = authInfoService.getListByOpenId(authInfo.openId ?: "")
        if (dbAuthInfoListByOpenId.isNotEmpty()) {
            dbAuthInfoListByOpenId.forEach {
                // 踢掉此华米账号的其他第一赛道账号授权
                authInfoService.removeById(it)
            }
        }
        // 查询此第一赛道是否绑定了多个华米账号
        val dbAuthInfoListByUserId = authInfoService.getListByUserId(authInfo.userId)
        if (dbAuthInfoListByUserId.isNotEmpty()) {
            dbAuthInfoListByUserId.forEach {
                // 踢掉此用户的其他华米账号授权
                authInfoService.removeById(it)
            }
        }

        // 插入新的授权信息
        authInfoService.save(authInfo)

        return true
    }

    /**
     * 根据用户ID取消授权
     */
    fun cancelAuthByUserId(userId: Long): Boolean {
        val authInfo = authInfoService.getOneByUserId(userId)
        checkOrRefreshAccessToken(authInfo)
        authInfoService.removeById(authInfo)
        return true
    }

}
