package com.gusto.upload.core.roozym.util

import com.gusto.upload.model.entity.roozym.RoozymSportType

/**
 *
 * <AUTHOR>
 * @since 2022/5/26
 */
object RoozymSportTypeUtils {
    fun formatSportType(sportType: Int): Int {
        return when (sportType) {
            RoozymSportType.RUNNING.number,
            RoozymSportType.MARATHON.number -> 1

            RoozymSportType.RUNNING_IN_ROOM.number -> 2
            RoozymSportType.HIKING.number,
            RoozymSportType.WALKING.number -> 3

            RoozymSportType.CROSS_COUNTRY_RACE.number,
            RoozymSportType.WALKING_CROSS_COUNTRY_RACE.number -> 4

            RoozymSportType.CYCLING.number -> 5
            RoozymSportType.SWIMMING_POOL.number, RoozymSportType.SWIMMING_OPEN_WATER.number -> 6
            else -> 100
        }
    }
}
