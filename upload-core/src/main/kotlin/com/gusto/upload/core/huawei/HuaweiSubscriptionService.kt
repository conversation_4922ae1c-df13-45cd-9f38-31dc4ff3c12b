package com.gusto.upload.core.huawei

import cn.hutool.core.codec.Base64
import cn.hutool.crypto.digest.DigestUtil
import cn.hutool.crypto.digest.HmacAlgorithm
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.core.service.NotifyRecordService
import com.gusto.upload.core.utils.huawei.toMessageOrNull
import com.gusto.upload.model.entity.NotifyRecord
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.huawei.HuaweiEventType
import com.gusto.upload.model.entity.huawei.HuaweiSubscription
import com.gusto.upload.model.entity.huawei.HuaweiSubscriptionEventType
import com.gusto.upload.model.entity.huawei.HuaweiUserAuthInfo
import com.gusto.upload.model.entity.huawei.request.HuaweiNotificationReq
import com.gusto.upload.model.entity.huawei.request.HuaweiUpsertSubscriptionReq
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service


/**
 * 华为-订阅 服务类
 *
 * <AUTHOR>
 * @since 2021-10-28
 */
@Service
class HuaweiSubscriptionService : HuaweiService() {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var notifyRecordService: NotifyRecordService

    /**
     * doc: https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/subscription-0000001078496860#ZH-CN_TOPIC_0000001078496860__section13879857154018
     */
    private val eventTypeList = buildEventTypeList()

    private fun buildEventTypeList(): List<HuaweiEventType> {
        val type = HuaweiEventType()
        type.type = "ACTIVITY_RECORD_EVENT"
        type.subType = "UPDATE"
        type.eventType = type.type + "$" + type.subType
        return listOf(type)
    }

    /**
     * 新增/更新订阅记录（只能有一处调用，避免authInfo被乱更新）
     */
    fun upsert(authInfo: HuaweiUserAuthInfo) {
        checkOrRefreshAccessToken(authInfo)!!
        val req = HuaweiUpsertSubscriptionReq()
        req.eventTypes = eventTypeList
        req.subscriberId = "07a13a7c-3372-47d6-85f4-251b21faa847"
        val rsp = huaweiClient.upsertSubscription(req, getHttpsHeaders(authInfo))
        if (rsp.isError && rsp.statusIsNot(200)) {
            log.warn("[upsert] req error, error = ${rsp.content}")
            throw ServiceException(UploadErrorCode.HUAWEI_UPSERT_SUBSCRIPTION_ERROR)
        }
        authInfo.openId = rsp.result.first().openId
        authInfoService.updateWithCache(authInfo)
        log.info("[upsert] req success")
    }

    /**
     * 删除所有订阅记录
     */
    fun deleteAll(authInfo: HuaweiUserAuthInfo) {
        val subList = getList(authInfo)
        subList.forEach {
            val rsp = huaweiClient.deleteSubscription(it.subscriptionId, getHttpsHeaders(authInfo))
            if (rsp.isError && rsp.statusIsNot(200)) {
                log.error("[deleteAll] req error, error = ${rsp.content}")
            }
        }
        log.debug("[deleteAll] req success")
    }

    /**
     * 查询订阅记录列表
     */
    fun getList(authInfo: HuaweiUserAuthInfo): List<HuaweiSubscription> {
        val rsp = huaweiClient.getSubscriptionList(getHttpsHeaders(authInfo))
        if (rsp.isError && rsp.statusIsNot(200)) {
            log.warn("[getList] req error, error = ${rsp.content}")
        }
        return rsp.result ?: emptyList()
    }

    /**
     * 签名
     */
    fun sign(req: HuaweiNotificationReq): String {
        val hmac = DigestUtil.hmac(HmacAlgorithm.HmacSHA256, Base64.decode(config.subscriptionSecret))
        val content = "${req.openId}_${req.eventType}_${req.eventTime}"
        return hmac.digestBase64(content, false)
    }

    /**
     * 处理订阅事件
     */
    fun handler(req: HuaweiNotificationReq) {
        log.debug("[handler] req = ${req.formatToJson()}")
        val userId = authInfoService.getUserIdByOpenId(req.openId)

        when (HuaweiSubscriptionEventType.forDesc(req.eventType)) {
            // 创建/更新运动记录事件
            HuaweiSubscriptionEventType.ACTIVITY_RECORD_EVENT_UPDATE -> {
                val activityMessage = req.toMessageOrNull(userId) ?: return
                val notifyRecord = NotifyRecord().apply {
                    this.uniqueKey = activityMessage.buildUniqueKey()
                    this.deviceId = 1
                }
                notifyRecordService.save(notifyRecord)
                activityProducer.sendSubscriptionMessage(activityMessage)
            }

            else -> return
        }
    }
}