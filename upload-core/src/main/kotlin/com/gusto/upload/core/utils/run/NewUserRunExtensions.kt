package com.gusto.upload.core.utils.run

import cn.hutool.core.util.NumberUtil
import com.gusto.upload.core.utils.roundTwo
import com.gusto.upload.model.entity.user.NewUserRun
import com.gusto.upload.model.entity.user.NewUserRunDetail

private const val halfMarathon = "21.0975"
private const val marathon = "42.195"

/**
 * 保留两位小数
 */
fun NewUserRun.roundTwo() {
    totalDistance = totalDistance.roundTwo()
    totalCalorie = totalCalorie.roundTwo()
    averagePace = averagePace.roundTwo()
    maxPace = maxPace.roundTwo()
    averageStride = averageStride.roundTwo()
    minAltitude = minAltitude.roundTwo()
    maxAltitude = maxAltitude.roundTwo()
    totalAscent = totalAscent.roundTwo()
    totalDescent = totalDescent.roundTwo()
}

/**
 * 保留两位小数
 */
fun NewUserRunDetail.roundTwo() {
    totalDistance = totalDistance.roundTwo()
    totalCalorie = totalCalorie.roundTwo()
    averagePace = averagePace.roundTwo()
    maxPace = maxPace.roundTwo()
    averageStride = averageStride.roundTwo()
    minAltitude = minAltitude.roundTwo()
    maxAltitude = maxAltitude.roundTwo()
    totalAscent = totalAscent.roundTwo()
    totalDescent = totalDescent.roundTwo()
    lapList.onEach {
        it.distance = it.distance.roundTwo()
    }
    locationList.onEach {
        it.speed = it.speed.roundTwo()
    }
    altitudeList.onEach {
        it.altitude = it.altitude.roundTwo()
    }
}

/**
 * 填充最佳距离用时
 */
fun NewUserRun.fillBestResult() {
    if (partTimeKmList.isNotEmpty()) {
        partTimeKmList = partTimeKmList.toSortedMap { o1, o2 ->
            NumberUtil.parseDouble(o1).compareTo(NumberUtil.parseDouble(o2))
        }
    }
    val timeList = buildTimeList(partTimeKmList, totalDistance)
    bestOneKm = calBestTime(timeList, 1, this)
    bestThreeKm = calBestTime(timeList, 3, this)
    bestFiveKm = calBestTime(timeList, 5, this)
    bestTenKm = calBestTime(timeList, 10, this)
    bestHalfMarathon = partTimeKmList[halfMarathon] ?: 0
    bestMarathon = partTimeKmList[marathon] ?: 0
}

/**
 * 创建时间列表
 */
private fun buildTimeList(partTimeKmList: Map<String, Int>, totalDistance: Double): List<Int> {
    val kmMap = partTimeKmList.toMutableMap()
    kmMap.remove("21.0975")
    kmMap.remove("42.195")
    val kmSize = (totalDistance / 1000).toInt()
    // 分段时间列表是完整或者有多的，直接返回列表
    if (kmSize == kmMap.size) {
        return kmMap.values.toList().sorted()
    }
    // 分段时间列表不完整直接返回空列表
    return emptyList()
}

/**
 * 计算最佳距离用时
 */
private fun calBestTime(timeList: List<Int>, calSize: Int, run: NewUserRun): Int {
    if (timeList.isEmpty() || calSize == 0) {
        return 0
    }
    val totalDistance = run.totalDistance
    // 总距离小于最佳距离
    if (totalDistance < calSize * 1000) {
        return 0
    }
    if (timeList.size == calSize) {
        return timeList.last()
    }
    val newTimeList = timeList.toMutableList()
    newTimeList.add(0, 0)
    val timeGroup = newTimeList.windowed(calSize + 1)
    return timeGroup
        .map { it.last() - it.first() }
        .minOf { it }
}