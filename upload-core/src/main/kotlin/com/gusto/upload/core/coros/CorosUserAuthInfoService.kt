package com.gusto.upload.core.coros

import cn.hutool.core.util.StrUtil
import com.baomidou.mybatisplus.core.toolkit.Wrappers
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwError
import com.gusto.upload.core.dao.coros.CorosUserAuthInfoDao
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.coros.CorosUserAuthInfo
import org.springframework.stereotype.Service

/**
 * 高驰-用户授权信息
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Service
class CorosUserAuthInfoService : ServiceImpl<CorosUserAuthInfoDao, CorosUserAuthInfo>() {

    /**
     * 根据高驰用户ID获取列表
     */
    fun getListByOpenId(openId: String): List<CorosUserAuthInfo> {
        if (StrUtil.isEmpty(openId)) {
            return emptyList()
        }
        val wrapper = Wrappers.lambdaQuery<CorosUserAuthInfo>()
            .eq(CorosUserAuthInfo::getOpenId, openId)
        return list(wrapper)
    }

    /**
     * 根据用户ID获取列表
     */
    fun getListByUserId(userId: Long): List<CorosUserAuthInfo> {
        val wrapper = Wrappers.lambdaQuery<CorosUserAuthInfo>()
            .eq(CorosUserAuthInfo::getUserId, userId)
        return list(wrapper)
    }

    /**
     * 根据高驰用户ID获取
     */
    fun getOneByOpenIdOrNull(openId: String): CorosUserAuthInfo? {
        val wrapper = Wrappers.lambdaQuery<CorosUserAuthInfo>()
            .eq(CorosUserAuthInfo::getOpenId, openId)
        val authInfo = getOne(wrapper)
        if (authInfo == null) {
            log.warn("[getOneByOpenId] openId = $openId not found")
            // throwError(ServiceException(UploadErrorCode.COROS_USER_AUTH_INFO_NOT_FOUND))
        }
        return authInfo
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserId(userId: Long): CorosUserAuthInfo {
        val wrapper = Wrappers.lambdaQuery<CorosUserAuthInfo>()
            .eq(CorosUserAuthInfo::getUserId, userId)
        val authInfo = getOne(wrapper)
        if (authInfo == null) {
            log.error("[getOneByUserId] userId = $userId not found")
            throwError(ServiceException(UploadErrorCode.COROS_USER_AUTH_INFO_NOT_FOUND))
        }
        return authInfo!!
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserIdOrNull(userId: Long): CorosUserAuthInfo? {
        val wrapper = Wrappers.lambdaQuery<CorosUserAuthInfo>()
            .eq(CorosUserAuthInfo::getUserId, userId)
        return getOne(wrapper)
    }

}
