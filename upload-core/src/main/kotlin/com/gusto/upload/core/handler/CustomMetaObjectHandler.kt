package com.gusto.upload.core.handler

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler
import org.apache.ibatis.reflection.MetaObject
import org.springframework.stereotype.Component
import java.time.Instant

/**
 * 自定义字段填充器
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Component
class CustomMetaObjectHandler : MetaObjectHandler {

    override fun insertFill(metaObject: MetaObject) {
        val now = Instant.now()
        strictInsertFill(metaObject, "createTime", { now }, Instant::class.java)
        strictInsertFill(metaObject, "updateTime", { now }, Instant::class.java)
    }

    override fun updateFill(metaObject: MetaObject) {
        directFillHasGetter(metaObject, "updateTime", Instant.now())
    }

    /**
     * 有值不覆盖
     */
    protected fun fillHasGetter(metaObject: MetaObject, fieldName: String?, fieldVal: Any?) {
        if (metaObject.hasGetter(fieldName)) {
            fillStrategy(metaObject, fieldName, fieldVal)
        }
    }

    /**
     * 直接覆盖
     */
    protected fun directFillHasGetter(metaObject: MetaObject, fieldName: String?, fieldVal: Any?) {
        if (metaObject.hasGetter(fieldName)) {
            metaObject.setValue(fieldName, fieldVal)
        }
    }

}