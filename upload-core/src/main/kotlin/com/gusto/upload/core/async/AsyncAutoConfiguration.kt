package com.gusto.upload.core.async

import lombok.extern.slf4j.Slf4j
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.annotation.AsyncConfigurer
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import java.lang.reflect.Method
import java.util.concurrent.Executor

@Slf4j
@Configuration
@EnableAsync(proxyTargetClass = true)
class AsyncAutoConfiguration : AsyncConfigurer {

    private val log = LoggerFactory.getLogger(javaClass)

    @Bean(name = ["asyncExecutor"])
    override fun getAsyncExecutor(): Executor {
        val executor = ThreadPoolTaskExecutor()
        executor.setBeanName("asyncExecutor")
        executor.corePoolSize = 2
        executor.maxPoolSize = 32
        executor.queueCapacity = 16
        executor.threadNamePrefix = "Async-"
        // 设置线程池监听器
        executor.setTaskDecorator { runnable ->
            // 在任务执行完成后调用回调方法
            val contextMap = MDC.getCopyOfContextMap()
            Runnable {
                try {
                    if (contextMap != null) {
                        // 内部为子线程的领域范围，所以将父线程的上下文保存到子线程上下文中，而且每次submit/execute调用都会更新为最新的上下文对象
                        MDC.setContextMap(contextMap);
                    }
                    runnable.run()
                } finally {
                    MDC.clear();
                }
            }
        }
        executor.initialize()
        return executor
    }

    /*
     * 仅能捕获到返回值为 void 的 @Async 方法的异常，若该方法有 Future<?> 返回值，需要在方法内自行捕获异常并记录日志
     */
    override fun getAsyncUncaughtExceptionHandler(): AsyncUncaughtExceptionHandler {
        return AsyncUncaughtExceptionHandler { ex: Throwable, method: Method, _: Array<Any> ->
            val methodName = method.declaringClass.name + "." + method.name
            log.error(methodName, ex)
        }
    }

}