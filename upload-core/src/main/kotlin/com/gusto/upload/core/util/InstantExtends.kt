package com.gusto.upload.core.util

import com.gusto.framework.core.util.time.toInstant
import java.time.DayOfWeek
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.TemporalAdjusters
import java.time.temporal.WeekFields
import java.util.*

fun Instant.format(pattern: String): String {
    val formatter = DateTimeFormatter.ofPattern(pattern)
    return LocalDateTime.ofInstant(this, ZoneId.systemDefault()).format(formatter)
}

fun Instant.formatDate(): String {
    return format("yyyy-MM-dd")
}

fun Instant.formatDateHourMinute(): String {
    return format("yyyy-MM-dd HH:mm")
}

fun Instant.formatDateHourMinuteSecond(): String {
    return format("yyyy-MM-dd HH:mm:ss")
}

fun Instant.toLocalDate(): LocalDate {
    return this.atZone(ZoneId.systemDefault()).toLocalDate()
}

/**
 * 获取本周第一天
 */
fun Instant.startOfWeek(): Instant {
    val fieldIso = WeekFields.of(DayOfWeek.MONDAY, 1).dayOfWeek()
    return toLocalDate().with(fieldIso, 1).atStartOfDay().toInstant()
}

/**
 * 获取本周最后一天
 */
fun Instant.endOfWeek(): Instant {
    val fieldIso = WeekFields.of(DayOfWeek.MONDAY, 1).dayOfWeek()
    return toLocalDate().with(fieldIso, 7).atStartOfDay().toInstant()
}

/**
 * 获取本月第一天
 */
fun Instant.startOfMonth(): Instant {
    val date = toLocalDate()
    return LocalDate.of(date.year, date.month, 1).toInstant()
}

/**
 * 获取本月最后一天
 */
fun Instant.endOfMonth(): Instant {
    return toLocalDate().with(TemporalAdjusters.lastDayOfMonth()).toInstant()
}

/**
 * 获取今天是周几
 * 1: 周一
 * 2: 周二
 * ...
 * 6: 周六
 * 7: 周日
 */
fun Instant.dayOfWeek(): Int {
    val cal = Calendar.getInstance()
    cal.timeInMillis = toEpochMilli()
    val calDay = cal.get(Calendar.DAY_OF_WEEK) - 1
    return if (calDay == 0) 7 else calDay
}
