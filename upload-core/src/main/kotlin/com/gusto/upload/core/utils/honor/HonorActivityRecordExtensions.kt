package com.gusto.upload.core.utils.honor

import com.gusto.upload.core.utils.honor.HonorSportTypeUtils.formatSportType
import com.gusto.upload.model.entity.honor.HonorActivityRecord
import com.gusto.upload.model.entity.user.NewUserRun
import org.bouncycastle.asn1.x509.X509ObjectIdentifiers.id

fun HonorActivityRecord.toNewUserRun(userId: Long): NewUserRun {
    val preview = recPreview
    val run = NewUserRun()
    run.activityId = preview.startTime * 1000
    run.userId = userId
    run.deviceId = 3
    run.deviceModel = "荣耀运动健康"
    run.deviceType = 311
    run.activityType = formatSportType(dataType)
    run.startTime = preview.startTime * 1000
    run.endTime = preview.endTime * 1000
    run.totalDistance = preview.distance.toDouble()
    run.totalDuration = preview.sportTime
    run.totalStep = preview.step
    run.totalCalorie = preview.consumeKcal
    run.averagePace = run.totalDuration.div(run.totalDistance / 1000)
    run.maxPace = 0.0
    run.averageHeartRate = preview.avgHeartRate
    run.maxHeartRate = 0
    run.averageStepRate = 0
    run.maxStepRate = 0
    run.averageStride = if (run.totalStep == 0) 0.0 else run.totalDistance * 100.0 / run.totalStep
    run.minAltitude = 0.0
    run.maxAltitude = 0.0
    run.totalAscent = 0.0
    run.totalDescent = 0.0
    run.weather = ""
    run.province = ""
    run.city = ""
    run.temperature = ""
    run.humidity = ""
    run.partTimeKmList = emptyMap()
    run.thirdActivityType = dataType
    run.thirdActivityId = id
    run.abnormal = 14
    run.externalSportType = run.thirdActivityType
    run.mapTrackImage = ""
    run.trackImage = ""
    return run
}