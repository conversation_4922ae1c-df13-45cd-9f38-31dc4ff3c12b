package com.gusto.upload.core.huawei

import cn.hutool.core.util.StrUtil
import com.dtflys.forest.http.ForestResponse
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwIf
import com.gusto.framework.core.util.json.formatToJson
import com.gusto.framework.core.util.time.minusDays
import com.gusto.upload.core.application.user.NewUserRunApplicationService
import com.gusto.upload.core.common.PushService
import com.gusto.upload.core.service.NotifyRecordService
import com.gusto.upload.core.service.app.AppMessageService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.util.NewUserRunMapUtils
import com.gusto.upload.core.util.formatDateHourMinuteSecond
import com.gusto.upload.core.utils.JsonUtils.initObjectMapper
import com.gusto.upload.core.utils.huawei.HuaweiConst.HUAWEI_DATA_ID_ALTITUDE
import com.gusto.upload.core.utils.huawei.HuaweiConst.HUAWEI_DATA_ID_EXERCISE_HEART_RATE
import com.gusto.upload.core.utils.huawei.HuaweiConst.HUAWEI_DATA_ID_LOCATION
import com.gusto.upload.core.utils.huawei.HuaweiConst.HUAWEI_DATA_ID_SPEED
import com.gusto.upload.core.utils.huawei.HuaweiConst.HUAWEI_DATA_ID_STEPS_RATE
import com.gusto.upload.core.utils.huawei.HuaweiConst.HUAWEI_DATA_TYPE_ALTITUDE
import com.gusto.upload.core.utils.huawei.HuaweiConst.HUAWEI_DATA_TYPE_EXERCISE_HEART_RATE
import com.gusto.upload.core.utils.huawei.HuaweiConst.HUAWEI_DATA_TYPE_LOCATION
import com.gusto.upload.core.utils.huawei.HuaweiConst.HUAWEI_DATA_TYPE_SPEED
import com.gusto.upload.core.utils.huawei.HuaweiConst.HUAWEI_DATA_TYPE_STEPS_RATE
import com.gusto.upload.core.utils.huawei.buildAltitude
import com.gusto.upload.core.utils.huawei.buildHeartRate
import com.gusto.upload.core.utils.huawei.buildLocationOrNull
import com.gusto.upload.core.utils.huawei.buildSpeed
import com.gusto.upload.core.utils.huawei.buildStepRate
import com.gusto.upload.core.utils.huawei.toNewUserRun
import com.gusto.upload.core.utils.roundTwo
import com.gusto.upload.core.utils.run.roundTwo
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.huawei.HuaweiActivityRecord
import com.gusto.upload.model.entity.huawei.HuaweiPolymerizeWith
import com.gusto.upload.model.entity.huawei.HuaweiUserAuthInfo
import com.gusto.upload.model.entity.huawei.request.HuaweiGetPolymerizeSampleSetReq
import com.gusto.upload.model.entity.huawei.response.HuaweiGetPolymerizeSampleSetRsp
import com.gusto.upload.model.entity.response.ManualSyncRsp
import com.gusto.upload.model.entity.user.NewUserRun
import com.gusto.upload.model.entity.user.NewUserRunDetailAltitudeItem
import com.gusto.upload.model.entity.user.NewUserRunDetailHeartRateItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLapItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLocationItem
import com.gusto.upload.model.entity.user.NewUserRunDetailSpeedItem
import com.gusto.upload.model.entity.user.NewUserRunDetailStepRateItem
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.concurrent.Executor
import javax.annotation.Resource
import kotlin.math.max


/**
 * 华为-活动 服务类
 *
 * <AUTHOR>
 * @since 2021-10-28
 */
@Service
class HuaweiActivityService : HuaweiService() {

    private val log = LoggerFactory.getLogger(javaClass)

    private val objectMapper = initObjectMapper(lowerCamelCase = true)

    // 聚合标准列表
    private val polymerizeWithList = getPolymerizeWithList()

    // 聚合标准列表
    private val polymerizeWithListWithoutAltitude = getPolymerizeWithListWithoutAltitude()

    @Autowired
    lateinit var newRunApplicationService: NewUserRunApplicationService

    @Autowired
    lateinit var pushService: PushService

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var mapUtils: NewUserRunMapUtils

    @Autowired
    lateinit var messageService: AppMessageService

    @Autowired
    lateinit var notifyRecordService: NotifyRecordService

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    /**
     * 根据订阅事件同步活动记录列表
     */
    fun syncUserActivityRecordListBySubscription(
        userId: Long,
        startTime: String,
        endTime: String,
        sportType: Int
    ) {
        val uncheckAuthInfo = try {
            authInfoService.getOneByUserId(userId)
        } catch (e: ServiceException) {
            log.warn("[syncUserActivityRecordListBySubscription] userId = $userId, auth not found")
            return
        }
        val authInfo = checkOrRefreshAccessToken(uncheckAuthInfo, true)
        if (authInfo == null) {
            log.warn("[syncUserActivityRecordListBySubscription] userId = $userId, refresh AT error, skip sync")
            return
        }
        val rsp = huaweiClient.getUserActivityRecordList(
            startTime,
            endTime,
            listOf(sportType),
            getHttpsHeaders(authInfo)
        )
        if (rsp.isError && rsp.statusIsNot(200)) {
            log.warn("[syncUserActivityRecordListBySubscription] userId = $userId, sync error, message = ${rsp.content}")
            if (!rsp.content.contains("scopes")) {
                return
            }
            val scopeRsp = huaweiClient.getScopeByClientId(config.clientId, getHttpsHeaders(authInfo))
            if (scopeRsp.isError && scopeRsp.statusIsNot(200)) {
                log.error("[getScopeListByUserId] req error, error = ${scopeRsp.content}")
            } else {
                val scopeList = scopeRsp.result.url2Desc?.formatToJson() ?: ""
                log.warn("[syncUserActivityRecordListBySubscription] userId = $userId, sync error, scope = $scopeList")
                asyncExecutor.execute {
                    messageService.notifyHuaweiScopeError(
                        userId,
                        scopeRsp.result.url2Desc?.values?.toList() ?: emptyList()
                    )
                }
            }
            return
        }

        // 创建跑步记录头部和明细
        createUserRunAndDetailForNew(authInfo, rsp.result.activityRecord ?: emptyList(), userId)
    }

    /**
     * 手动同步活动记录列表
     */
    fun syncUserActivityRecordListByManual(
        userId: Long,
        reqStartTime: Instant? = null,
        reqEndTime: Instant? = null,
        cacheKey: String? = null
    ): ManualSyncRsp {
        val uncheckAuthInfo = authInfoService.getOneByUserIdOrNull(userId)
        if (uncheckAuthInfo == null) {
            notifyRecordService.removeByUniqueKey(cacheKey!!)
            throw ServiceException(UploadErrorCode.HUAWEI_USER_AUTH_INFO_NOT_FOUND)
        }
        val authInfo = checkOrRefreshAccessToken(uncheckAuthInfo)!!
        val endTime = reqEndTime ?: Instant.now()
        val startTime =
            reqStartTime ?: Instant.ofEpochMilli(
                max(
                    authInfo.createTime.toEpochMilli(),
                    endTime.minusDays(7).toEpochMilli()
                )
            )
        val rsp = huaweiClient.getUserActivityRecordList(
            startTime.toEpochMilli().toString(),
            endTime.toEpochMilli().toString(),
            emptyList(),
            getHttpsHeaders(authInfo)
        )
        val outRsp = ManualSyncRsp()
        outRsp.result = true
        outRsp.reason = "同步成功，请稍后在跑步记录页查看"
        if (rsp.isSuccess) {
            if (StrUtil.isNotEmpty(cacheKey)) {
                notifyRecordService.removeByUniqueKey(cacheKey!!)
            }
        }
        if (rsp.isError && rsp.statusIsNot(200)) {
            log.error("[syncUserActivityRecordListByManual] userId = $userId, get record list from huawei error, response content = ${rsp.content}")
            notifyRecordService.removeByUniqueKey(cacheKey!!)
            outRsp.result = false
            outRsp.reason = "同步失败，请取消授权重新绑定"
            return outRsp
        }
        if (rsp.result.activityRecord.isEmpty()) {
            outRsp.reason = "暂无运动记录可同步"
            return outRsp
        }
        // 创建跑步记录头部和明细
        asyncExecutor.execute {
            log.info("[syncUserActivityRecordListByManual] userId = $userId, sync async start")
            createUserRunAndDetailForNew(authInfo, rsp.result.activityRecord, userId)
            log.info("[syncUserActivityRecordListByManual] userId = $userId, sync async success")
        }
        return outRsp
    }

    /**
     * 新 遍历获取到的活动记录，同步到第一赛道
     */
    private fun createUserRunAndDetailForNew(
        authInfo: HuaweiUserAuthInfo,
        activityRecordList: List<HuaweiActivityRecord>,
        userId: Long
    ) {
        var syncSuccessCount = 0
        val runList = emptyList<NewUserRun>().toMutableList()
        activityRecordList.forEach {
            val run = it.toNewUserRun(userId)
            log.debug(
                "[createUserRunAndDetailForNew] userId=$userId, startTime=${
                    Instant.ofEpochMilli(run.startTime).formatDateHourMinuteSecond()
                }"
            )
            val createSuccess = newRunApplicationService.handlerAndCreate(run)
            notifyRecordService.removeByUniqueKey(run.buildUniqueKey())
            if (!createSuccess) return@forEach
            syncSuccessCount++
            buildAndUploadNewUserRunDetail(authInfo, it, run)
            runList.add(run)
        }
        log.info("[getUserActivityRecordList] userId=$userId, sync success, count=$syncSuccessCount")
        if (syncSuccessCount == 0) return
        asyncExecutor.execute {
            val latestRun = runList.maxByOrNull { it.startTime }
            if (latestRun != null) {
                val user = userService.getOneById(latestRun.userId)
                pushService.notifySyncSuccess(latestRun, syncSuccessCount, user.userId)
            }
        }
    }

    /**
     * 处理地图缺失
     */
    fun handlerMapMission(run: NewUserRun) {
        // 计算trackFile里面有几个“_1”
        val count = StrUtil.count(run.trackFile, "_1")
        if (count > 3) {
            log.debug("[handlerMapMission] trackFile=${run.trackFile}, count=$count>3, return")
            return
        }
        val uncheckAuthInfo = authInfoService.getOneByUserIdOrNull(run.userId)
        throwIf(uncheckAuthInfo == null, ServiceException(UploadErrorCode.HUAWEI_USER_AUTH_INFO_NOT_FOUND))
        val authInfo = checkOrRefreshAccessToken(uncheckAuthInfo!!)!!
        val rsp = huaweiClient.getUserActivityRecordList(
            run.startTime.toString(),
            run.endTime.toString(),
            emptyList(),
            getHttpsHeaders(authInfo)
        )
        if (rsp.result.activityRecord.isEmpty()) return
        buildAndUploadNewUserRunDetail(authInfo, rsp.result.activityRecord.first(), run, true)
    }

    /**
     * 新 创建和上传新跑步记录明细
     */
    private fun buildAndUploadNewUserRunDetail(
        authInfo: HuaweiUserAuthInfo,
        activityRecord: HuaweiActivityRecord,
        run: NewUserRun,
        handlerMapMission: Boolean = false
    ) {
        val rsp = getPolymerizeSampleSet(authInfo, run.startTime, run.endTime) ?: return
        val locationList = emptyList<NewUserRunDetailLocationItem>().toMutableList()
        val speedList = emptyList<NewUserRunDetailSpeedItem>().toMutableList()
        val heartRateList = emptyList<NewUserRunDetailHeartRateItem>().toMutableList()
        val stepRateList = emptyList<NewUserRunDetailStepRateItem>().toMutableList()
        val altitudeList = emptyList<NewUserRunDetailAltitudeItem>().toMutableList()
        val groupList = rsp.result.group
        groupList.forEach { group ->
            val collectorId = group.sampleSet.first().dataCollectorId
            val samplePointList = group.sampleSet.first().samplePoints
            log.debug("[buildAndUploadNewUserRunDetail] collectorId = $collectorId, samplePointList size = ${samplePointList.size}")
            when (group.sampleSet.first().dataCollectorId) {
                HUAWEI_DATA_ID_LOCATION -> {
                    val samplePoints = group.sampleSet.first().samplePoints
                    samplePoints.forEach location@{
                        val location = it.buildLocationOrNull() ?: return@location
                        locationList.add(location)
                    }
                }

                HUAWEI_DATA_ID_EXERCISE_HEART_RATE -> {
                    group.sampleSet.first().samplePoints.forEach { heartRateList.add(it.buildHeartRate()) }
                }

                HUAWEI_DATA_ID_SPEED -> {
                    group.sampleSet.first().samplePoints.forEach { speedList.add(it.buildSpeed()) }
                }

                HUAWEI_DATA_ID_STEPS_RATE -> {
                    group.sampleSet.first().samplePoints.forEach { stepRateList.add(it.buildStepRate()) }
                }

                HUAWEI_DATA_ID_ALTITUDE -> {
                    group.sampleSet.first().samplePoints.forEach { altitudeList.add(it.buildAltitude()) }
                }
            }
        }
        // 填充速度
        val locationListSize = locationList.size
        locationList.onEachIndexed { index, item ->
            var multiple = index / 5
            if (multiple > locationListSize) multiple = locationListSize
            item.speed = speedList.getOrNull(multiple)?.speed ?: 0.0
        }
        // 获取每圈信息
        val paceSummary = activityRecord.activitySummary.paceSummary
        val sortedPaceMapList = if (paceSummary != null) {
            paceSummary.paceMap
                .entries.sortedBy { it.key.toDouble() }
                .associateBy({ it.key.toDouble() }, { it.value.toInt() })
                .map { it }
        } else {
            emptyList()
        }
        val lapList = emptyList<NewUserRunDetailLapItem>().toMutableList()
        sortedPaceMapList.forEachIndexed { index, it ->
            val lap = NewUserRunDetailLapItem()
            if (index == 0) {
                lap.distance = it.key * 1000
                lap.duration = it.value
            } else {
                val preLap = sortedPaceMapList[index - 1]
                lap.distance = (it.key - preLap.key) * 1000
                lap.duration = it.value
            }
            lap.distance = lap.distance.roundTwo()

            if (lap.distance < 1000) {
                lap.duration = (lap.duration * (lap.distance / 1000)).toInt()
            }

            lapList.add(lap)
        }

        val detail = mapUtils.toNewUserRunDetail(run)
        detail.lapList = lapList
        detail.locationList = locationList
        detail.heartRateList = heartRateList
        detail.stepRateList = stepRateList
        detail.altitudeList = altitudeList
        detail.partTimeKmList = run.partTimeKmList
        detail.roundTwo()

        if (handlerMapMission) {
            run.trackFile += "_1"
            // 计算trackFile里面有几个“_1”
            val count = StrUtil.count(run.trackFile, "_1")
            if (count > 3) {
                log.debug("[buildAndUploadNewUserRunDetail] trackFile=${run.trackFile}, count=$count>3, return")
                return
            }
            newRunApplicationService.runService.updateById(run)
        }
        // 上传到七牛
//        qiniuService.uploadString(
//            objectMapper.writeValueAsString(detail),
//            "${run.trackFile}.gzip"
//        )
//        qiniuService.uploadString(
//            objectMapper.writeValueAsString(activityRecord),
//            "huawei_activity_record_${run.trackFile}.gzip"
//        )
//        qiniuService.uploadString(
//            objectMapper.writeValueAsString(rsp.result),
//            "huawei_activity_detail_${run.trackFile}.gzip"
//        )
        qiniuService.uploadObject(
            detail,
            "${run.trackFile}.gzip"
        )
        qiniuService.uploadObject(
            activityRecord,
            "huawei_activity_record_${run.trackFile}.gzip"
        )
        qiniuService.uploadObject(
            rsp.result,
            "huawei_activity_detail_${run.trackFile}.gzip"
        )
    }

    /**
     * 获取采样数据
     */
    private fun getPolymerizeSampleSet(
        authInfo: HuaweiUserAuthInfo,
        startTime: Long,
        endTime: Long
    ): ForestResponse<HuaweiGetPolymerizeSampleSetRsp>? {
        val req = HuaweiGetPolymerizeSampleSetReq.builder()
            .startTime(startTime)
            .endTime(endTime)
            .polymerizeWith(polymerizeWithList)
            .groupByTime(null)
            .build()
        val rsp = huaweiClient.getPolymerizeSampleSet(
            req,
            getHttpsHeaders(authInfo)
        )
        if (rsp.isError && rsp.statusIsNot(200)) {
            if (rsp.content.contains("com.huawei.instantaneous.altitude")) {
                log.warn("[getPolymerizeSampleSet] userId = ${authInfo.userId}, sync error, message = ${rsp.content}")
                req.polymerizeWith = polymerizeWithListWithoutAltitude
                val againRsp = huaweiClient.getPolymerizeSampleSet(req, getHttpsHeaders(authInfo))
                if (againRsp.isError && againRsp.statusIsNot(200)) {
                    log.warn("[getPolymerizeSampleSet] userId = ${authInfo.userId}, sync error, message = ${againRsp.content}")
                    return null
                }
            } else {
                log.warn("[getPolymerizeSampleSet] userId = ${authInfo.userId}, sync error, message = ${rsp.content}")
                return null
            }
        }
        return rsp
    }

    /**
     * 获取聚合标准列表
     */
    private fun getPolymerizeWithList(): List<HuaweiPolymerizeWith> {
        // GPS 位置
        val location = HuaweiPolymerizeWith.builder()
            .dataTypeName(HUAWEI_DATA_TYPE_LOCATION)
            .build()
        // 活动心率
        val exerciseHeartRate = HuaweiPolymerizeWith.builder()
            .dataTypeName(HUAWEI_DATA_TYPE_EXERCISE_HEART_RATE)
            .build()
        // 速度
        val speed = HuaweiPolymerizeWith.builder()
            .dataTypeName(HUAWEI_DATA_TYPE_SPEED)
            .build()
        // 步频
        val stepsRate = HuaweiPolymerizeWith.builder()
            .dataTypeName(HUAWEI_DATA_TYPE_STEPS_RATE)
            .build()
        // 海拔
        val altitude = HuaweiPolymerizeWith.builder()
            .dataTypeName(HUAWEI_DATA_TYPE_ALTITUDE)
            .build()
        return listOf(location, exerciseHeartRate, speed, stepsRate, altitude)
    }

    /**
     * 获取聚合标准列表
     */
    private fun getPolymerizeWithListWithoutAltitude(): List<HuaweiPolymerizeWith> {
        // GPS 位置
        val location = HuaweiPolymerizeWith.builder()
            .dataTypeName(HUAWEI_DATA_TYPE_LOCATION)
            .build()
        // 活动心率
        val exerciseHeartRate = HuaweiPolymerizeWith.builder()
            .dataTypeName(HUAWEI_DATA_TYPE_EXERCISE_HEART_RATE)
            .build()
        // 速度
        val speed = HuaweiPolymerizeWith.builder()
            .dataTypeName(HUAWEI_DATA_TYPE_SPEED)
            .build()
        // 步频
        val stepsRate = HuaweiPolymerizeWith.builder()
            .dataTypeName(HUAWEI_DATA_TYPE_STEPS_RATE)
            .build()
        return listOf(location, exerciseHeartRate, speed, stepsRate)
    }
}
