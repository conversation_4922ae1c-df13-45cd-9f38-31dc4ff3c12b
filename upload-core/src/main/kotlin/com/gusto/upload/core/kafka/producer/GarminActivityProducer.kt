package com.gusto.upload.core.kafka.producer

import com.gusto.upload.model.entity.message.GarminActivityMessage
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.support.SendResult
import org.springframework.stereotype.Component
import org.springframework.util.concurrent.ListenableFutureCallback

/**
 * 佳明-活动
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Component
class GarminActivityProducer {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var kafkaTemplate: KafkaTemplate<String, Any>

    fun sendNotifyActivityMessage(message: GarminActivityMessage) {
        val topic = when (message.source) {
            2 -> GarminActivityMessage.NOTIFY_GLOBAL_ACTIVITY_TOPIC
            else -> GarminActivityMessage.NOTIFY_ACTIVITY_TOPIC
        }
        val feature = kafkaTemplate.send(topic, message)
        feature.addCallback(object : ListenableFutureCallback<SendResult<String, Any>> {
            override fun onFailure(ex: Throwable) {
                log.error("[sendNotifyActivityMessage-${message.source}] [onFailure] message = $message, cause = ${ex.message}")
            }

            override fun onSuccess(result: SendResult<String, Any>) {
                log.info("[sendNotifyActivityMessage-${message.source}] [onSuccess] topic = $topic, message = ${result.producerRecord.value()}")
            }
        })
    }

}
