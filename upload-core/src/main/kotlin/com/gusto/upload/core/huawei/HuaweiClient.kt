package com.gusto.upload.core.huawei

import com.dtflys.forest.annotation.Body
import com.dtflys.forest.annotation.Delete
import com.dtflys.forest.annotation.Get
import com.dtflys.forest.annotation.Header
import com.dtflys.forest.annotation.JSONBody
import com.dtflys.forest.annotation.LogEnabled
import com.dtflys.forest.annotation.Post
import com.dtflys.forest.annotation.Query
import com.dtflys.forest.annotation.Retry
import com.dtflys.forest.annotation.Var
import com.dtflys.forest.http.ForestResponse
import com.gusto.upload.model.entity.huawei.HuaweiAccessToken
import com.gusto.upload.model.entity.huawei.HuaweiScopeLangItem
import com.gusto.upload.model.entity.huawei.HuaweiSubscription
import com.gusto.upload.model.entity.huawei.request.HuaweiGetPolymerizeSampleSetReq
import com.gusto.upload.model.entity.huawei.request.HuaweiUpsertSubscriptionReq
import com.gusto.upload.model.entity.huawei.response.HuaweiGetActivityRecordListRsp
import com.gusto.upload.model.entity.huawei.response.HuaweiGetPolymerizeSampleSetRsp

/**
 * 华为-REST API调用入口
 *
 * <AUTHOR>
 * @since 2021-10-28
 */
@Retry(maxRetryCount = "0", maxRetryInterval = "0")
interface HuaweiClient {

    /**
     * 使用授权码Code获取AT
     */
    @Post("https://oauth-login.cloud.huawei.com/oauth2/v3/token")
    fun getAccessToken(
        @Body("grant_type") grantType: String = "authorization_code",
        @Body("code") code: String,
        @Body("client_id") clientId: String,
        @Body("client_secret") clientSecret: String,
        @Body("redirect_uri") redirectUri: String
    ): ForestResponse<HuaweiAccessToken>

    /**
     * 通过RT刷新AT
     */
    @Post("https://oauth-login.cloud.huawei.com/oauth2/v3/token")
    fun refreshAccessToken(
        @Body("grant_type") grantType: String = "refresh_token",
        @Body("refresh_token") refreshToken: String,
        @Body("client_id") clientId: String,
        @Body("client_secret") clientSecret: String,
        @Body("access_type") accessType: String = "offline"
    ): ForestResponse<HuaweiAccessToken>

    /**
     * 查询用户授予某个应用的权限信息
     *
     * @param clientId 必填，应用ID
     */
    @Get(
        url = "https://health-api.cloud.huawei.com/healthkit/v1/consents/{clientId}?lang=zh-cn",
        interceptor = [HuaweiClientInterceptor::class]
    )
    fun getScopeByClientId(
        @Var("clientId") clientId: String,
        @Header headers: Map<String, Any>
    ): ForestResponse<HuaweiScopeLangItem>

    /**
     * 隐私授权状态查询接口
     * 查询华为运动健康App对Health Kit的隐私授权情况
     * 1-已授权 2-未授权 3-非华为运动健康App用户（不能授权）
     */
    @Get(
        url = "https://health-api.cloud.huawei.com/healthkit/v1/profile/privacyRecords",
        interceptor = [HuaweiClientInterceptor::class]
    )
    fun getPrivacyAuthState(
        @Header headers: Map<String, Any>
    ): ForestResponse<List<Map<String, Int>>>

    /**
     * 取消授权
     *
     * @param clientId 必填，应用ID
     */
    @Delete(
        url = "https://health-api.cloud.huawei.com/healthkit/v1/consents/{clientId}",
        interceptor = [HuaweiClientInterceptor::class]
    )
    fun cancelAuth(
        @Var("clientId") clientId: String,
        @Header headers: Map<String, Any>
    ): ForestResponse<String>

    /**
     * 查询已创建的活动记录
     *
     * @param startTime    必填，开始时间。13位整数的时间戳，单位：毫秒。startTime/endTime同时为非空字符串时生效，否则不生效
     * @param endTime      必填，结束时间。13位整数的时间戳，单位：毫秒。endTime与startTime时间间隔不能超过30天
     * @param activityType 非必填，待查询的活动类型列表
     */
    @Get(
        url = "https://health-api.cloud.huawei.com/healthkit/v2/activityRecords?includeDuplicate=true&includeAbnormal=true",
        interceptor = [HuaweiClientInterceptor::class]
    )
    fun getUserActivityRecordList(
        @Query("startTime") startTime: String,
        @Query("endTime") endTime: String,
        @Query("activityType") activityType: List<Int>,
        @Header headers: Map<String, Any>
    ): ForestResponse<HuaweiGetActivityRecordListRsp>

    /**
     * 采样数据聚合分组
     */
    @LogEnabled(logResponseContent = false)
    @Post(
        url = "https://health-api.cloud.huawei.com/healthkit/v1/sampleSet:polymerize",
        interceptor = [HuaweiClientInterceptor::class]
    )
    fun getPolymerizeSampleSet(
        @JSONBody req: HuaweiGetPolymerizeSampleSetReq,
        @Header headers: Map<String, Any>
    ): ForestResponse<HuaweiGetPolymerizeSampleSetRsp>

    /**
     * 新增/更新订阅记录
     */
    @Post(
        url = "https://health-api.cloud.huawei.com/healthkit/v1/subscriptions",
        interceptor = [HuaweiClientInterceptor::class]
    )
    fun upsertSubscription(
        @JSONBody req: HuaweiUpsertSubscriptionReq,
        @Header headers: Map<String, Any>
    ): ForestResponse<List<HuaweiSubscription>>

    /**
     * 删除订阅记录
     */
    @Delete(
        url = "https://health-api.cloud.huawei.com/healthkit/v1/subscriptions/{subscriptionId}",
        interceptor = [HuaweiClientInterceptor::class]
    )
    fun deleteSubscription(
        @Var("subscriptionId") subscriptionId: String,
        @Header headers: Map<String, Any>
    ): ForestResponse<HuaweiSubscription>

    /**
     * 查询订阅记录列表
     */
    @Get(
        url = "https://health-api.cloud.huawei.com/healthkit/v1/subscriptions",
        interceptor = [HuaweiClientInterceptor::class]
    )
    fun getSubscriptionList(
        @Header headers: Map<String, Any>
    ): ForestResponse<List<HuaweiSubscription>>

}
