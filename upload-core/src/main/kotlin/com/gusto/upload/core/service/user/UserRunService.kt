package com.gusto.upload.core.service.user

import cn.hutool.core.date.DateTime
import cn.hutool.core.date.DateUtil
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.gusto.upload.core.dao.user.UserRunDao
import com.gusto.upload.model.entity.user.old.UserRun
import org.springframework.stereotype.Service
import java.time.Instant

/**
 * 用户跑步记录 服务类
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Deprecated("Old data format")
@Service
class UserRunService : ServiceImpl<UserRunDao, UserRun>() {
    /**
     * 根据指定用户ID和时间获取列表，用于查重
     */
    fun getListByUserIdAndTimeForDuplicate(userId: Long, startTime: Long, endTime: Long): List<UserRun> {
        val query = QueryWrapper<UserRun>()
        query.eq(UserRun.USER_ID_FIELD, userId)
        query.ge(UserRun.STATE_FIELD, 1)
        query.not {
            it.lt(UserRun.END_TIME_FIELD, startTime).or().gt(
                UserRun.START_TIME_FIELD, endTime
            )
        }
        return list(query)
    }

    /**
     * 根据用户ID和识别ID获取列表
     */
    fun getListByUserIdAndRunId(userId: Long, runId: Long): List<UserRun> {
        val query = QueryWrapper<UserRun>()
        query.eq(UserRun.USER_ID_FIELD, userId)
        query.eq(UserRun.RUN_ID_FIELD, runId)
        return list(query)
    }

    /**
     * 创建记录
     */
    fun create(run: UserRun): UserRun {
        val now = Instant.now()
        run.uploadTime = now
        run.modifyTime = now
        run.adminUpdate = false
        run.adminUpdateTime = null
        run.trackFile = "${run.userId}_${run.runId}"
        run.deviceName = ""
        run.type = 0
        run.source = 3
        run.externalSportType = ""
        run.state = 1
        try {
            run.week = DateUtil.weekOfYear(DateTime.of(run.startTime))
            run.province = ""
            run.city = ""
            run.weather = ""
            run.temperature = ""
            run.humidity = ""
        } catch (e: Exception) {
            println(e.message)
        }
        save(run)
        return run
    }
}
