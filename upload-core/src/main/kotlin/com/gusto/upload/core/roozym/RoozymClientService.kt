package com.gusto.upload.core.roozym

import com.dtflys.forest.http.ForestResponse
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.isNotNullOrEmpty
import com.gusto.upload.core.roozym.util.isExpires
import com.gusto.upload.core.roozym.util.toUserAuthInfo
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.roozym.RoozymAccessToken
import com.gusto.upload.model.entity.roozym.RoozymAuthInfo
import com.gusto.upload.model.entity.roozym.RoozymProperties
import com.gusto.upload.model.entity.roozym.RoozymSport
import com.gusto.upload.model.entity.roozym.req.RoozymQuerySportReq
import com.gusto.upload.model.entity.roozym.req.RoozymUpsertSubscriptionReq
import com.gusto.upload.model.entity.roozym.rsp.RoozymCancelAuthRsp
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant

/**
 *
 * <AUTHOR>
 * @since 2022/7/21
 */
@Service
class RoozymClientService {
    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var roozymClient: RoozymClient

    @Autowired
    lateinit var authInfoService: RoozymUserAuthInfoService

    @Autowired
    lateinit var config: RoozymProperties

    /**
     * 查询跑步数据
     */
    fun querySport(
        req: RoozymQuerySportReq,
        uncheckAuthInfo: RoozymAuthInfo
    ): Pair<ForestResponse<List<RoozymSport>>, RoozymAuthInfo> {
        val authInfo = checkOrRefreshAccessToken(uncheckAuthInfo)
        return Pair(roozymClient.querySport(req, getHttpsHeaders(authInfo)), authInfo)
    }

    /**
     * 使用授权码Code获取AT
     */
    fun getAccessToken(
        code: String,
        clientId: String,
        clientSecret: String,
        redirectUri: String
    ): ForestResponse<RoozymAccessToken> {
        return roozymClient.getAccessToken("authorization_code", code, clientId, clientSecret, redirectUri)
    }

    /**
     * 新增/更新订阅记录
     */
    fun upsertSubscription(
        req: RoozymUpsertSubscriptionReq,
        uncheckAuthInfo: RoozymAuthInfo
    ): Pair<ForestResponse<String>, RoozymAuthInfo> {
        val authInfo = checkOrRefreshAccessToken(uncheckAuthInfo)
        return Pair(roozymClient.upsertSubscription(req, getHttpsHeaders(authInfo)), authInfo)
    }

    /**
     * 取消授权
     */
    fun cancelAuth(uncheckAuthInfo: RoozymAuthInfo): Pair<ForestResponse<RoozymCancelAuthRsp>, RoozymAuthInfo> {
        val authInfo = checkOrRefreshAccessToken(uncheckAuthInfo)
        return Pair(roozymClient.cancelAuth(getHttpsHeaders(authInfo)), authInfo)
    }

    /**
     * 获取统一HTTPS请求头
     */
    private fun getHttpsHeaders(authInfo: RoozymAuthInfo): Map<String, Any> {
        val headers = HashMap<String, Any>()
        // 必填
        headers["Content-type"] = "application/json; charset=UTF-8"
        // 必填
        headers["Authorization"] = "bearer ${authInfo.accessToken}"
        // 必填
        headers["client-id"] = config.clientId
        // 非必填 接口调用方的软件版本号。即当前客户端版本号，服务端可以基于其进行灰度，建议携带
        headers["x-version"] = config.outApiVersion
        // 非必填 请求跟踪ID。用于串联服务调用方与服务端整体请求链条，建议携带
        headers["x-caller-trace-id"] = "${authInfo.userId}-${Instant.now().toEpochMilli()}"
        return headers
    }

    /**
     * 检查用户授权信息是否过期，如果过期则刷新
     */
    private fun checkOrRefreshAccessToken(authInfo: RoozymAuthInfo): RoozymAuthInfo {
        return if (authInfo.isExpires()) {
            refreshAccessToken(authInfo)
        } else {
            authInfo
        }
    }

    /**
     * 刷新AT（access_type=offline）
     * PS：access_type为offline，刷新AT时，会一并返回RT，需要比较新的RT与当前正在使用的RT是否有变化，如果有变化，则立即更换并使用新的RT
     */
    private fun refreshAccessToken(authInfo: RoozymAuthInfo): RoozymAuthInfo {
        log.info("[Roozym:refreshAccessToken] userId = ${authInfo.userId}, authInfo = $authInfo")
        if (authInfo.refreshTokenExpires) {
            log.error("[Roozym:refreshAccessToken] userId = ${authInfo.userId}, RT expires")
            authInfo.refreshTokenExpires = true
            authInfo.deleted = true
            authInfoService.updateWithCache(authInfo)
            throw ServiceException(UploadErrorCode.ROOZYM_REFRESH_TOKEN_EXPIRES)
        }
        val rsp = roozymClient.refreshAccessToken(
            refreshToken = authInfo.refreshToken,
            clientId = config.clientId,
            clientSecret = config.clientSecret
        )
        if (rsp.isError) {
            log.error("[Roozym:refreshAccessToken] userId = ${authInfo.userId} refresh AT error, error = ${rsp.content}")
            authInfo.refreshTokenExpires = true
            authInfo.deleted = true
            authInfoService.updateWithCache(authInfo)
            throw ServiceException(UploadErrorCode.ROOZYM_REFRESH_ACCESS_TOKEN_ERROR)
        }
        val newAuthInfo = rsp.result.toUserAuthInfo(authInfo.userId)
        authInfo.updateAccessToken(newAuthInfo.accessToken)
        authInfo.expiresIn = newAuthInfo.expiresIn
        authInfo.refreshTokenExpires = false
        if (newAuthInfo.refreshToken.isNotNullOrEmpty()) {
            authInfo.refreshToken = newAuthInfo.refreshToken
        }
        authInfoService.updateWithCache(authInfo)
        return authInfo
    }
}
