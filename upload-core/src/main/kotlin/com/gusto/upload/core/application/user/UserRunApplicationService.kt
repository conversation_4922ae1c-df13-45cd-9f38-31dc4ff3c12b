package com.gusto.upload.core.application.user

import com.alicp.jetcache.Cache
import com.alicp.jetcache.anno.CreateCache
import com.gusto.framework.core.util.isNotNullOrBlank
import com.gusto.upload.core.common.MatchClient
import com.gusto.upload.core.service.user.NewUserRunUploadNotifyService
import com.gusto.upload.core.service.user.UserRunService
import com.gusto.upload.model.entity.user.old.UserRun
import com.gusto.upload.model.entity.user.rsp.GetNewUserRunListStatRsp
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.concurrent.Executor
import javax.annotation.Resource

/**
 * 用户跑步记录 应用服务类
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Deprecated("Old data format")
@Service
class UserRunApplicationService {
    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var runService: UserRunService

    @Autowired
    lateinit var matchClient: MatchClient

    @CreateCache(name = "NewUserRunApplicationService.getMonthStat.", expire = 3600 * 24 * 7)
    lateinit var runMonthStatCache: Cache<Long, GetNewUserRunListStatRsp>

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    @Autowired
    lateinit var uploadNotifyService: NewUserRunUploadNotifyService

    /**
     * 处理跑步记录并插入
     *
     * @return 是否插入成功
     */
    fun handlerAndCreate(run: UserRun): Boolean {
        val userId = run.userId
        val runId = run.runId
        val logPre = "[handlerAndCreate] userId = $userId , runId = $runId"
        log.debug("$logPre, start handler")
        if (userId == 0L || runId == 0L || run.eventId.isNullOrEmpty()) {
            log.debug("$logPre, eventId = ${run.eventId}, one of them is zero or null, return false")
            return false
        }

        // 超出时间范围直接忽略
        if (run.year < 2020 || run.year > 2100) {
            log.debug("$logPre, return false")
            return false
        }

        // 小于100米直接忽略
        if (run.distance < 100F) {
            log.debug("$logPre, distance < 100, return false")
            return false
        }

        // [重要] 判断当前记录是否已入库
        val dbRun = runService.getListByUserIdAndRunId(userId, runId).firstOrNull()
        if (dbRun != null) {
            log.debug("$logPre, has been inserted into the database")
            return false
        }

        // 判断跑步记录是否合法，默认为其他运动，满足条件的才设为跑步
        run.unusual = 0
        run.unusualMsg = ""
        run.category = 2
        if (run.sportType >= 5) {
            // 非跑步/徒步则设为其他运动
            run.category = 2
            run.unusual = 1
            run.unusualMsg += "非跑步/徒步运动"
        } else if (run.source == 2) {
            // 手动录入有效
            run.category = 1
        } else if (run.averagePace in 130.0..600.0 && run.stepFrequency in 70..300 && run.stepStride in 30.0..200.0) {
            run.category = 1
        } else if (run.averagePace in 600.0..3600.0 && run.stepFrequency in 1..300 && run.stepStride in 30.0..200.0) {
            run.category = 1
        }

        // 判断是否重复
        run.duplicate = false
        run.duplicateGroupId = 0
        // 关联tapd 【【重复记录】处理错误问题】https://www.tapd.cn/32401252/bugtrace/bugs/view?bug_id=1132401252001001231
        // [重要] 判断是否重复
        // 查出时间有重合的记录列表
        val duplicateList = runService.getListByUserIdAndTimeForDuplicate(run.userId, run.startTime, run.endTime)
        if (duplicateList.isNotEmpty()) {
            val duplicateRun = duplicateList.minByOrNull { it.startTime }!!

            // 有重复记录
            run.duplicate = true
            run.duplicateGroupId = if (duplicateRun.duplicateGroupId > 0) {
                duplicateRun.duplicateGroupId
            } else {
                duplicateRun.id
            }
            run.unusualMsg = if (run.unusualMsg.isNotNullOrBlank()) {
                "${run.unusualMsg} 重复记录"
            } else {
                "重复记录"
            }
            duplicateRun.duplicateGroupId = run.duplicateGroupId
            runService.updateById(duplicateRun)
        }

        runService.create(run)
        log.debug("$logPre, create success")

        // 清除缓存
        runMonthStatCache.REMOVE(run.userId)

        asyncExecutor.execute {
            // PS：有可能出现502
            val rsp = matchClient.userRunUploadNotify(run.userId, run.id)
            if (rsp.isError || rsp.status_4xx() || rsp.status_5xx()) {
                val uploadNotify = run.buildNewUserRunUploadNotify(1)
                uploadNotifyService.save(uploadNotify)
            }
        }

        return true
    }
}