package com.gusto.upload.core.utils.huawei

import com.gusto.upload.model.entity.huawei.HuaweiActivityType

/**
 * 华为-运动类型工具类
 *
 * <AUTHOR>
 * @since 2021-11-04
 */
object HuaweiSportTypeUtils {
    /**
     * 运动类型转换
     */
    fun formatSportType(activityType: Int): Int {
        // 1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它
        // https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/introduction-fitness-record-data-0000001131831088
        return when (activityType) {
            // 户外跑
            HuaweiActivityType.RUNNING.number -> 1
            // 室内跑
            HuaweiActivityType.RUNNING_MACHINE.number,
            HuaweiActivityType.RUNNING_INDOOR.number -> 2
            // 徒步
            HuaweiActivityType.ON_FOOT.number,
            HuaweiActivityType.WALKING.number,
            HuaweiActivityType.WALKING_INDOOR.number,
            HuaweiActivityType.HIKING.number -> 3
            // 越野
            HuaweiActivityType.ORIENTEERING.number,
            HuaweiActivityType.MOUNTAIN_CLIMBING.number,
            HuaweiActivityType.CROSS_COUNTRY_RACE.number -> 4
            // 骑车
            HuaweiActivityType.CYCLING.number,
            HuaweiActivityType.CYCLING_INDOOR.number,
            HuaweiActivityType.SPINNING.number,
            HuaweiActivityType.BMX.number -> 5
            // 游泳
            HuaweiActivityType.SWIMMING.number,
            HuaweiActivityType.SWIMMING_OPEN_WATER.number,
            HuaweiActivityType.SWIMMING_POOL.number,
            HuaweiActivityType.FREEDIVING.number -> 6

            else -> 100
        }
    }
}