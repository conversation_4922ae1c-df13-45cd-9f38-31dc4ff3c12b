package com.gusto.upload.core.kafka.comsumer

import com.alicp.jetcache.Cache
import com.alicp.jetcache.anno.CreateCache
import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.core.huawei.HuaweiActivityService
import com.gusto.upload.core.service.NotifyRecordService
import com.gusto.upload.core.service.user.NewUserRunService
import com.gusto.upload.model.entity.message.HuaweiActivityMessage
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component

/**
 * 华为-活动消费者
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
@Component
class HuaweiActivityConsumer {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var activityService: HuaweiActivityService

    @Autowired
    lateinit var notifyRecordService: NotifyRecordService

    @Autowired
    lateinit var runService: NewUserRunService

    @CreateCache(name = "HuaweiActivityConsumer.upsertUserRunCache.", expire = 3600)
    private lateinit var upsertUserRunCache: Cache<String, String>

    /**
     * 处理订阅事件
     */
    @KafkaListener(
        topics = [HuaweiActivityMessage.SUBSCRIPTION_TOPIC],
        groupId = HuaweiActivityMessage.SUBSCRIPTION_TOPIC + "#UpsertUserRun",
        concurrency = "2"
    )
    fun upsertUserRun(message: HuaweiActivityMessage) {
        log.info("[upsertUserRun] get message = {}", message)
        val cache = upsertUserRunCache.GET(message.buildUniqueKey())
        if (cache.isSuccess) {
            log.info("[upsertUserRun] cache exist, return")
            checkExist(message)
            return
        } else {
            upsertUserRunCache.PUT(message.buildUniqueKey(), message.formatToJson())
        }
        if (checkExist(message)) return
        activityService.syncUserActivityRecordListBySubscription(
            message.userId,
            message.startTime,
            message.endTime,
            message.activityType
        )
        notifyRecordService.removeByUniqueKey(message.buildUniqueKey())
        upsertUserRunCache.REMOVE(message.buildUniqueKey())
        log.info("[upsertUserRun] handle success")
    }

    private fun checkExist(message: HuaweiActivityMessage): Boolean {
        val dbRun = runService.getListByUserIdAndRunId(message.userId, message.startTime.toLong())
            .firstOrNull { it.deviceType == 304 }
        if (dbRun != null) {
            notifyRecordService.removeByUniqueKey(message.buildUniqueKey())
            upsertUserRunCache.REMOVE(message.buildUniqueKey())
            val logPre = "[checkExist] " +
                    "userId=${message.userId}, " +
                    "deviceId=1, " +
                    "deviceType=304, " +
                    "activityId=${message.startTime}"
            log.debug("$logPre, has been inserted into the database, return false")
            return true
        } else {
            return false
        }
    }

}
