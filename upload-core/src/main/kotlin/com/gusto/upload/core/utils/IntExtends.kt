package com.gusto.upload.core.utils

/**
 * 将秒转换成时分秒
 */
fun Int.formatDuration(): String {
    val hours = this / 3600
    val minutes = (this - hours * 3600) / 60
    val seconds = this - hours * 3600 - minutes * 60
    val hoursString = if (hours <= 9) "0${hours}" else hours.toString()
    val minutesString = if (minutes <= 9) "0${minutes}" else minutes.toString()
    val secondsString = if (seconds <= 9) "0${seconds}" else seconds.toString()
    return "$hoursString:$minutesString:$secondsString"
}