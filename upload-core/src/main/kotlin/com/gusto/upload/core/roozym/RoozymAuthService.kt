package com.gusto.upload.core.roozym

import com.gusto.upload.core.roozym.util.toUserAuthInfo
import com.gusto.upload.model.entity.roozym.RoozymProperties
import com.gusto.upload.model.entity.roozym.req.RoozymUpsertSubscriptionReq
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.net.URLEncoder
import java.util.concurrent.Executor
import javax.annotation.Resource

/**
 *
 * <AUTHOR>
 * @since 2022/6/9
 */
@Service
class RoozymAuthService {
    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var subscriptionService: RoozymSubscriptionService

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    @Autowired
    lateinit var authInfoService: RoozymUserAuthInfoService

    @Autowired
    lateinit var roozymClientService: RoozymClientService

    @Autowired
    lateinit var config: RoozymProperties

    /**
     * 使用授权码Code获取AT
     * PS: AT一个小时有效
     */
    fun upsertAccessTokenByCode(code: String, userId: Long): Boolean {
        log.info("[Roozym:upsertAccessTokenByCode] userId = $userId Roozym Auth start")
        val rsp = roozymClientService.getAccessToken(
            code = URLEncoder.encode(code, "UTF-8"),
            clientId = config.clientId,
            clientSecret = config.clientSecret,
            redirectUri = config.redirectUri
        )
        log.debug("[Roozym:upsertAccessTokenByCode] userId = $userId getAccessToken = ${rsp.result}")
        if (rsp.isError) {
            log.warn("[Roozym:upsertAccessTokenByCode] userId = $userId, code = $code get AT error, error = ${rsp.content}")
            return false
        }
        val authInfo = rsp.result.toUserAuthInfo(userId)

        // 查询此如骏账号是否绑定了多个第一赛道账号
        val dbAuthInfoList = authInfoService.getListByOpenId(authInfo.openId)
        // 筛选出另外的第一赛道账号的如骏授权信息
        val otherUserAuthInfo = dbAuthInfoList.filter { it.userId != userId }
        if (otherUserAuthInfo.isNotEmpty()) {
            // 踢掉另外的第一赛道账号授权
            otherUserAuthInfo.forEach {
                cancelAuth(it.userId)
                authInfoService.deleteWithCache(it)
            }
        }

        // 查询用户是否已存在授权信息
        var dbAuthInfo = authInfoService.getOneByUserIdOrNullWithoutFilter(userId, authInfo.openId)
        if (dbAuthInfo == null) {
            log.debug("[Roozym:upsertAccessTokenByCode] userId = ${userId} success first upsert")
            // 没有，插入新的授权信息
            dbAuthInfo = authInfo
            authInfoService.create(dbAuthInfo)
        } else {
            log.debug("[Roozym:upsertAccessTokenByCode] userId = ${userId} success updateMessage")
            // 有，更新原有授权信息
            dbAuthInfo.updateAccessToken(authInfo.accessToken)
            dbAuthInfo.refreshToken = authInfo.refreshToken
            dbAuthInfo.expiresIn = authInfo.expiresIn
            dbAuthInfo.refreshTokenExpires = false
            dbAuthInfo.deleted = false
            authInfoService.updateWithCache(dbAuthInfo)
        }

        // 异步新增订阅记录
        asyncExecutor.execute {
            subscriptionService.upsert(dbAuthInfo)
        }

        return true
    }

    /**
     * 取消授权
     */
    fun cancelAuth(userId: Long): Boolean {
        log.info("[Roozym:cancelAuth] userId = ${userId} start cancel")
        val uncheckAuthInfo = authInfoService.getOneByUserId(userId)

        // 取消订阅记录
        val req = RoozymUpsertSubscriptionReq()
        req.openId = uncheckAuthInfo.openId
        val eventTypeList = emptyList<String>().toMutableList()
        eventTypeList.add("event_sport_add")
        eventTypeList.add("event_auth_cancel")
        req.eventType = eventTypeList
        req.status = 2
        val (rsp, authInfo) = roozymClientService.upsertSubscription(req, uncheckAuthInfo)
        if (rsp.isError) {
            log.warn("[upsert] req error, error = ${rsp.content}")
            authInfoService.deleteWithCache(authInfo)
        }

        // 取消授权
        val (cancelRsp, lastAuthInfo) = roozymClientService.cancelAuth(
            authInfo
        )

        if (cancelRsp.isError) {
            authInfoService.deleteWithCache(authInfo)
            log.warn("[roozym:cancelAuth] user cancelAuth Error, userId = $userId, error = ${cancelRsp.content}")
            return false
        }

        if (cancelRsp.result.resultCode != 1L) {
            log.warn("[roozym:cancelAuth] user cancelAuth Error, userId = $userId,  result error = ${cancelRsp.result.resultMsg}")
            return false
        }

        // 更新数据库
        authInfoService.deleteWithCache(lastAuthInfo)
        return true
    }
}
