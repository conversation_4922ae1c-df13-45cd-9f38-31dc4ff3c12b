package com.gusto.upload.core.wanbao

import cn.hutool.core.codec.Base64
import cn.hutool.core.util.StrUtil
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwError
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.wanbao.WanbaoProperties
import com.gusto.upload.model.entity.wanbao.WanbaoUserAuthInfo
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.concurrent.Executor
import javax.annotation.Resource

/**
 * <AUTHOR>
 * @since 2024-09-12
 */
@Service
class WanbaoCommonService {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authInfoService: WanbaoUserAuthInfoService

    @Autowired
    lateinit var config: WanbaoProperties

    @Autowired
    lateinit var wanbaoClient: WanbaoClient

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    /**
     * 检查用户AT是否过期，如果过期则刷新
     */
    fun checkOrRefreshAccessToken(authInfo: WanbaoUserAuthInfo) {
        val result = refreshAccessToken(authInfo)
        if (!result) {
            authInfoService.removeById(authInfo)
            throwError(
                ServiceException(
                    UploadErrorCode.WANBAO_REFRESH_ACCESS_TOKEN_ERROR.code,
                    "获取授权失败，请返回并重新绑定"
                )
            )
        }
    }

    /**
     * 刷新AT
     */
    private fun refreshAccessToken(authInfo: WanbaoUserAuthInfo): Boolean {
        val rsp = wanbaoClient.refreshAccessToken(
            refreshToken = authInfo.refreshToken,
            headers = buildHeaders(null)
        )
        if (rsp.isError) {
            log.warn("[refreshAccessToken] refresh AT error, error = ${rsp.content}")
            return false
        }
        authInfo.refreshToken = rsp.result.refreshToken
        authInfo.accessToken = rsp.result.accessToken
        authInfo.accessTokenUpdateTime = Instant.now()
        authInfoService.updateById(authInfo)
        return true
    }

    protected fun buildHeaders(accessToken: String?): Map<String, Any> {
        val headers = HashMap<String, Any>()
        val authString = Base64.encode("${config.clientId}:${config.clientSecret}")
        headers["Authorization"] = "Basic $authString"
        if (StrUtil.isNotEmpty(accessToken)) {
            headers["blade-auth"] = "bearer $accessToken"
        }
        return headers
    }

}
