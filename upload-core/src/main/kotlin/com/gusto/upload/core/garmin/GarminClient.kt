package com.gusto.upload.core.garmin

import com.dtflys.forest.annotation.Delete
import com.dtflys.forest.annotation.Get
import com.dtflys.forest.annotation.Header
import com.dtflys.forest.annotation.LogEnabled
import com.dtflys.forest.annotation.Post
import com.dtflys.forest.annotation.Query
import com.dtflys.forest.annotation.Retry
import com.dtflys.forest.http.ForestResponse
import com.gusto.upload.model.entity.garmin.rsp.CancelAuthRsp
import com.gusto.upload.model.entity.garmin.rsp.GarminActivityDetailRsp
import com.gusto.upload.model.entity.garmin.rsp.GarminUserIdRsp

/**
 * 佳明-REST API调用入口
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@Retry(maxRetryCount = "5", maxRetryInterval = "100")
interface GarminClient {

    /**
     * 获取RequestToken
     */
    @Post("https://connectapi.garmin.cn/oauth-service/oauth/request_token")
    fun getRequestToken(
        @Header headers: Map<String, Any>
    ): ForestResponse<String>

    /**
     * 获取AccessToken
     */
    @Post("https://connectapi.garmin.cn/oauth-service/oauth/access_token")
    fun getAccessToken(
        @Header headers: Map<String, Any>
    ): ForestResponse<String>

    /**
     * 获取用户ID
     */
    @Get("https://gcs-wellness.garmin.cn/wellness-api/rest/user/id")
    fun getGarminUserId(
        @Header headers: Map<String, Any>
    ): ForestResponse<GarminUserIdRsp>

    /**
     * 取消授权
     */
    @Delete("https://gcs-wellness.garmin.cn/wellness-api/rest/user/registration")
    fun cancelAuth(
        @Header headers: Map<String, Any>
    ): ForestResponse<CancelAuthRsp>

    /**
     * 获取活动详情列表
     */
    @LogEnabled(logResponseContent = false)
    @Get("https://gcs-wellness.garmin.cn/wellness-api/rest/activityDetails")
    fun getActivityDetailList(
        @Query("uploadStartTimeInSeconds") startTime: Long,
        @Query("uploadEndTimeInSeconds") endTime: Long,
        @Header header: Map<String, Any>
    ): ForestResponse<List<GarminActivityDetailRsp>>

    /**
     * 海外获取RequestToken
     */
    @Post("https://connectapi.garmin.com/oauth-service/oauth/request_token")
    fun getRequestTokenForGlobal(
        @Header headers: Map<String, Any>
    ): ForestResponse<String>

    /**
     * 海外获取AccessToken
     */
    @Post("https://connectapi.garmin.com/oauth-service/oauth/access_token")
    fun getAccessTokenForGlobal(
        @Header headers: Map<String, Any>
    ): ForestResponse<String>

    /**
     * 海外获取用户ID
     */
    @Get("https://apis.garmin.com/wellness-api/rest/user/id")
    fun getGarminUserIdForGlobal(
        @Header headers: Map<String, Any>
    ): ForestResponse<GarminUserIdRsp>

    /**
     * 海外取消授权
     */
    @Delete("https://apis.garmin.com/wellness-api/rest/user/registration")
    fun cancelAuthForGlobal(
        @Header headers: Map<String, Any>
    ): ForestResponse<CancelAuthRsp>

    /**
     * 海外获取活动详情列表
     */
    @LogEnabled(logResponseContent = false)
    @Get("https://apis.garmin.com/wellness-api/rest/activityDetails")
    fun getActivityDetailListForGlobal(
        @Query("uploadStartTimeInSeconds") startTime: Long,
        @Query("uploadEndTimeInSeconds") endTime: Long,
        @Header header: Map<String, Any>
    ): ForestResponse<List<GarminActivityDetailRsp>>

}
