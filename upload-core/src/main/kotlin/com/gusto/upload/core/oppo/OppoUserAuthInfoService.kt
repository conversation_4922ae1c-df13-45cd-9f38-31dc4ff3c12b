package com.gusto.upload.core.oppo

import cn.hutool.core.util.StrUtil
import com.baomidou.mybatisplus.core.toolkit.Wrappers
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwError
import com.gusto.upload.core.dao.oppo.OppoUserAuthInfoDao
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.oppo.OppoUserAuthInfo
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Service
class OppoUserAuthInfoService : ServiceImpl<OppoUserAuthInfoDao, OppoUserAuthInfo>() {

    /**
     * 根据OPPO用户ID获取列表
     */
    fun getListByOpenId(openId: String): List<OppoUserAuthInfo> {
        if (StrUtil.isEmpty(openId)) {
            return emptyList()
        }
        val wrapper = Wrappers.lambdaQuery<OppoUserAuthInfo>()
            .eq(OppoUserAuthInfo::getOpenId, openId)
        return list(wrapper)
    }

    /**
     * 根据用户ID获取列表
     */
    fun getListByUserId(userId: Long): List<OppoUserAuthInfo> {
        val wrapper = Wrappers.lambdaQuery<OppoUserAuthInfo>()
            .eq(OppoUserAuthInfo::getUserId, userId)
        return list(wrapper)
    }

    /**
     * 根据OPPO用户ID获取
     */
    fun getOneByOpenIdOrNull(openId: String): OppoUserAuthInfo? {
        val wrapper = Wrappers.lambdaQuery<OppoUserAuthInfo>()
            .eq(OppoUserAuthInfo::getOpenId, openId)
        val authInfo = getOne(wrapper)
        if (authInfo == null) {
            log.warn("[getOneByOpenId] openId = $openId not found")
            // throwError(ServiceException(UploadErrorCode.COROS_USER_AUTH_INFO_NOT_FOUND))
        }
        return authInfo
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserId(userId: Long): OppoUserAuthInfo {
        val wrapper = Wrappers.lambdaQuery<OppoUserAuthInfo>()
            .eq(OppoUserAuthInfo::getUserId, userId)
        val authInfo = getOne(wrapper)
        if (authInfo == null) {
            log.error("[getOneByUserId] userId = $userId not found")
            throwError(ServiceException(UploadErrorCode.OPPO_USER_AUTH_INFO_NOT_FOUND))
        }
        return authInfo!!
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserIdOrNull(userId: Long): OppoUserAuthInfo? {
        val wrapper = Wrappers.lambdaQuery<OppoUserAuthInfo>()
            .eq(OppoUserAuthInfo::getUserId, userId)
        return getOne(wrapper)
    }

}
