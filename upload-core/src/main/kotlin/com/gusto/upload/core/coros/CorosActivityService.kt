package com.gusto.upload.core.coros

import cn.hutool.core.io.IoUtil
import cn.hutool.core.util.StrUtil
import cn.hutool.http.HttpUtil
import com.dtflys.forest.Forest
import com.dtflys.forest.http.ForestResponse
import com.garmin.fit.EventMesg
import com.garmin.fit.EventType
import com.garmin.fit.FitDecoder
import com.garmin.fit.FitMessages
import com.garmin.fit.FitRuntimeException
import com.garmin.fit.RecordMesg
import com.gusto.framework.core.util.time.minusDays
import com.gusto.upload.core.application.user.NewUserRunApplicationService
import com.gusto.upload.core.common.PushService
import com.gusto.upload.core.service.NotifyRecordService
import com.gusto.upload.core.service.user.NewUserRunService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.util.NewUserRunMapUtils
import com.gusto.upload.core.util.format
import com.gusto.upload.core.utils.JsonUtils
import com.gusto.upload.core.utils.round
import com.gusto.upload.core.utils.roundTwo
import com.gusto.upload.model.entity.NotifyRecord
import com.gusto.upload.model.entity.coros.CorosActivity
import com.gusto.upload.model.entity.coros.CorosActivityTriathlonItem
import com.gusto.upload.model.entity.coros.CorosUserAuthInfo
import com.gusto.upload.model.entity.response.ManualSyncRsp
import com.gusto.upload.model.entity.user.NewUserRun
import com.gusto.upload.model.entity.user.NewUserRunDetailAltitudeItem
import com.gusto.upload.model.entity.user.NewUserRunDetailHeartRateItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLapItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLocationItem
import com.gusto.upload.model.entity.user.NewUserRunDetailStepRateItem
import okhttp3.internal.closeQuietly
import org.apache.commons.io.FileUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.io.IOException
import java.io.InputStream
import java.time.Duration
import java.time.Instant
import kotlin.math.max
import kotlin.math.min
import kotlin.math.pow
import kotlin.math.roundToInt


/**
 * 高驰-活动
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Service
class CorosActivityService : CorosCommonService() {

    private val log = LoggerFactory.getLogger(javaClass)
    private val objectMapper = JsonUtils.initObjectMapper(lowerCamelCase = true)
    private val halfMarathon = 21.0975
    private val marathon = 42.195

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var newRunApplicationService: NewUserRunApplicationService

    @Autowired
    lateinit var pushService: PushService

    @Autowired
    lateinit var mapUtils: NewUserRunMapUtils

    @Autowired
    lateinit var runService: NewUserRunService

    @Autowired
    lateinit var notifyRecordService: NotifyRecordService

    /**
     * 根据订阅事件同步活动列表
     */
    fun syncActivityBySubscription(activity: CorosActivity) {
        val authInfo = authInfoService.getOneByOpenIdOrNull(activity.openId) ?: return
        checkOrRefreshAccessToken(authInfo, true)
        // 创建跑步记录头部和明细
        createUserRunAndDetail(authInfo, listOf(activity))
    }

    /**
     * 手动同步活动列表
     */
    fun syncActivityListByManual(
        userId: Long,
        reqStartTime: Instant? = null,
        reqEndTime: Instant? = null,
        notifyRecord: NotifyRecord? = null
    ): ManualSyncRsp {
        val authInfo = authInfoService.getOneByUserId(userId)
        checkOrRefreshAccessToken(authInfo)
        val now = Instant.now()
        val startDate = reqStartTime?.format("yyyyMMdd")?.toInt()
            ?: Instant.ofEpochMilli(max(authInfo.createTime.toEpochMilli(), now.minusDays(7).toEpochMilli()))
                .format("yyyyMMdd").toInt()
        val endDate = reqEndTime?.format("yyyyMMdd")?.toInt()
            ?: now.format("yyyyMMdd").toInt()
        val rsp = corosClient.getActivityListByDate(
            authInfo.accessToken,
            authInfo.openId,
            startDate,
            endDate
        )
        val outRsp = ManualSyncRsp()
        outRsp.result = true
        outRsp.reason = "同步成功，请稍后在跑步记录页查看"
        if (rsp.isError) {
            log.error("[syncActivityListByManual] userId = $userId, get activity list error, response content = ${rsp.content}")
            outRsp.result = false
            outRsp.reason = "同步失败，请稍后再试"
            return outRsp
        }
        if (rsp.result == null || rsp.result.data == null || rsp.result.data.isEmpty()) {
            outRsp.reason = "暂无运动记录可同步"
            if (notifyRecord != null) {
                notifyRecordService.removeByUniqueKey(notifyRecord.uniqueKey)
            }
            return outRsp
        }
        // 创建跑步记录头部和明细
        asyncExecutor.execute {
            log.info("[syncActivityListByManual] userId = $userId, sync async start")
            createUserRunAndDetail(authInfo, rsp.result.data, notifyRecord)
            log.info("[syncActivityListByManual] userId = $userId, sync async success")
        }
        return outRsp
    }

    /**
     * 创建跑步记录并上传到APP
     */
    private fun createUserRunAndDetail(
        authInfo: CorosUserAuthInfo,
        activityList: List<CorosActivity>,
        notifyRecord: NotifyRecord? = null
    ) {
        var syncSuccessCount = 0
        val runList = mutableListOf<NewUserRun>()
        activityList.forEach { activity ->
            if (activity.triathlonItemList != null && activity.triathlonItemList.isNotEmpty()) {
                activity.triathlonItemList.forEachIndexed { index, item ->
                    val run = buildNewUserRunFromItem(authInfo, activity, item, index)
                    val createSuccess = uploadRunAndDetail(run, item.fitUrl)
                    notifyRecordService.removeByUniqueKey(run.buildUniqueKey())
                    if (!createSuccess) return@forEachIndexed
                    syncSuccessCount++
                    runList.add(run)
                }
            } else {
                val run = buildNewUserRun(authInfo, activity)
                val createSuccess = uploadRunAndDetail(run, activity.fitUrl)
                notifyRecordService.removeByUniqueKey(run.buildUniqueKey())
                if (!createSuccess) return@forEach
                syncSuccessCount++
                runList.add(run)
            }
        }
        if (notifyRecord != null) {
            if (runList.all { it.buildUniqueKey() != notifyRecord.uniqueKey }) {
                notifyRecordService.removeByUniqueKey(notifyRecord.uniqueKey)
            }
        }
        log.info("[createUserRunAndDetail] userId = ${authInfo.userId}, sync success, count = $syncSuccessCount")
        if (syncSuccessCount == 0) return
        asyncExecutor.execute {
            val latestRun = runList.maxByOrNull { it.startTime }
            if (latestRun != null) {
                pushService.notifySyncSuccess(latestRun, syncSuccessCount, authInfo.userId)
            }
        }
    }

    /**
     * 上传跑步记录和明细
     */
    private fun uploadRunAndDetail(run: NewUserRun, fitFileUrl: String?): Boolean {
        val dbRun = runService.getListByUserIdAndRunId(run.userId, run.activityId).firstOrNull { it.deviceType == 303 }
        if (dbRun != null) {
            val logPre = "[handlerAndCreate] " +
                    "userId=${run.userId}, " +
                    "deviceId=${run.deviceId}, " +
                    "deviceType=${run.deviceType}, " +
                    "deviceModel=${run.deviceModel}, " +
                    "activityId=${run.activityId}"
            log.debug("$logPre, has been inserted into the database, return false")
            return false
        }
        val fitMessages = getFitMessagesByForest(fitFileUrl) ?: return false
        val session = fitMessages.sessionMesgs.first()
        val recordList = fitMessages.recordMesgs.sortedBy { it.timestamp }
        run.apply {
            if (totalStep == 0) {
                totalStep = (session.totalStrides?.toInt() ?: 0) * 2
            }
            totalDuration = if (totalDuration == 0) {
                session.totalTimerTime.toInt()
            } else {
                totalDuration
            }
            if (startTime == 0L || endTime == 0L) {
                startTime = session.startTime.date.time
                endTime = startTime + session.totalElapsedTime.roundToInt() * 1000
            }
            averagePace = if (session.avgSpeed != null && session.avgSpeed > 0) {
                (1000 / session.avgSpeed).toDouble().roundToInt().toDouble()
            } else {
                0.0
            }
            maxPace = if (session.maxSpeed != null && session.maxSpeed > 0) {
                (1000 / session.maxSpeed).toDouble().roundToInt().toDouble()
            } else {
                0.0
            }
            averageHeartRate = session.avgHeartRate?.toInt() ?: 0
            if (averageHeartRate == 0) {
                averageHeartRate = recordList.mapNotNull { it.heartRate }.average().toInt()
            }
            maxHeartRate = session.maxHeartRate?.toInt() ?: 0
            maxStepRate = (session.maxCadence ?: 0) * 2
            averageStride = (session.avgStepLength?.toDouble() ?: 0.0) / 10
//            minAltitude = recordList.mapNotNull { it.altitude }.minOrNull()?.toDouble() ?: 0.0
//            maxAltitude = recordList.mapNotNull { it.altitude }.maxOrNull()?.toDouble() ?: 0.0
            minAltitude = 0.0
            maxAltitude = 0.0
            totalAscent = session.totalAscent?.toDouble() ?: 0.0
            totalDescent = session.totalDescent?.toDouble() ?: 0.0
            partTimeKmList = buildPartTimeMap(recordList, fitMessages.eventMesgs.sortedBy { it.timestamp }, this)
        }
        var lapList = mutableListOf<NewUserRunDetailLapItem>()
        fitMessages.lapMesgs.forEach {
            val lap = NewUserRunDetailLapItem()
            lap.distance = it.totalDistance?.toDouble()?.roundTwo() ?: 0.0
            lap.duration = it.totalTimerTime.roundToInt()
            lapList.add(lap)
        }
        if (lapList.all { it.distance == 0.0 }) {
            lapList = mutableListOf()
        }
        val locationList = mutableListOf<NewUserRunDetailLocationItem>()
        val heartRateList = mutableListOf<NewUserRunDetailHeartRateItem>()
        val stepRateList = mutableListOf<NewUserRunDetailStepRateItem>()
        val altitudeList = mutableListOf<NewUserRunDetailAltitudeItem>()
        recordList
            .forEach {
                val timestamp = it.timestamp.date.time
                val speedValue = it.speed?.toDouble()?.roundTwo() ?: 0.0
                val location = NewUserRunDetailLocationItem()
                location.timestamp = timestamp
                location.latitude = if (it.positionLat != null) {
                    (it.positionLat.toDouble() * (180.0 / (2.0.pow(31)))).round(8)
                } else {
                    0.0
                }
                location.longitude = if (it.positionLong != null) {
                    (it.positionLong.toDouble() * (180.0 / (2.0.pow(31)))).round(8)
                } else {
                    0.0
                }
                location.speed = speedValue
                locationList.add(location)

                val heartRate = NewUserRunDetailHeartRateItem()
                heartRate.timestamp = timestamp
                heartRate.heartRate = it.heartRate?.toInt() ?: 0
                heartRateList.add(heartRate)

                val stepRate = NewUserRunDetailStepRateItem()
                stepRate.timestamp = timestamp
                stepRate.stepRate = (it.cadence ?: 0) * 2
                stepRateList.add(stepRate)

                val altitude = NewUserRunDetailAltitudeItem()
                altitude.timestamp = timestamp
                altitude.altitude = it.altitude?.toDouble() ?: 0.0
                altitudeList.add(altitude)

                run.maxAltitude = max(run.maxAltitude, altitude.altitude)
                run.minAltitude =
                    if (run.minAltitude == 0.0) altitude.altitude else min(run.minAltitude, altitude.altitude)
            }

        val createSuccess = newRunApplicationService.handlerAndCreate(run)
        if (createSuccess) {
            val detail = mapUtils.toNewUserRunDetail(run)
            detail.lapList = lapList
            detail.locationList = locationList
            detail.heartRateList = heartRateList.filter { it.heartRate > 0 }
            detail.stepRateList = stepRateList
            detail.altitudeList = altitudeList
            detail.partTimeKmList = run.partTimeKmList
            asyncExecutor.execute {
                // 上传到七牛
//                qiniuService.uploadString(
//                    objectMapper.writeValueAsString(detail),
//                    "${run.trackFile}.gzip"
//                )
//                qiniuService.uploadString(
//                    objectMapper.writeValueAsString(fitMessages),
//                    "coros_activity_detail_${run.trackFile}.gzip"
//                )
                qiniuService.uploadObject(
                    detail,
                    "${run.trackFile}.gzip"
                )
                qiniuService.uploadUrl(fitFileUrl!!, "coros_activity_detail_${run.trackFile}.fit")
            }
            return true
        }
        return false
    }

    /**
     * 创建每公里用时
     */
    private fun buildPartTimeMap(
        recordList: List<RecordMesg>,
        eventList: List<EventMesg>,
        run: NewUserRun
    ): Map<String, Int> {
        var startTime = 0L
        var pauseTime = 0L
        var isPaused = false
        var lastTimestamp = 0L
        var halfMarathonFlag = false
        var marathonFlag = false
        var currentLap = 1
        var currentDuration = 0L
        val partTimeKmMap = mutableMapOf<String, Int>()
        val eventMap = eventList.associateBy { it.timestamp.date.time }
        recordList.forEach {
            val currentTimestamp = it.timestamp.date.time
            if (startTime == 0L) {
                // 找到第一个开始事件
                val startEvent = eventList.first()
                startTime = startEvent.timestamp.date.time
                lastTimestamp = currentTimestamp
            }

            // 计算duration
            val timeDiff = currentTimestamp - lastTimestamp
            if (isPaused) {
                pauseTime = 2L
            } else {
                currentDuration += timeDiff / 1000
            }
            lastTimestamp = currentTimestamp

            // 处理暂停事件
            if (eventMap.containsKey(currentTimestamp)) {
                val event = eventMap[currentTimestamp]!!
                when (event.eventType) {
                    EventType.STOP, EventType.STOP_ALL -> {
                        isPaused = true
                        pauseTime = 0L
                    }

                    EventType.START -> {
                        isPaused = false
                        currentDuration -= pauseTime
                        pauseTime = 0L
                    }

                    else -> {}
                }
            }

            // 计算分段累计用时
            val distance = it.distance ?: 0F
            if (!halfMarathonFlag && distance >= halfMarathon * 1000) {
                val halfMarathonItem = partTimeKmMap[halfMarathon.toString()]
                if (halfMarathonItem == null) {
                    partTimeKmMap[halfMarathon.toString()] = currentDuration.toInt()
                    halfMarathonFlag = true
                }
            } else if (!marathonFlag && distance >= marathon * 1000) {
                val marathonItem = partTimeKmMap[marathon.toString()]
                if (marathonItem == null) {
                    partTimeKmMap[marathon.toString()] = currentDuration.toInt()
                    marathonFlag = true
                }
            } else {
                if (distance >= 1000) {
                    if (distance / 1000 >= currentLap) {
                        partTimeKmMap[currentLap.toDouble().toString()] = currentDuration.toInt()
                        currentLap++
                    }
                }
            }
        }
        // 适配“recordMesgs最后一个点的距离<=sessionMesgs的距离”
        if (run.totalDistance - (currentLap - 1) * 1000 > 1000) {
            partTimeKmMap[currentLap.toDouble().toString()] = run.totalDuration
        }
        return partTimeKmMap
    }

    /**
     * 创建新跑步记录
     */
    private fun buildNewUserRunFromItem(
        authInfo: CorosUserAuthInfo,
        activity: CorosActivity,
        item: CorosActivityTriathlonItem,
        index: Int
    ): NewUserRun {
        val run = NewUserRun().apply {
            activityId = activity.startTime * 1000 + index
            userId = authInfo.userId
            deviceId = 3
            deviceType = 303
            deviceModel = activity.deviceName
            activityType = formatActivityType(item.mode, item.subMode)
            startTime = 0
            endTime = 0
            totalDistance = item.distance
            totalDuration = item.duration
            totalStep = item.step ?: 0
            totalCalorie = (item.calorie / 1000).roundTwo()
            averagePace = 0.0
            maxPace = 0.0
//            averageHeartRate = activity.avgHeartRate
            averageHeartRate = 0
            maxHeartRate = 0
            averageStepRate = 0
            maxStepRate = 0
            averageStride = 0.0
            minAltitude = 0.0
            maxAltitude = 0.0
            totalAscent = 0.0
            totalDescent = 0.0
            weather = ""
            province = ""
            city = ""
            temperature = ""
            humidity = ""
            partTimeKmList = mutableMapOf()
            thirdActivityId = "${activity.labelId}-$index"
            thirdActivityType = "${item.mode}-${item.subMode}"
            abnormal = 9
            externalSportType = "${item.mode}-${item.subMode}"
            mapTrackImage = ""
            trackImage = ""
            fitUrl = item.fitUrl
        }
        return run
    }

    /**
     * 创建新跑步记录
     */
    private fun buildNewUserRun(authInfo: CorosUserAuthInfo, activity: CorosActivity): NewUserRun {
        val run = NewUserRun().apply {
            activityId = activity.startTime * 1000
            userId = authInfo.userId
            deviceId = 3
            deviceType = 303
            deviceModel = activity.deviceName
            activityType = formatActivityType(activity.mode, activity.subMode)
            startTime = activity.startTime * 1000
            endTime = activity.endTime * 1000
            totalDistance = activity.distance
//            totalDuration = (activity.endTime - activity.startTime).toInt()
            totalDuration = 0
            totalStep = activity.step ?: 0
            totalCalorie = (activity.calorie / 1000).roundTwo()
            averagePace = activity.avgSpeed.toDouble()
            maxPace = 0.0
//            averageHeartRate = activity.avgHeartRate
            averageHeartRate = 0
            maxHeartRate = 0
            averageStepRate = activity.avgFrequency
            maxStepRate = 0
            averageStride = 0.0
            minAltitude = 0.0
            maxAltitude = 0.0
            totalAscent = 0.0
            totalDescent = 0.0
            weather = ""
            province = ""
            city = ""
            temperature = ""
            humidity = ""
            partTimeKmList = mutableMapOf()
            thirdActivityId = activity.labelId
            thirdActivityType = "${activity.mode}-${activity.subMode}"
            abnormal = 9
            externalSportType = "${activity.mode}-${activity.subMode}"
            mapTrackImage = ""
            trackImage = ""
            fitUrl = activity.fitUrl
        }
        return run
    }

    /**
     * 获取并解析fit文件
     */
    fun getFitMessages(fitFileUrl: String?): FitMessages? {
        if (StrUtil.isEmpty(fitFileUrl)) {
            log.warn("[getFitMessages] url = $fitFileUrl, return null")
            return null
        }
        val startTime = System.currentTimeMillis()
        val response = HttpUtil.createGet(fitFileUrl)
            .timeout(-1)
            .execute()
        val endTime = System.currentTimeMillis()
        if (!response.isOk) {
            log.error("[getFitMessages] HttpException downloading file: $fitFileUrl, status = ${response.status}")
            response.contentLength()
        }
        val downloadTime = Duration.ofMillis(endTime - startTime).toMillis()
        val fileSize = (response.contentLength().toDouble() / FileUtils.ONE_KB).roundTwo()
        log.debug("[getFitMessages] url = $fitFileUrl, download time = $downloadTime ms")
        log.debug("[getFitMessages] url = $fitFileUrl, size = $fileSize kb")
        val fitDecoder = FitDecoder()
        val inputStream = response.bodyStream()
        try {
            return fitDecoder.decode(inputStream)
        } catch (e: IOException) {
            log.error("[getFitMessages] IOException opening file: $fitFileUrl")
        } catch (e: FitRuntimeException) {
            log.error("[getFitMessages] FitRuntimeException decoding file: ${e.message}")
        } catch (e: Exception) {
            log.error("[getFitMessages] Exception decoding file: ${e.message}")
        } finally {
            inputStream.closeQuietly()
//            println(fitDecoder.decode(inputStream).sessionMesgs.formatToJson())
//            println(fitDecoder.decode(response.bodyStream()).sessionMesgs.formatToJson())
//            println("done")
        }
        return null
    }

    /**
     * 获取并解析fit文件
     */
    fun getFitMessagesByForest(fitFileUrl: String?): FitMessages? {
        if (StrUtil.isEmpty(fitFileUrl)) {
            log.warn("[getFitMessages] url = $fitFileUrl, return null")
            return null
        }
        val startTime = System.currentTimeMillis()
        val response = Forest.get(fitFileUrl)
            .connectTimeout(3000)
            .readTimeout(3000)
            .maxRetryCount(5)
            .maxRetryInterval(100)
            .execute(ForestResponse::class.java)
        val endTime = System.currentTimeMillis()
        if (!response.isSuccess) {
            log.error("[getFitMessages] HttpException downloading file: $fitFileUrl, status = ${response.statusCode}")
            response.contentLength
        }
        val downloadTime = Duration.ofMillis(endTime - startTime).toMillis()
        val fileSize = (response.byteArray.size.toDouble() / FileUtils.ONE_KB).roundTwo()
        log.debug("[getFitMessages] url = $fitFileUrl, download time = $downloadTime ms")
        log.debug("[getFitMessages] url = $fitFileUrl, size = $fileSize kb")
        val fitDecoder = FitDecoder()
        var inputStream: InputStream? = null
        try {
            inputStream = response.inputStream
            return fitDecoder.decode(inputStream)
        } catch (e: IOException) {
            log.error("[getFitMessages] IOException opening file: $fitFileUrl")
        } catch (e: FitRuntimeException) {
            log.error("[getFitMessages] FitRuntimeException decoding file: ${e.message}")
        } catch (e: Exception) {
            log.error("[getFitMessages] Exception decoding file: ${e.message}")
        } finally {
            IoUtil.close(inputStream)

            val runtime = Runtime.getRuntime()
            val totalMemory = runtime.totalMemory() / 1024 / 1024 // 总内存
            val maxMemory = runtime.maxMemory() / 1024 / 1024 // 最大内存
            val freeMemory = runtime.freeMemory() / 1024 / 1024 // 可用内存
            val usedMemory = totalMemory - freeMemory // 已使用内存
            // 内存使用率
            val memoryRate = (usedMemory.toDouble() / maxMemory.toDouble() * 100).roundTwo()
            log.debug("[getFitMessages] 总内存 = $maxMemory MB | 最大内存 = $totalMemory MB | 已使用内存 = $usedMemory MB | 可用内存 = $freeMemory MB | 内存使用率 =  $memoryRate%")

//            println(fitDecoder.decode(inputStream).sessionMesgs.formatToJson())
//            println(fitDecoder.decode(response.inputStream).sessionMesgs.formatToJson())
//            println("done")
        }
        return null
    }

    /**
     * 转换活动类型
     *
     * 运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它
     */
    private fun formatActivityType(mode: Int, subMode: Int): Int {
        return when (mode) {
            8 -> if (subMode == 1) 1 else 2 // 室外跑步/室内跑步
            9 -> 5 // 室外骑行/室内骑行
            10 -> 6 // 室外游泳/室内游泳
            13 -> 100 // 铁人三项/组合运动/登山滑雪
            14 -> 3 // 登山
            15 -> 4 // 越野跑
            16 -> 3 // 徒步
            18 -> 100 // 室外有氧运动/室内有氧运动
            19 -> 100 // 越野滑雪
            20 -> 1 // 操场跑圈
            21 -> 100 // 双板滑雪/单板滑雪
            22 -> 100 // 战斗机模式
            23 -> 100 // 力量训练
            24 -> 100 // 赛艇/划船机
            25 -> 100 // 皮划艇
            26 -> 100 // 桨板
            27 -> 100 // 帆板
            28 -> 100 // 竞速帆板
            29 -> 3 // 登山滑雪
            31 -> 3 // 健走
            33 -> 4 // 单段攀登
            34 -> 100 // 跳绳
            else -> 100
        }
    }

}