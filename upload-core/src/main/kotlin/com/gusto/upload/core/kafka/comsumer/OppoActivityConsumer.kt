package com.gusto.upload.core.kafka.comsumer

import com.gusto.upload.core.oppo.OppoActivityService
import com.gusto.upload.model.entity.message.OppoActivityMessage
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @since 2024-03-05
 */
@Component
class OppoActivityConsumer {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var activityService: OppoActivityService

    /**
     * 处理订阅事件
     */
    @KafkaListener(
        topics = [OppoActivityMessage.NOTIFY_ACTIVITY_TOPIC],
        groupId = OppoActivityMessage.GROUP + "#UpsertUserRun"
    )
    fun upsertUserRun(message: OppoActivityMessage) {
        log.info("[upsertUserRun] get message = {}", message)
        activityService.syncActivityBySubscription(message.openId, message.timestamp)
        log.info("[upsertUserRun] handle success")
    }

}
