package com.gusto.upload.core.vivo

import com.gusto.framework.core.util.time.minusDays
import com.gusto.upload.core.application.user.NewUserRunApplicationService
import com.gusto.upload.core.common.PushService
import com.gusto.upload.core.qiniu.QiniuService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.util.DistanceUtils
import com.gusto.upload.core.util.NewUserRunMapUtils
import com.gusto.upload.core.utils.JsonUtils
import com.gusto.upload.core.utils.roundTwo
import com.gusto.upload.model.entity.response.ManualSyncRsp
import com.gusto.upload.model.entity.user.NewUserRun
import com.gusto.upload.model.entity.user.NewUserRunDetailAltitudeItem
import com.gusto.upload.model.entity.user.NewUserRunDetailHeartRateItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLapItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLocationItem
import com.gusto.upload.model.entity.user.NewUserRunDetailStepRateItem
import com.gusto.upload.model.entity.vivo.VivoUserAuthInfo
import com.gusto.upload.model.entity.vivo.req.VivoGetActivityDetailReq
import com.gusto.upload.model.entity.vivo.req.VivoGetActivityListReq
import com.gusto.upload.model.entity.vivo.rsp.VivoGetActivityDetailChartPointRsp
import com.gusto.upload.model.entity.vivo.rsp.VivoGetActivityDetailRsp
import com.gusto.upload.model.entity.vivo.rsp.VivoGetActivityDetailTrackInfoRsp
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import kotlin.math.max

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Service
class VivoActivityService : VivoCommonService() {

    private val log = LoggerFactory.getLogger(javaClass)
    private val objectMapper = JsonUtils.initObjectMapper(lowerCamelCase = true)
    private val halfMarathon = 21.0975
    private val marathon = 42.195

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var newRunApplicationService: NewUserRunApplicationService

    @Autowired
    lateinit var qiniuService: QiniuService

    @Autowired
    lateinit var pushService: PushService

    @Autowired
    lateinit var mapUtils: NewUserRunMapUtils

    /**
     * 根据订阅事件同步活动列表
     */
    fun syncActivityBySubscription(openId: String, exerciseId: String) {
        val authInfo = authInfoService.getOneByOpenIdOrNull(openId) ?: return
        checkOrRefreshAccessToken(authInfo)
        val activityDetail = getActivityDetail(exerciseId, authInfo) ?: return
        val run = buildNewUserRun(authInfo, activityDetail)
        val result = uploadRunAndDetail(run, activityDetail)
        log.info("[syncActivityBySubscription] userId = ${authInfo.userId}, sync success")
        if (result) {
            asyncExecutor.execute {
                pushService.notifySyncSuccess(run, 1, authInfo.userId)
            }
        }
    }

    /**
     * 手动同步活动列表
     */
    fun syncActivityListByManual(userId: Long): ManualSyncRsp {
        val authInfo = authInfoService.getOneByUserId(userId)
        checkOrRefreshAccessToken(authInfo)
        val endTime = Instant.now()
        val startTime = max(authInfo.createTime.toEpochMilli(), endTime.minusDays(7).toEpochMilli())
        val req = VivoGetActivityListReq().apply {
            this.startTime = startTime
            this.endTime = endTime.toEpochMilli()
        }
        val rsp = vivoClient.getActivityList(req, buildHeaders(authInfo.accessToken, req))
        val outRsp = ManualSyncRsp()
        outRsp.result = true
        outRsp.reason = "同步成功，请稍后在跑步记录页查看"
        if (rsp.isError) {
            log.error("[syncActivityListByManual] userId = $userId, get activity list error, response content = ${rsp.content}")
            outRsp.result = false
            outRsp.reason = "同步失败，请稍后再试"
            return outRsp
        }
        if (rsp.result == null || rsp.result.data == null || rsp.result.data.isEmpty()) {
            outRsp.reason = "暂无运动记录可同步"
            return outRsp
        }
//        createUserRunAndDetail(authInfo, rsp.result.data)
//        return outRsp
        // 创建跑步记录头部和明细
        asyncExecutor.execute {
            log.info("[syncActivityListByManual] userId = $userId, sync async start")
            createUserRunAndDetail(authInfo, rsp.result.data)
            log.info("[syncActivityListByManual] userId = $userId, sync async success")
        }
        return outRsp
    }

    /**
     * 创建跑步记录并上传到APP
     */
    private fun createUserRunAndDetail(authInfo: VivoUserAuthInfo, exerciseIdList: List<String>) {
        var syncSuccessCount = 0
        val runList = mutableListOf<NewUserRun>()
        exerciseIdList.forEach { exerciseId ->
            val activityDetail = getActivityDetail(exerciseId, authInfo) ?: return@forEach
            val run = buildNewUserRun(authInfo, activityDetail)
            val createSuccess = uploadRunAndDetail(run, activityDetail)
            if (!createSuccess) return@forEach
            syncSuccessCount++
            runList.add(run)
        }
        log.info("[createUserRunAndDetail] userId = ${authInfo.userId}, sync success, count = $syncSuccessCount")
        if (syncSuccessCount == 0) return
        asyncExecutor.execute {
            val latestRun = runList.maxByOrNull { it.startTime }
            if (latestRun != null) {
                pushService.notifySyncSuccess(latestRun, syncSuccessCount, authInfo.userId)
            }
        }
    }

    /**
     * 上传跑步记录和明细
     */
    private fun uploadRunAndDetail(run: NewUserRun, activityDetail: VivoGetActivityDetailRsp): Boolean {
        val lapList = activityDetail.paceChart?.points
            ?.map { buildLap(it, run) }
            ?.toMutableList()
            ?: emptyList()
        run.partTimeKmList = buildPartTimeMap(lapList, run)
        val createSuccess = newRunApplicationService.handlerAndCreate(run)
        if (!createSuccess) return false
        val locationList = buildLocationList(activityDetail.trackInfos)
        val heartRateList = activityDetail.heartChart?.points?.map { buildHeartRate(it) } ?: emptyList()
        val stepRateList = activityDetail.cadenceChart?.points?.map { buildStepRate(it) } ?: emptyList()
        val altitudeList = activityDetail.altSportChart?.points?.map { buildAltitude(it) } ?: emptyList()
        val detail = mapUtils.toNewUserRunDetail(run)
        detail.lapList = lapList
        detail.locationList = locationList
        detail.heartRateList = heartRateList
        detail.stepRateList = stepRateList
        detail.altitudeList = altitudeList
        detail.partTimeKmList = run.partTimeKmList
        asyncExecutor.execute {
            // 上传到七牛
//            qiniuService.uploadString(
//                objectMapper.writeValueAsString(detail),
//                "${run.trackFile}.gzip"
//            )
//            qiniuService.uploadString(
//                objectMapper.writeValueAsString(activityDetail),
//                "vivo_activity_detail_${run.trackFile}.gzip"
//            )
            qiniuService.uploadObject(
                detail,
                "${run.trackFile}.gzip"
            )
            qiniuService.uploadObject(
                activityDetail,
                "vivo_activity_detail_${run.trackFile}.gzip"
            )
        }
        return createSuccess
    }

    /**
     * 创建每公里用时
     */
    private fun buildPartTimeMap(lapList: List<NewUserRunDetailLapItem>, run: NewUserRun): Map<String, Int> {
        val partTimeKmMap = mutableMapOf<String, Int>()
        if (lapList.isEmpty()) {
            return partTimeKmMap
        }
        var currentDuration = 0
        lapList.forEachIndexed { index, item ->
            if (item.distance != 1000.0) {
                return@forEachIndexed
            }
            val currentLap = (index + 1).toDouble()
            currentDuration += item.duration
            partTimeKmMap[currentLap.toString()] = currentDuration
            if (currentLap == 21.0 && run.totalDistance >= 21097.5 && run.totalDistance < 22000) {
                partTimeKmMap[halfMarathon.toString()] = run.totalDuration
            } else if (currentLap == 42.0 && run.totalDistance >= 42195 && run.totalDistance < 43000) {
                partTimeKmMap[marathon.toString()] = run.totalDuration
            }
        }
        return partTimeKmMap
    }

    /**
     * 创建新跑步记录
     */
    private fun buildNewUserRun(authInfo: VivoUserAuthInfo, detail: VivoGetActivityDetailRsp): NewUserRun {
        val run = NewUserRun().apply {
            activityId = detail.startTime
            userId = authInfo.userId
            deviceId = 3
            deviceType = 309
            deviceModel = "vivo健康"
            activityType = formatActivityType(detail.type)
            startTime = detail.startTime
            endTime = detail.endTime
            totalDistance = detail.distance.toDouble()
            totalDuration = detail.costTime.toInt() / 1000
            totalStep = detail.totalStep
            totalCalorie = detail.calorie.toDouble()
            averagePace = (1000 / (totalDistance / totalDuration)).roundTwo()
            maxPace = detail.paceChart?.up?.toDouble() ?: 0.0
            averageHeartRate = detail.avgHeartRate
            maxHeartRate = detail.heartChart?.up ?: 0
            averageStepRate = detail.stepRate
            maxStepRate = detail.cadenceChart?.up ?: 0
            averageStride = if (totalStep != 0) {
                totalDistance * 100 / totalStep
            } else {
                detail.pace.toDouble()
            }
            minAltitude = detail.altSportChart?.down?.toDouble() ?: 0.0
            maxAltitude = detail.altSportChart?.up?.toDouble() ?: 0.0
            totalAscent = detail.mountainTotalHeight?.toDouble() ?: 0.0
            totalDescent = detail.cumulativeDecline?.toDouble() ?: 0.0
            weather = ""
            province = ""
            city = ""
            temperature = ""
            humidity = ""
            partTimeKmList = mutableMapOf()
            // VIVO第三方活动记录ID因数据库字段长度不足（DDL时间太慢），暂存fitUrl字段中
            fitUrl = detail.eid
            thirdActivityId = ""
            thirdActivityType = detail.type.toString()
            abnormal = 12
            externalSportType = detail.type.toString()
            mapTrackImage = ""
            trackImage = ""
            if (activityType == 5) {
                averagePace = if (averagePace != 0.0) {
                    (3600 / averagePace)
                } else {
                    averageStride
                }
                maxPace = if (maxPace != 0.0) {
                    (3600 / maxPace)
                } else {
                    maxPace
                }
            }
        }
        return run
    }

    /**
     * 转换活动类型
     *
     * 运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它
     */
    private fun formatActivityType(sportMode: Int): Int {
        return when (sportMode) {
            2, // 户外跑
            114, // 操场跑步
            -> 1

            1, // 室内跑
            3, // 室内单车
            11, // 跑步机
            -> 2

            12, // 户外步行
            14, // 健步走WALKING
            81, // 遛狗
            85, // 漫步机
            112, // 室内步行
            -> 3

            6, // 登山
            7, // 越野跑
            16, // 徒步
            61, // 定向越野
            87, // 爬楼
            -> 4

            4, // 户外骑行
            63, // 动感单车
            -> 5

            5, // 泳池游泳
            15, // 开放水域游泳
            -> 6

            else -> 100
        }
    }

    /**
     * 获取运动详情
     */
    private fun getActivityDetail(exerciseId: String, authInfo: VivoUserAuthInfo): VivoGetActivityDetailRsp? {
        val req = VivoGetActivityDetailReq().apply {
            this.exerciseId = exerciseId
        }
        val rsp = vivoClient.getActivityDetail(req, buildHeaders(authInfo.accessToken, req))
        if (rsp.isError || rsp.result == null || rsp.result.data == null) {
            log.error("[getActivityDetail] get activity detail error, error = ${rsp.content}")
            return null
        }
        val activityType = formatActivityType(rsp.result.data.type)
        if (activityType == 100) {
            log.debug("[getActivityDetail] activity type is ${rsp.result.data.type} = 100, skip")
            return null
        }
        return rsp.result.data
    }

    /**
     * 创建轨迹
     */
    private fun buildLocationList(trackInfoList: List<VivoGetActivityDetailTrackInfoRsp>?): List<NewUserRunDetailLocationItem> {
        if (trackInfoList.isNullOrEmpty()) {
            return emptyList()
        }
        val locationList = mutableListOf<NewUserRunDetailLocationItem>()
        trackInfoList
            .filter { it.status == 0 }
            .forEachIndexed { index, trackInfo ->
                val location = buildLocation(trackInfo)
                // 用最近20个点的距离和时间计算速度
                if (index >= 20) {
                    val lastLocation = locationList[index - 20]
                    val distance = DistanceUtils.getDistance(
                        trackInfo.latitude,
                        trackInfo.longitude,
                        lastLocation.latitude,
                        lastLocation.longitude
                    )
                    val time = trackInfo.time - lastLocation.timestamp
                    location.speed = (distance / (time / 1000)).roundTwo()
                }
                locationList.add(location)
            }
        return locationList
    }

    /**
     * 创建轨迹
     */
    private fun buildLocation(location: VivoGetActivityDetailTrackInfoRsp): NewUserRunDetailLocationItem {
        val item = NewUserRunDetailLocationItem()
        item.timestamp = location.time
        item.latitude = location.latitude
        item.longitude = location.longitude
        item.speed = 0.0
        return item
    }

    /**
     * 创建海拔
     */
    private fun buildAltitude(point: VivoGetActivityDetailChartPointRsp): NewUserRunDetailAltitudeItem {
        val item = NewUserRunDetailAltitudeItem()
        item.timestamp = point.time
        item.altitude = point.rate.toDouble()
        return item
    }

    /**
     * 创建心率
     */
    private fun buildHeartRate(point: VivoGetActivityDetailChartPointRsp): NewUserRunDetailHeartRateItem {
        val item = NewUserRunDetailHeartRateItem()
        item.timestamp = point.time
        item.heartRate = point.rate
        return item
    }

    /**
     * 创建步频
     */
    private fun buildStepRate(point: VivoGetActivityDetailChartPointRsp): NewUserRunDetailStepRateItem {
        val item = NewUserRunDetailStepRateItem()
        item.timestamp = point.time
        item.stepRate = point.rate
        return item
    }

    /**
     * 创建每圈信息
     */
    private fun buildLap(point: VivoGetActivityDetailChartPointRsp, run: NewUserRun): NewUserRunDetailLapItem {
        val item = NewUserRunDetailLapItem()
        item.distance = if (point.rate != -1) {
            1000.0
        } else {
            run.totalDistance % 1000
        }
        item.duration = point.time.toInt()
        return item
    }

}