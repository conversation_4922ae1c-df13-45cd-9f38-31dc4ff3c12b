package com.gusto.upload.core.kafka.comsumer

import com.gusto.upload.core.wanbao.WanbaoActivityService
import com.gusto.upload.model.entity.message.WanbaoActivityMessage
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @since 2024-09-12
 */
@Component
class WanbaoActivityConsumer {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var activityService: WanbaoActivityService

    /**
     * 处理订阅事件
     */
    @KafkaListener(
        topics = [WanbaoActivityMessage.NOTIFY_ACTIVITY_TOPIC],
        groupId = WanbaoActivityMessage.GROUP + "#UpsertUserRun"
    )
    fun upsertUserRun(message: WanbaoActivityMessage) {
        log.info("[upsertUserRun] get message = {}", message)
        activityService.syncActivityBySubscription(
            message.openId,
            message.countId,
            message.sportType,
            message.fileUrl
        )
        log.info("[upsertUserRun] handle success")
    }

}
