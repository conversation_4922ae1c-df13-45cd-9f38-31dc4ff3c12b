package com.gusto.upload.core.roozym.util

import com.gusto.upload.model.entity.message.RoozymActivityMessage
import com.gusto.upload.model.entity.roozym.RoozymSubscriptionEventType
import com.gusto.upload.model.entity.roozym.req.RoozymNotificationReq

/**
 *
 * <AUTHOR>
 * @since 2022/6/1
 */
fun RoozymNotificationReq.toMessageOrNull(userId: Long): RoozymActivityMessage {
    val message = RoozymActivityMessage()
    message.activityType = if (this.sportType == null) 0 else this.sportType
    message.openId = this.openId
    message.userId = userId
    message.endTime = this.endTime
    message.startTime = this.startTime
    message.eventType = RoozymSubscriptionEventType.forDesc(this.eventType).number
    return message
}
