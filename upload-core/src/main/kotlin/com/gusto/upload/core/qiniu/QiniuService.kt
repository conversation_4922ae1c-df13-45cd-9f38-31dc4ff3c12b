package com.gusto.upload.core.qiniu

import cn.hutool.core.util.ZipUtil
import com.google.gson.Gson
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.core.utils.JsonUtils
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.qiniu.QiniuProperties
import com.qiniu.cdn.CdnManager
import com.qiniu.common.QiniuException
import com.qiniu.storage.BucketManager
import com.qiniu.storage.UploadManager
import com.qiniu.storage.model.DefaultPutRet
import com.qiniu.util.Auth
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.io.UnsupportedEncodingException
import java.util.zip.GZIPOutputStream

/**
 * 七牛 服务类
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Service
class QiniuService {

    private val log = LoggerFactory.getLogger(javaClass)
    private val objectMapper = JsonUtils.initObjectMapper(lowerCamelCase = true)

    @Autowired
    lateinit var config: QiniuProperties

    @Autowired
    lateinit var auth: Auth

    @Autowired
    lateinit var uploadManager: UploadManager

    @Autowired
    lateinit var cdnManager: CdnManager

    @Autowired
    lateinit var bucketManager: BucketManager

    /**
     * 上传网络资源
     */
    fun uploadUrl(remoteSrcUrl: String, key: String) {
        try {
            val fetchRet = bucketManager.fetch(remoteSrcUrl, config.bucket, key)
            log.debug("qiniu upload finished, key=${fetchRet.key}, hash=${fetchRet.hash}, mimeType=${fetchRet.mimeType}, fsize=${fetchRet.fsize}")
        } catch (ex: QiniuException) {
            val r = ex.response
            log.error(r.toString())
            try {
                log.error(r.bodyString())
            } catch (ex2: QiniuException) {
                // ignore
            }
        }
    }

    /**
     * 上传字符串
     */
    fun uploadString(str: String, key: String) {
        try {
            val uploadBytes = ZipUtil.gzip(str, "UTF-8")
            val upToken = auth.uploadToken(config.bucket)
            try {
                val response = uploadManager.put(uploadBytes, key, upToken)
                // 解析上传成功的结果
                val putRet: DefaultPutRet = Gson().fromJson(response.bodyString(), DefaultPutRet::class.java)
                log.debug("qiniu upload finished, key={}, hash={}", putRet.key, putRet.hash)
            } catch (ex: QiniuException) {
                val r = ex.response
                log.error(r.toString())
                try {
                    log.error(r.bodyString())
                } catch (ex2: QiniuException) {
                    // ignore
                }
            }
        } catch (ex: UnsupportedEncodingException) {
            // ignore
        }
    }

    /**
     * 刷新文件
     */
    fun refresh(url: String) {
        try {
            // 单次方法调用刷新的链接不可以超过100个
            val result = cdnManager.refreshUrls(listOf(url).toTypedArray())
            log.debug("qiniu refresh finished, result={}", result.formatToJson())
        } catch (e: QiniuException) {
            log.error(e.response.toString())
        }
    }

    /**
     * 判断文件是否存在
     */
    fun isExist(url: String): Boolean {
        return try {
            val fileInfo = bucketManager.stat(config.bucket, url)
            fileInfo.fsize > 0
        } catch (e: QiniuException) {
            log.error(e.response.toString())
            false
        }
    }

    /**
     * 上传对象
     */
    fun uploadObject(any: Any, key: String) {
        try {
            val uploadBytes = compressToJson(any)
            val upToken = auth.uploadToken(config.bucket)
            try {
                val response = uploadManager.put(uploadBytes, key, upToken)
                // 解析上传成功的结果
                val putRet: DefaultPutRet = Gson().fromJson(response.bodyString(), DefaultPutRet::class.java)
                log.debug("qiniu upload finished, key={}, hash={}", putRet.key, putRet.hash)
            } catch (ex: QiniuException) {
                val r = ex.response
                log.error(r.toString())
                try {
                    log.error(r.bodyString())
                } catch (ex2: QiniuException) {
                    // ignore
                }
            }
        } catch (ex: UnsupportedEncodingException) {
            // ignore
        }
    }

    /**
     * 将数据压缩为GZIP字节数组
     */
    private fun compressToJson(detail: Any): ByteArray {
        try {
            ByteArrayOutputStream().use { byteArrayOutputStream ->
                GZIPOutputStream(byteArrayOutputStream).use { gzipOutputStream ->
                    objectMapper.writeValue(gzipOutputStream, detail)
                    gzipOutputStream.finish()
                    return byteArrayOutputStream.toByteArray()
                }
            }
        } catch (e: IOException) {
            log.error("Error compressing data to GZIP", e)
            throw ServiceException(UploadErrorCode.QINIU_UPLOAD_ERROR.code, "Error compressing data to GZIP")
        }
    }

}