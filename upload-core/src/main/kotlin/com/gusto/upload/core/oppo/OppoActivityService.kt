package com.gusto.upload.core.oppo

import com.gusto.framework.core.util.time.minusDays
import com.gusto.upload.core.application.user.NewUserRunApplicationService
import com.gusto.upload.core.common.PushService
import com.gusto.upload.core.qiniu.QiniuService
import com.gusto.upload.core.service.user.NewUserRunService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.util.DistanceUtils
import com.gusto.upload.core.util.NewUserRunMapUtils
import com.gusto.upload.core.util.OppoSportModeUtils
import com.gusto.upload.core.utils.JsonUtils
import com.gusto.upload.core.utils.roundTwo
import com.gusto.upload.model.entity.oppo.OppoUserAuthInfo
import com.gusto.upload.model.entity.oppo.rsp.OppoGetActivityDetailOtherElevationRsp
import com.gusto.upload.model.entity.oppo.rsp.OppoGetActivityDetailOtherGpsRsp
import com.gusto.upload.model.entity.oppo.rsp.OppoGetActivityDetailOtherHeartRateRsp
import com.gusto.upload.model.entity.oppo.rsp.OppoGetActivityDetailOtherStepFrequencyRsp
import com.gusto.upload.model.entity.oppo.rsp.OppoGetActivityDetailRsp
import com.gusto.upload.model.entity.oppo.rsp.OppoGetActivityRsp
import com.gusto.upload.model.entity.response.ManualSyncRsp
import com.gusto.upload.model.entity.user.NewUserRun
import com.gusto.upload.model.entity.user.NewUserRunDetailAltitudeItem
import com.gusto.upload.model.entity.user.NewUserRunDetailHeartRateItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLapItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLocationItem
import com.gusto.upload.model.entity.user.NewUserRunDetailStepRateItem
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import kotlin.math.max

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Service
class OppoActivityService : OppoCommonService() {

    private val log = LoggerFactory.getLogger(javaClass)
    private val objectMapper = JsonUtils.initObjectMapper(lowerCamelCase = true)
    private val halfMarathon = 21.0975
    private val marathon = 42.195

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var newRunApplicationService: NewUserRunApplicationService

    @Autowired
    lateinit var runService: NewUserRunService

    @Autowired
    lateinit var qiniuService: QiniuService

    @Autowired
    lateinit var pushService: PushService

    @Autowired
    lateinit var mapUtils: NewUserRunMapUtils

    /**
     * 根据订阅事件同步活动列表
     */
    fun syncActivityBySubscription(openId: String, timestamp: Long) {
        val authInfo = authInfoService.getOneByOpenIdOrNull(openId) ?: return
        checkOrRefreshAccessToken(authInfo)
        // 结束时间等于开始时间加一天
        val endTime = timestamp + 86400000
        val rsp = oppoClient.getActivityListByDate(
            timestamp,
            endTime,
            buildHeaders(authInfo.accessToken)
        )
        val userId = authInfo.userId
        if (rsp.isError) {
            log.warn("[syncActivityBySubscription] userId = $userId, sync error, message = ${rsp.content}")
            return
        }
        if (rsp.result == null || rsp.result.body == null || rsp.result.body.none { it.dataType == 2 }) {
            log.warn("[syncActivityBySubscription] userId = ${authInfo.userId}, sync success, but no activity, message = ${rsp.content}")
            return
        }

        // 创建跑步记录头部和明细
        createUserRunAndDetail(authInfo, rsp.result.body)
    }

    /**
     * 手动同步活动列表
     */
    fun syncActivityListByManual(userId: Long): ManualSyncRsp {
        val authInfo = authInfoService.getOneByUserId(userId)
        checkOrRefreshAccessToken(authInfo)
        val endTime = Instant.now()
        val startTime = max(authInfo.createTime.toEpochMilli(), endTime.minusDays(7).toEpochMilli())
        val rsp = oppoClient.getActivityListByDate(
            startTime,
            endTime.toEpochMilli(),
            buildHeaders(authInfo.accessToken)
        )
        val outRsp = ManualSyncRsp()
        outRsp.result = true
        outRsp.reason = "同步成功，请稍后在跑步记录页查看"
        if (rsp.isError) {
            log.error("[syncActivityListByManual] userId = $userId, get activity list error, response content = ${rsp.content}")
            outRsp.result = false
            outRsp.reason = "同步失败，请稍后再试"
            return outRsp
        }
        if (rsp.result == null || rsp.result.body == null || rsp.result.body.none { it.dataType == 2 }) {
            outRsp.reason = "暂无运动记录可同步"
            return outRsp
        }
        // 创建跑步记录头部和明细
//        createUserRunAndDetail(authInfo, rsp.result.body.filter { it.dataType == 2 })
        asyncExecutor.execute {
            log.info("[syncActivityListByManual] userId = $userId, sync async start")
            createUserRunAndDetail(authInfo, rsp.result.body.filter { it.dataType == 2 })
            log.info("[syncActivityListByManual] userId = $userId, sync async success")
        }
        return outRsp
    }

    /**
     * 创建跑步记录并上传到APP
     */
    private fun createUserRunAndDetail(authInfo: OppoUserAuthInfo, activityList: List<OppoGetActivityRsp>) {
        var syncSuccessCount = 0
        val runList = mutableListOf<NewUserRun>()
        activityList.forEach { activity ->
            val run = buildNewUserRun(authInfo, activity) ?: return@forEach
            val createSuccess = uploadRunAndDetail(run, activity, authInfo)
            if (!createSuccess) return@forEach
            syncSuccessCount++
            runList.add(run)
        }
        log.info("[createUserRunAndDetail] userId = ${authInfo.userId}, sync success, count = $syncSuccessCount")
        if (syncSuccessCount == 0) return
        asyncExecutor.execute {
            val latestRun = runList.maxByOrNull { it.startTime }
            if (latestRun != null) {
                pushService.notifySyncSuccess(latestRun, syncSuccessCount, authInfo.userId)
            }
        }
    }

    /**
     * 上传跑步记录和明细
     */
    private fun uploadRunAndDetail(run: NewUserRun, activity: OppoGetActivityRsp, authInfo: OppoUserAuthInfo): Boolean {
        val activityDetail = getActivityDetail(authInfo, activity) ?: return false
        val otherData = activityDetail.otherSportData
        val gpsList = otherData.gpsPoint ?: emptyList()
        val eleList = otherData.elevation ?: emptyList()
        val hrList = otherData.heartRate ?: emptyList()
        val cadenceList = otherData.frequency ?: emptyList()
        run.apply {
            if (hrList.isNotEmpty()) {
                maxHeartRate = hrList.maxOfOrNull { it.value } ?: 0
            }
            if (eleList.isNotEmpty()) {
                minAltitude = eleList.minOfOrNull { it.value.toDouble() } ?: 0.0
                minAltitude /= 10
                maxAltitude = eleList.maxOfOrNull { it.value.toDouble() } ?: 0.0
                maxAltitude /= 10
                val pair = buildAscentAndDescent(eleList.map { it.value.toDouble() })
                totalAscent = pair.first / 10
                totalDescent = pair.second / 10
            }
            if (cadenceList.isNotEmpty()) {
                maxStepRate = cadenceList.maxOfOrNull { it.value } ?: 0
            }
            partTimeKmList = buildPartTimeMap(gpsList)
        }
        val createSuccess = newRunApplicationService.handlerAndCreate(run)
        if (createSuccess) {
            val locationList = buildLocationList(gpsList)
            val kmPace = otherData.kmPace ?: emptyList()
            val lapList = kmPace
                .map { buildLap(it) }
                .toMutableList()
            val sumLapDistance = lapList.sumOf { it.distance }
            if (run.totalDistance - sumLapDistance > 0) {
                val lastLap = NewUserRunDetailLapItem()
                lastLap.distance = run.totalDistance - sumLapDistance
                lastLap.duration = run.totalDuration - lapList.sumOf { it.duration }
                lapList.add(lastLap)
            }
            val heartRateList = hrList.map { buildHeartRate(it) }
            val stepRateList = cadenceList.map { buildStepRate(it) }
            val altitudeList = eleList.map { buildAltitude(it) }
            val detail = mapUtils.toNewUserRunDetail(run)
            detail.lapList = lapList
            detail.locationList = locationList
            detail.heartRateList = heartRateList
            detail.stepRateList = stepRateList
            detail.altitudeList = altitudeList
            detail.partTimeKmList = run.partTimeKmList
            asyncExecutor.execute {
                // 上传到七牛
//                qiniuService.uploadString(
//                    objectMapper.writeValueAsString(detail),
//                    "${run.trackFile}.gzip"
//                )
//                qiniuService.uploadString(
//                    objectMapper.writeValueAsString(activityDetail),
//                    "oppo_activity_detail_${run.trackFile}.gzip"
//                )
                qiniuService.uploadObject(
                    detail,
                    "${run.trackFile}.gzip"
                )
                qiniuService.uploadObject(
                    activityDetail,
                    "oppo_activity_detail_${run.trackFile}.gzip"
                )
            }
        }
        return createSuccess
    }

    /**
     * 创建每公里用时
     */
    private fun buildPartTimeMap(gpsList: List<OppoGetActivityDetailOtherGpsRsp>): Map<String, Int> {
        val partTimeKmMap = mutableMapOf<String, Int>()
        if (gpsList.isEmpty()) {
            return partTimeKmMap
        }
//        if (kmPaceList.isNotEmpty()) {
//            var totalTime = 0
//            kmPaceList.forEachIndexed { index, duration ->
//                totalTime += duration
//                partTimeKmMap[(index + 1).toDouble().toString()] = totalTime
//            }
//        }
        var lastTimestamp = gpsList.first().timestamp
        var halfMarathonFlag = false
        var marathonFlag = false
        var currentDuration = 0L
        var currentDistance = 0.0
        var currentLap = 1
        gpsList.forEachIndexed { index, gps ->
            val currentTimestamp = gps.timestamp
            if (gps.type == 0) {
                // 正常点才计入
                val timeDiff = currentTimestamp - lastTimestamp
                currentDuration += timeDiff / 1000
                val distanceDiff = if (index > 1) {
                    DistanceUtils.getDistance(
                        gps.latitude,
                        gps.longitude,
                        gpsList[index - 1].latitude,
                        gpsList[index - 1].longitude
                    )
                } else {
                    0.0
                }
                currentDistance += distanceDiff
            }
            lastTimestamp = currentTimestamp

            // 计算半马、全马累计用时，或每公里用时（如果接口没有返回kmPace）
            if (!halfMarathonFlag && currentDistance >= halfMarathon * 1000) {
                val halfMarathonItem = partTimeKmMap[halfMarathon.toString()]
                if (halfMarathonItem == null) {
                    partTimeKmMap[halfMarathon.toString()] = currentDuration.toInt()
                    halfMarathonFlag = true
                }
            } else if (!marathonFlag && currentDistance >= marathon * 1000) {
                val marathonItem = partTimeKmMap[marathon.toString()]
                if (marathonItem == null) {
                    partTimeKmMap[marathon.toString()] = currentDuration.toInt()
                    marathonFlag = true
                }
            } else {
                if (currentDistance >= 1000) {
                    if (currentDistance / 1000 >= currentLap) {
                        partTimeKmMap[currentLap.toDouble().toString()] = currentDuration.toInt()
                        currentLap++
                    }
                }
            }
        }
        return partTimeKmMap.toSortedMap(compareBy { it.toDouble() })
    }

    /**
     * 创建新跑步记录
     */
    private fun buildNewUserRun(authInfo: OppoUserAuthInfo, activity: OppoGetActivityRsp): NewUserRun? {
        val otherData = activity.otherSportData ?: return null
        val run = NewUserRun().apply {
            activityId = activity.startTime
            userId = authInfo.userId
            deviceId = 3
            deviceType = 308
            deviceModel = activity.deviceName
            activityType = formatActivityType(activity.sportMode)
            startTime = activity.startTime
            endTime = activity.endTime
            totalDistance = otherData?.totalDistance?.toDouble() ?: 0.0
            totalDuration = ((otherData.totalTime ?: 0) / 1000).toInt()
            totalStep = otherData.totalSteps
            totalCalorie = (otherData.totalCalories / 1000).toDouble()
            averagePace = otherData.avgPace.toDouble()
            maxPace = otherData.bestPace.toDouble()
            averageHeartRate = otherData.avgHeartRate
            maxHeartRate = 0
            averageStepRate = otherData.avgStepRate
            maxStepRate = otherData.bestStepRate
            averageStride = if (totalDistance > 0 && totalStep > 0) {
                ((totalDistance / totalStep) * 100).roundTwo()
            } else {
                0.0
            }
            minAltitude = 0.0
            maxAltitude = 0.0
            totalAscent = 0.0
            totalDescent = 0.0
            weather = ""
            province = ""
            city = ""
            temperature = ""
            humidity = ""
            partTimeKmList = mutableMapOf()
            thirdActivityId = activity.startTime.toString()
            thirdActivityType = activity.sportMode.toString()
            abnormal = 11
            externalSportType = activity.sportMode.toString()
            mapTrackImage = ""
            trackImage = ""
            fitUrl = ""
        }
        return run
    }

    /**
     * 转换活动类型
     *
     * 运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它
     */
    private fun formatActivityType(sportMode: Int): Int {
        return when (sportMode) {
            OppoSportModeUtils.RUN,
            OppoSportModeUtils.OUTDOOR_PHYSICAL_RUN,
            OppoSportModeUtils.OUTDOOR_5KM_RELAX_RUN,
            OppoSportModeUtils.OUTDOOR_FAT_REDUCE_RUN,
            OppoSportModeUtils.MARATHON,
            OppoSportModeUtils.AUTO_MODE_RUN,
            OppoSportModeUtils.RUN_ALL,
            OppoSportModeUtils.PLAYGROUND_RUNNING -> 1

            OppoSportModeUtils.INDOOR_RUN,
            OppoSportModeUtils.INDOOR_PHYSICAL_RUN,
            OppoSportModeUtils.INDOOR_5KM_RELAX_RUN,
            OppoSportModeUtils.INDOOR_FAT_REDUCE_RUN,
            OppoSportModeUtils.TREADMILL_RUN -> 2

            OppoSportModeUtils.WALK,
            OppoSportModeUtils.INDOOR_FITNESS_WALK,
            OppoSportModeUtils.AUTO_MODE_WALK,
            OppoSportModeUtils.WALK_ALL,
            OppoSportModeUtils.WALK_THE_DOG -> 3

            OppoSportModeUtils.CLIMB,
            OppoSportModeUtils.MOUNTAIN_CLIMBING,
            OppoSportModeUtils.CROSS_COUNTRY -> 4

            OppoSportModeUtils.RIDE,
            OppoSportModeUtils.INDOOR_MOTION_BIKE,
            OppoSportModeUtils.RIDE_ALL -> 5

            OppoSportModeUtils.SWIM,
            OppoSportModeUtils.SWIM_ALL -> 6

            else -> 100
        }
    }

    /**
     * 获取运动详情
     */
    private fun getActivityDetail(authInfo: OppoUserAuthInfo, activity: OppoGetActivityRsp): OppoGetActivityDetailRsp? {
        val dbRun =
            runService.getListByUserIdAndRunId(authInfo.userId, activity.startTime).filter { it.deviceType == 308 }
        if (dbRun.isNotEmpty()) {
            log.warn("[getActivityDetail] get activity detail, db run exists, runId = ${activity.startTime}, userId = ${authInfo.userId}")
            return null
        }
        val rsp =
            oppoClient.getActivityDetailByDate(activity.startTime, activity.endTime, buildHeaders(authInfo.accessToken))
        if (rsp.isError || rsp.result == null || rsp.result.body == null || rsp.result.body.none { it.startTime == activity.startTime }) {
            log.error("[getActivityDetail] get activity detail error, error = ${rsp.content}")
            return null
        }
        return rsp.result.body.first { it.startTime == activity.startTime }
    }

    /**
     * 获取累计爬升和下降
     */
    private fun buildAscentAndDescent(eleList: List<Double>): Pair<Double, Double> {
        var ascent = 0.0
        var descent = 0.0
        for (i in 1 until eleList.size) {
            val prevElevation = eleList[i - 1]
            val currentElevation = eleList[i]
            if (currentElevation > prevElevation) {
                ascent += currentElevation - prevElevation
            } else if (currentElevation < prevElevation) {
                descent += prevElevation - currentElevation
            }
        }
        return Pair(ascent, descent)
    }

    /**
     * 创建轨迹
     */
    private fun buildLocationList(gpsList: List<OppoGetActivityDetailOtherGpsRsp>): List<NewUserRunDetailLocationItem> {
        val locationList = mutableListOf<NewUserRunDetailLocationItem>()
        gpsList
            .filter { it.type == 0 }
            .forEachIndexed { index, gps ->
                val location = buildLocation(gps)
                // 用最近20个点的距离和时间计算速度
                if (index >= 20) {
                    val lastLocation = locationList[index - 20]
                    val distance = DistanceUtils.getDistance(
                        gps.latitude,
                        gps.longitude,
                        lastLocation.latitude,
                        lastLocation.longitude
                    )
                    val time = gps.timestamp - lastLocation.timestamp
                    location.speed = (distance / (time / 1000)).roundTwo()
                }
                locationList.add(location)
            }
        return locationList
    }

    /**
     * 创建轨迹
     */
    private fun buildLocation(location: OppoGetActivityDetailOtherGpsRsp): NewUserRunDetailLocationItem {
        val item = NewUserRunDetailLocationItem()
        item.timestamp = location.timestamp
        item.latitude = location.latitude
        item.longitude = location.longitude
        item.speed = 0.0
        return item
    }

    /**
     * 创建海拔
     */
    private fun buildAltitude(elevation: OppoGetActivityDetailOtherElevationRsp): NewUserRunDetailAltitudeItem {
        val item = NewUserRunDetailAltitudeItem()
        item.timestamp = elevation.timestamp
        item.altitude = elevation.value.toDouble() / 10
        return item
    }

    /**
     * 创建心率
     */
    private fun buildHeartRate(heartRate: OppoGetActivityDetailOtherHeartRateRsp): NewUserRunDetailHeartRateItem {
        val item = NewUserRunDetailHeartRateItem()
        item.timestamp = heartRate.timestamp
        item.heartRate = heartRate.value
        return item
    }

    /**
     * 创建步频
     */
    private fun buildStepRate(stepRate: OppoGetActivityDetailOtherStepFrequencyRsp): NewUserRunDetailStepRateItem {
        val item = NewUserRunDetailStepRateItem()
        item.timestamp = stepRate.timestamp
        item.stepRate = stepRate.value
        return item
    }

    /**
     * 创建每圈信息
     */
    private fun buildLap(duration: Int): NewUserRunDetailLapItem {
        val item = NewUserRunDetailLapItem()
        item.distance = 1000.0
        item.duration = duration
        return item
    }

}