package com.gusto.upload.core.roozym.producer

import com.gusto.upload.model.entity.message.RoozymActivityMessage
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.support.SendResult
import org.springframework.stereotype.Component
import org.springframework.util.concurrent.ListenableFutureCallback

/**
 *
 * <AUTHOR>
 * @since 2022/6/1
 */
@Component
class RoozymActivityProducer {
    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var kafkaTemplate: KafkaTemplate<String, Any>

    /**
     * 发送到订阅事件消费队列
     */
    fun sendSubscriptionMessage(message: RoozymActivityMessage) {
        val feature = kafkaTemplate.send(RoozymActivityMessage.SUBSCRIPTION_TOPIC, message)
        feature.addCallback(object : ListenableFutureCallback<SendResult<String, Any>> {
            override fun onFailure(ex: Throwable) {
                log.error("[Roozym:sendSubscriptionMessage] [onFailure] message = ${message}, cause = ${ex.message}")
            }

            override fun onSuccess(result: SendResult<String, Any>) {
                log.debug(
                    "[Roozym:sendSubscriptionMessage] [onSuccess] topic = ${RoozymActivityMessage.SUBSCRIPTION_TOPIC}, message = ${result.producerRecord.value()}"
                )
            }
        })
    }
}
