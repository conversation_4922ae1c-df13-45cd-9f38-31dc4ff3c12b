package com.gusto.upload.core.honor

import com.gusto.framework.core.exception.ServiceException
import com.gusto.upload.core.utils.honor.toUserAuthInfo
import com.gusto.upload.model.entity.UploadErrorCode
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.net.URLEncoder.encode
import java.time.Instant
import java.util.concurrent.Executor
import javax.annotation.Resource


/**
 * 荣耀-鉴权 服务类
 *
 * <AUTHOR>
 * @since 2021-10-28
 */
@Service
class HonorAuthService : HonorService() {

    private val log = LoggerFactory.getLogger(javaClass)

    private val scopeList = listOf(
        "https://www.hihonor.com/healthkit/step.read",
        // "https://www.hihonor.com/healthkit/distance.read",
        "https://www.hihonor.com/healthkit/speed.read",
        // "https://www.hihonor.com/healthkit/calories.read",
        "https://www.hihonor.com/healthkit/location.read",
        "https://www.hihonor.com/healthkit/altitude.read",
        "https://www.hihonor.com/healthkit/heartrate.read",
        "https://www.hihonor.com/healthkit/activityrecord.read"
    ).sorted()

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    /**
     * 使用授权码Code获取AT
     * PS: AT一个小时有效
     */
    fun upsertAccessTokenByCode(code: String, userId: Long): Boolean {
        val rsp = honorClient.getAccessToken(
            code = encode(code, "UTF-8"),
            redirectUri = config.redirectUri,
            clientId = config.clientId,
            clientSecret = config.clientSecret
        )
        if (rsp.isError) {
            log.error("[upsertAccessTokenByCode] get AT error, error = ${rsp.content}")
            throw ServiceException(UploadErrorCode.HONOR_AUTH_ERROR.code, "授权失败，请重新授权")
        }
        val parseRsp = honorClient.parseAccessToken(accessToken = rsp.result.accessToken)
        if (parseRsp.isError) {
            log.error("[upsertAccessTokenByCode] parse AT error, error = ${parseRsp.content}")
            throw ServiceException(UploadErrorCode.HONOR_AUTH_ERROR.code, "授权失败，请重新授权")
        }
        val userScopeList = parseRsp.result.scope.split(" ").sorted()
        if (!userScopeList.containsAll(scopeList)) {
            log.debug("[upsertAccessTokenByCode] system score list is $scopeList")
            log.debug("[upsertAccessTokenByCode] user scope list is $userScopeList")
            cancelAuthPassive(userId)
            throw ServiceException(UploadErrorCode.HONOR_AUTH_ERROR.code, "用户授权权限不足，请重新授权")
        }

        val openId = parseRsp.result.openId
        val authInfo = rsp.result.toUserAuthInfo(userId, openId)
        val avatar = "honor_avatar_${authInfo.userId}_${Instant.now().epochSecond}.jpg"
        qiniuService.uploadUrl(authInfo.avatar, avatar)
        authInfo.avatar = "https://file.gusto.cn/$avatar"

        // 查询此荣耀账号是否绑定了多个第一赛道账号
        val dbAuthInfoList = authInfoService.getListByUnionId(authInfo.unionId)
        if (dbAuthInfoList.isNotEmpty()) {
            dbAuthInfoList.forEach {
                // 踢掉此荣耀账号的其他第一赛道账号授权，不用请求接口，因为荣耀是根据openId推送运动记录的
                authInfoService.deleteWithCache(it)
            }
        }

        // 插入新的授权信息
        authInfoService.create(authInfo)

        return true
    }

    /**
     * 根据用户ID取消授权
     */
    fun cancelAuthByUserId(userId: Long): Boolean {
        val uncheckAuthInfo = authInfoService.getOneByUserId(userId)
        val authInfo = checkOrRefreshAccessToken(uncheckAuthInfo)!!

        // 取消授权
        val rsp = honorClient.cancelAuth(authInfo.accessToken)
        if (rsp.isError && rsp.statusIsNot(200)) {
            log.error("[cancelAuth] req error, error = ${rsp.content}")
            return false
        }
        // 更新数据库
        authInfoService.deleteWithCache(authInfo)

        return true
    }

    /**
     * 取消授权（被动）
     */
    fun cancelAuthPassive(userId: Long) {
        val uncheckAuthInfo = authInfoService.getOneByUserIdOrNull(userId) ?: return
        val authInfo = checkOrRefreshAccessToken(uncheckAuthInfo)!!

        try {
            // 取消授权
            val rsp = honorClient.cancelAuth(authInfo.accessToken)
            if (rsp.isError) {
                log.warn("[cancelAuthPassive] req error, error = ${rsp.content}")
                // throw BusinessException(UploadErrorInfo.HONOR_CANCEL_AUTH_ERROR)
            }
        } catch (e: Exception) {
            log.warn("[cancelAuthPassive] req error, already cancel")
        } finally {
            // 更新数据库
            authInfoService.deleteWithCache(authInfo)
        }
    }

}
