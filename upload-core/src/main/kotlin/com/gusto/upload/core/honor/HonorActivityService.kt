package com.gusto.upload.core.honor

import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.time.minusDays
import com.gusto.upload.core.application.user.NewUserRunApplicationService
import com.gusto.upload.core.common.PushService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.util.NewUserRunMapUtils
import com.gusto.upload.core.utils.JsonUtils.initObjectMapper
import com.gusto.upload.core.utils.honor.toNewUserRun
import com.gusto.upload.core.utils.round
import com.gusto.upload.core.utils.roundTwo
import com.gusto.upload.core.utils.run.roundTwo
import com.gusto.upload.model.entity.honor.HonorActivityRecord
import com.gusto.upload.model.entity.honor.HonorMotionDetail
import com.gusto.upload.model.entity.honor.HonorTracePointEntity
import com.gusto.upload.model.entity.honor.HonorWorkoutPaceEntity
import com.gusto.upload.model.entity.honor.req.HonorGetActivityRecordListReq
import com.gusto.upload.model.entity.response.ManualSyncRsp
import com.gusto.upload.model.entity.user.NewUserRun
import com.gusto.upload.model.entity.user.NewUserRunDetailAltitudeItem
import com.gusto.upload.model.entity.user.NewUserRunDetailHeartRateItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLapItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLocationItem
import com.gusto.upload.model.entity.user.NewUserRunDetailSpeedItem
import com.gusto.upload.model.entity.user.NewUserRunDetailStepRateItem
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.*
import java.util.concurrent.Executor
import javax.annotation.Resource
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import kotlin.math.max

/**
 * 荣耀-活动 服务类
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Service
class HonorActivityService : HonorService() {

    private val log = LoggerFactory.getLogger(javaClass)
    private val objectMapper = initObjectMapper(lowerCamelCase = true)
    private val halfMarathon = 21.0975
    private val marathon = 42.195

    @Autowired
    lateinit var newRunApplicationService: NewUserRunApplicationService

    @Autowired
    lateinit var pushService: PushService

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var mapUtils: NewUserRunMapUtils

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    /**
     * 创建密文
     */
    private fun buildSecretData(appSecret: String, openId: String): String {
        val secretKey = SecretKeySpec(appSecret.toByteArray(), "HmacSHA256")
        val mac = Mac.getInstance(secretKey.algorithm)
        mac.init(secretKey)
        val hamc = mac.doFinal(openId.toByteArray())
        return Base64.getEncoder().encodeToString(hamc)
    }

    /**
     * 根据订阅事件同步活动记录列表
     */
    fun syncActivityListBySubscription(
        userId: Long,
        startTime: Long,
        dataType: String
    ) {
        val uncheckAuthInfo = try {
            authInfoService.getOneByUserId(userId)
        } catch (e: ServiceException) {
            log.warn("[syncActivityListBySubscription] userId = $userId, auth not found")
            return
        }
        val authInfo = checkOrRefreshAccessToken(uncheckAuthInfo, true)
        if (authInfo == null) {
            log.warn("[syncActivityListBySubscription] userId = $userId, refresh AT error, skip sync")
            return
        }
        val req = HonorGetActivityRecordListReq().apply {
            this.startTime = startTime
            this.endTime = Instant.now().toEpochMilli()
            this.dataTypes = listOf(dataType)
            this.openId = authInfo.openId
            this.appId = config.clientId
            this.accessToken = authInfo.accessToken
            this.secretData = buildSecretData(config.clientSecret, authInfo.openId)
        }
        val rsp = honorClient.getActivityRecordList(req)
        if (rsp.isError) {
            log.error("[syncActivityListBySubscription] userId = $userId, sync error, message = ${rsp.content}")
            return
        }

        // 创建跑步记录头部和明细
        createUserRunAndDetail(rsp.result.data.sportRecord ?: emptyList(), userId)
    }

    /**
     * 手动同步活动列表
     */
    fun syncActivityListByManual(userId: Long): ManualSyncRsp {
        val authInfo = authInfoService.getOneByUserId(userId)
        checkOrRefreshAccessToken(authInfo)
        val endTime = Instant.now()
        val startTime = max(authInfo.createTime.toEpochMilli(), endTime.minusDays(7).toEpochMilli())
        val req = HonorGetActivityRecordListReq().apply {
            this.startTime = startTime
            this.endTime = Instant.now().toEpochMilli()
            this.dataTypes = listOf(
                "RECORD_RUNNING_OUTDOOR",
                "RECORD_RUNNING_INDOOR",
                "RECORD_WALKING_OUTDOOR",
                "RECORD_RIDING",
            )
            this.openId = authInfo.openId
            this.appId = config.clientId
            this.accessToken = authInfo.accessToken
            this.secretData = buildSecretData(config.clientSecret, authInfo.openId)
        }
        val rsp = honorClient.getActivityRecordList(req)
        val outRsp = ManualSyncRsp()
        outRsp.result = true
        outRsp.reason = "同步成功，请稍后在跑步记录页查看"
        if (rsp.isError) {
            log.error("[syncActivityListByManual] userId = $userId, get activity list error, response content = ${rsp.content}")
            outRsp.result = false
            outRsp.reason = "同步失败，请稍后再试"
            return outRsp
        }
        if (rsp.result == null || rsp.result.data == null || rsp.result.data.sportRecord == null || rsp.result.data.sportRecord.isEmpty()) {
            outRsp.reason = "暂无运动记录可同步"
            return outRsp
        }
//        createUserRunAndDetail(rsp.result.data.sportRecord, userId)
        // 创建跑步记录头部和明细
        asyncExecutor.execute {
            log.info("[syncActivityListByManual] userId = $userId, sync async start")
            createUserRunAndDetail(rsp.result.data.sportRecord, userId)
            log.info("[syncActivityListByManual] userId = $userId, sync async success")
        }
        return outRsp
    }

    /**
     * 遍历获取到的活动记录，同步到第一赛道
     */
    private fun createUserRunAndDetail(activityRecordList: List<HonorActivityRecord>, userId: Long) {
        var syncSuccessCount = 0
        val runList = emptyList<NewUserRun>().toMutableList()
        activityRecordList.forEach {
            val run = it.toNewUserRun(userId)
            val createSuccess = uploadRunAndDetail(it, run)
            if (!createSuccess) return@forEach
            syncSuccessCount++
            runList.add(run)
        }
        log.info("[getUserActivityRecordList] userId=$userId, sync success, count=$syncSuccessCount")
        if (syncSuccessCount == 0) return
        asyncExecutor.execute {
            val latestRun = runList.maxByOrNull { it.startTime }
            if (latestRun != null) {
                val user = userService.getOneById(latestRun.userId)
                pushService.notifySyncSuccess(latestRun, syncSuccessCount, user.userId)
            }
        }
    }

    /**
     * 创建和上传新跑步记录明细
     */
    private fun uploadRunAndDetail(activityRecord: HonorActivityRecord, run: NewUserRun): Boolean {
        val detailDataList = activityRecord.recDetail.detailDataList
        val motionDetailList = detailDataList.flatMap { it.detailDataList }
        run.apply {
            val maxSpeed = motionDetailList.filter { it.speed > 0 }.maxOfOrNull { it.speed }?.toDouble() ?: 0.0
            maxPace = if (maxSpeed == 0.0) {
                0.0
            } else {
                (10000 / maxSpeed).roundTwo()
            }
            run.maxHeartRate = motionDetailList
                .filter { it.heartRate in 1..249 }
                .maxOfOrNull { it.heartRate } ?: 0
            run.averageStepRate = motionDetailList
                .mapNotNull { it.stepFrequency }
                .average().toInt()
            run.maxStepRate = motionDetailList
                .mapNotNull { it.stepFrequency }
                .maxOrNull() ?: 0
            val elevationList = motionDetailList.mapNotNull { it.elevation }
            run.minAltitude = elevationList.minOrNull()?.toDouble()?.div(10) ?: 0.0
            run.maxAltitude = elevationList.maxOrNull()?.toDouble()?.div(10) ?: 0.0
            val pair = buildAscentAndDescent(motionDetailList)
            run.totalAscent = pair.first
            run.totalDescent = pair.second
            run.partTimeKmList = buildPartTimeMap(activityRecord.recDetail.workoutPaceEntityList, run)
        }
        val createSuccess = newRunApplicationService.handlerAndCreate(run)
        if (!createSuccess) return false

        val locationList = emptyList<NewUserRunDetailLocationItem>().toMutableList()
        val speedList = emptyList<NewUserRunDetailSpeedItem>().toMutableList()
        val heartRateList = emptyList<NewUserRunDetailHeartRateItem>().toMutableList()
        val stepRateList = emptyList<NewUserRunDetailStepRateItem>().toMutableList()
        val altitudeList = emptyList<NewUserRunDetailAltitudeItem>().toMutableList()

        val recDetail = activityRecord.recDetail
        recDetail.tracePointEntityList.forEach {
            locationList.add(buildLocation(it))
            altitudeList.add(buildAltitude(it))
        }
        detailDataList.forEach {
            var currentTime = it.startTime
            it.detailDataList.forEach { md ->
                speedList.add(buildSpeed(md, currentTime))
                heartRateList.add(buildHeartRate(md, currentTime))
                stepRateList.add(buildStepRate(md, currentTime))
                currentTime += it.interval * 1000
            }
        }

        val detail = mapUtils.toNewUserRunDetail(run)
        detail.lapList = buildLapList(run, activityRecord.recDetail.workoutPaceEntityList)
        detail.locationList = locationList
        detail.heartRateList = if (heartRateList.any { it.heartRate > 0 }) {
            heartRateList
        } else {
            emptyList()
        }
        detail.stepRateList = stepRateList
        detail.altitudeList = altitudeList
        detail.partTimeKmList = run.partTimeKmList
        detail.roundTwo()

        // 上传到七牛
        qiniuService.uploadObject(
            detail,
            "${run.trackFile}.gzip"
        )
        qiniuService.uploadObject(
            activityRecord,
            "honor_activity_detail_${run.trackFile}.gzip"
        )
        return true
    }

    /**
     * 创建每圈信息列表
     */
    private fun buildLapList(run: NewUserRun, paceList: List<HonorWorkoutPaceEntity>): List<NewUserRunDetailLapItem> {
        val paceDataList = paceList.flatMap { it.paceDataList }
            .filter { it.distance > 0 && it.unit == 0 && (it.distance * 1000) <= run.totalDistance }
            .sortedBy { it.distance }
        val partTimeKmMap = mutableMapOf<String, Int>()
        paceDataList.forEach {
            partTimeKmMap[it.distance.toString()] = it.pace
            if (it.distance == 21 && run.totalDistance >= 21097.5 && run.totalDistance < 22000) {
                partTimeKmMap[halfMarathon.toString()] = run.totalDuration
            } else if (it.distance == 42 && run.totalDistance >= 42195 && run.totalDistance < 43000) {
                partTimeKmMap[marathon.toString()] = run.totalDuration
            }
        }
        val fakePartTimeKmList = partTimeKmMap.toSortedMap { a, b -> a.toDouble().compareTo(b.toDouble()) }
        val itemList = mutableListOf<NewUserRunDetailLapItem>()
        fakePartTimeKmList.forEach {
            if (it.key.toDouble() % 1 == 0.0) {
                val item = NewUserRunDetailLapItem()
                item.distance = 1000.0
                item.duration = it.value
                itemList.add(item)
            }
        }
        if (run.totalDistance > itemList.size * 1000) {
            val item = NewUserRunDetailLapItem()
            item.distance = run.totalDistance - itemList.size * 1000
            item.duration = run.totalDuration - itemList.sumOf { it.duration }
            itemList.add(item)
        }
        return itemList
    }

    /**
     * 创建定位
     */
    private fun buildLocation(entity: HonorTracePointEntity): NewUserRunDetailLocationItem {
        return NewUserRunDetailLocationItem().apply {
            timestamp = entity.timestamp * 1000
            latitude = entity.latitude.round(8)
            longitude = entity.longitude.round(8)
            speed = entity.speed
        }
    }

    /**
     * 创建速度
     */
    private fun buildSpeed(entity: HonorMotionDetail, currentTime: Long): NewUserRunDetailSpeedItem {
        return NewUserRunDetailSpeedItem().apply {
            timestamp = currentTime * 1000
            speed = entity.speed.toDouble() / 10.0
        }
    }

    /**
     * 创建心率
     */
    private fun buildHeartRate(entity: HonorMotionDetail, currentTime: Long): NewUserRunDetailHeartRateItem {
        return NewUserRunDetailHeartRateItem().apply {
            timestamp = currentTime * 1000
            heartRate = entity.heartRate
        }
    }

    /**
     * 创建步频
     */
    private fun buildStepRate(entity: HonorMotionDetail, currentTime: Long): NewUserRunDetailStepRateItem {
        return NewUserRunDetailStepRateItem().apply {
            timestamp = currentTime * 1000
            stepRate = entity.stepFrequency
        }
    }

    /**
     * 创建海拔
     */
    private fun buildAltitude(entity: HonorTracePointEntity): NewUserRunDetailAltitudeItem {
        return NewUserRunDetailAltitudeItem().apply {
            this.timestamp = entity.timestamp * 1000
            this.altitude = entity.altitude.roundTwo()
        }
    }

    /**
     * 获取累计爬升和下降
     */
    private fun buildAscentAndDescent(motionDetailList: List<HonorMotionDetail>): Pair<Double, Double> {
        val elevationList = motionDetailList
            .filter { it.elevation != null }
            .map { it.elevation / 10 }
        var ascent = 0.0
        var descent = 0.0
        for (i in 1 until elevationList.size) {
            val prevElevation = elevationList[i - 1]
            val currentElevation = elevationList[i]
            if (currentElevation > prevElevation) {
                ascent += currentElevation - prevElevation
            } else if (currentElevation < prevElevation) {
                descent += prevElevation - currentElevation
            }
        }
        return Pair(ascent, descent)
    }

    /**
     * 创建每公里用时
     */
    private fun buildPartTimeMap(paceList: List<HonorWorkoutPaceEntity>, run: NewUserRun): Map<String, Int> {
        val paceDataList = paceList.flatMap { it.paceDataList }
            .filter { it.distance > 0 && it.unit == 0 && (it.distance * 1000) <= run.totalDistance }
            .sortedBy { it.distance }
        val partTimeKmMap = mutableMapOf<String, Int>()
        var currentDuration = 0
        paceDataList.forEach {
            currentDuration += it.pace
            partTimeKmMap[it.distance.toString()] = currentDuration
            if (it.distance == 21 && run.totalDistance >= 21097.5 && run.totalDistance < 22000) {
                partTimeKmMap[halfMarathon.toString()] = run.totalDuration
            } else if (it.distance == 42 && run.totalDistance >= 42195 && run.totalDistance < 43000) {
                partTimeKmMap[marathon.toString()] = run.totalDuration
            }
        }
        // 按距离排序，可能包含小数
        return partTimeKmMap.toSortedMap { a, b -> a.toDouble().compareTo(b.toDouble()) }
    }

}
