package com.gusto.upload.core.ezon

import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.model.entity.ezon.EzonUserAuthInfo
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.Instant

/**
 * 宜准-授权
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Service
class EzonAuthService : EzonCommonService() {

    private val log = LoggerFactory.getLogger(javaClass)

    /**
     * 授权回调
     */
    fun upsertAccessTokenByCode(code: String, userId: Long, openId: String): Boolean {
        val rsp = ezonClient.getAccessToken(
            clientId = config.clientId,
            clientSecret = config.clientSecret,
            code = code,
            redirectUri = config.redirectUri
        )
        if (rsp.isError) {
            log.error("[upsertAccessTokenByCode] get AT error, error = ${rsp.content}")
            return false
        }
        val authInfo = EzonUserAuthInfo().apply {
            this.userId = userId
            refreshToken = code
            accessToken = rsp.result.data.accessToken
            accessTokenUpdateTime = Instant.now()
            this.openId = openId
        }

        val userInfoRsp = ezonClient.getUserInfo(authInfo.accessToken)
        if (userInfoRsp.isSuccess) {
            authInfo.nickname = userInfoRsp.result.data.nickName
        }

        // 查询此宜准账号是否绑定了多个第一赛道账号
        val dbAuthInfoList = authInfoService.getListByOpenId(authInfo.openId)
        if (dbAuthInfoList.isNotEmpty()) {
            dbAuthInfoList.forEach {
                // 踢掉此宜准账号的其他第一赛道账号授权，不用请求接口，因为宜准是主动请求接口获取数据的
                authInfoService.removeById(it)
            }
        }
        // 插入新的授权信息
        authInfoService.save(authInfo)
        return true
    }

    /**
     * 根据用户ID取消授权
     */
    fun cancelAuthByUserId(userId: Long): Boolean {
        val authInfo = authInfoService.getOneByUserId(userId)
        checkOrRefreshAccessToken(authInfo)
        val rsp = ezonClient.cancelAuth(authInfo.accessToken)
        if (rsp.isError) {
            log.error("[cancelAuthByUserId] cancel error, error = ${rsp.content}")
            return false
        }
        if (rsp.result.resultId == 0) {
            authInfoService.removeById(authInfo)
            return true
        }
        log.error("[cancelAuthByUserId] cancel error, rsp = ${rsp.result.formatToJson()}")
        return false
    }

}
