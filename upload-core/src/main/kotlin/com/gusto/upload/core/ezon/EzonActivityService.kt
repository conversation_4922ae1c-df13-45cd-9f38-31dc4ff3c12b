package com.gusto.upload.core.ezon

import cn.hutool.core.date.DateTime
import com.gusto.framework.core.util.json.formatToJson
import com.gusto.framework.core.util.time.minusDays
import com.gusto.upload.core.application.user.NewUserRunApplicationService
import com.gusto.upload.core.common.PushService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.util.NewUserRunMapUtils
import com.gusto.upload.core.util.formatDate
import com.gusto.upload.core.utils.JsonUtils
import com.gusto.upload.core.utils.roundTwo
import com.gusto.upload.model.entity.ezon.EzonActivity
import com.gusto.upload.model.entity.ezon.EzonActivityDetail
import com.gusto.upload.model.entity.ezon.EzonActivityDetailLap
import com.gusto.upload.model.entity.ezon.EzonActivityDetailLocationPoint
import com.gusto.upload.model.entity.ezon.EzonUserAuthInfo
import com.gusto.upload.model.entity.response.ManualSyncRsp
import com.gusto.upload.model.entity.user.NewUserRun
import com.gusto.upload.model.entity.user.NewUserRunDetailAltitudeItem
import com.gusto.upload.model.entity.user.NewUserRunDetailHeartRateItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLapItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLocationItem
import com.gusto.upload.model.entity.user.NewUserRunDetailStepRateItem
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import kotlin.math.max
import kotlin.math.roundToInt

/**
 * 宜准-活动
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Service
class EzonActivityService : EzonCommonService() {

    private val log = LoggerFactory.getLogger(javaClass)
    private val objectMapper = JsonUtils.initObjectMapper(lowerCamelCase = true)

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var newRunApplicationService: NewUserRunApplicationService

    @Autowired
    lateinit var pushService: PushService

    @Autowired
    lateinit var mapUtils: NewUserRunMapUtils

    /**
     * 手动同步活动列表
     */
    fun syncActivityListByManual(userId: Long): ManualSyncRsp {
        val authInfo = authInfoService.getOneByUserId(userId)
        checkOrRefreshAccessToken(authInfo)
        val now = Instant.now()
        val startDate =
            Instant.ofEpochMilli(max(authInfo.createTime.toEpochMilli(), now.minusDays(7).toEpochMilli())).formatDate()
        val endDate = now.formatDate()
        val rsp = ezonClient.getActivityListByDate(
            authInfo.accessToken,
            startDate,
            endDate
        )
        val outRsp = ManualSyncRsp()
        outRsp.result = true
        outRsp.reason = "同步成功，请稍后在跑步记录页查看"
        if (rsp.isError) {
            log.error("[syncActivityListByManual] userId = $userId, get activity list error, response content = ${rsp.content}")
            outRsp.result = false
            outRsp.reason = "同步失败，请稍后再试"
            return outRsp
        }
        if (rsp.result.dataList == null || rsp.result.dataList.isEmpty()) {
            outRsp.reason = "暂无运动记录可同步"
            return outRsp
        }
        // 创建跑步记录头部和明细
        // createUserRunAndDetail(authInfo, rsp.result.dataList)
        asyncExecutor.execute {
            log.info("[syncActivityListByManual] userId = $userId, sync async start")
            createUserRunAndDetail(authInfo, rsp.result.dataList)
            log.info("[syncActivityListByManual] userId = $userId, sync async success")
        }
        return outRsp
    }

    /**
     * 创建跑步记录并上传到APP
     */
    private fun createUserRunAndDetail(
        authInfo: EzonUserAuthInfo,
        activityList: List<EzonActivity>
    ) {
        var syncSuccessCount = 0
        val runList = mutableListOf<NewUserRun>()
        activityList.forEach { activity ->
            val run = buildNewUserRun(authInfo.userId, activity)
            val logPre = "[handlerAndCreate] " +
                    "userId=${run.userId}, " +
                    "deviceId=${run.deviceId}, " +
                    "deviceType=${run.deviceType}, " +
                    "deviceModel=${run.deviceModel}, " +
                    "activityId=${run.startTime}"
            val createSuccess = try {
                uploadRunAndDetail(run, activity, authInfo)
            } catch (e: Exception) {
                log.error("$logPre, create run and detail error, ${e.message}")
                log.error("$logPre, create run and detail error, ${e.stackTraceToString()}")
                false
            }
            if (!createSuccess) return@forEach
            syncSuccessCount++
            runList.add(run)
        }
        log.info("[createUserRunAndDetail] userId = ${authInfo.userId}, sync success, count = $syncSuccessCount")
        if (syncSuccessCount == 0) return
        asyncExecutor.execute {
            val latestRun = runList.maxByOrNull { it.startTime }
            if (latestRun != null) {
                pushService.notifySyncSuccess(latestRun, syncSuccessCount, authInfo.userId)
            }
        }
    }

    /**
     * 创建新跑步记录
     */
    private fun buildNewUserRun(userId: Long, activity: EzonActivity): NewUserRun {
        val run = NewUserRun().apply {
            this.userId = userId
            deviceId = 3
            deviceType = 302
            deviceModel = activity.watchType
            activityType = formatActivityType(activity.movementTypeId)
            startTime = DateTime.of(activity.startTime, "yyyy-MM-dd'T'HH:mm:ss").time
            endTime = DateTime.of(activity.endTime, "yyyy-MM-dd'T'HH:mm:ss").time
            totalDistance = activity.totalMetre?.toDouble() ?: 0.0
            totalDuration = activity.duration
            totalStep = activity.totalStep ?: 0
            totalCalorie = activity.totalKcal?.toDouble() ?: 0.0
            averagePace = if (totalDistance > 0 && totalDuration > 0) 1000 / (totalDistance / totalDuration) else 0.0
            maxPace = 0.0
            averageHeartRate = 0
            maxHeartRate = 0
            averageStepRate =
                if (totalStep > 0 && totalDuration > 0) (totalStep.toDouble() / totalDuration * 60).roundToInt() else 0
            maxStepRate = 0
            averageStride = if (totalStep != 0) totalDistance * 100 / totalStep else 0.0
            minAltitude = 0.0
            maxAltitude = 0.0
            totalAscent = activity.totalAscent?.toDouble() ?: 0.0
            totalDescent = activity.totalDescent?.toDouble() ?: 0.0
            weather = ""
            province = ""
            city = ""
            temperature = ""
            humidity = ""
            thirdActivityId = activity.id.toString()
            thirdActivityType = activity.movementTypeId.toString()
            abnormal = 8
            externalSportType = activity.movementTypeId.toString()
            mapTrackImage = ""
            trackImage = ""
            partTimeKmList = emptyMap()
        }
        return run
    }

    /**
     * 转换活动类型
     *
     * 运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它
     */
    private fun formatActivityType(activityType: Int): Int {
        return when (activityType) {
            0 -> 1 // 户外跑 -> 户外跑
            1 -> 2 // 室内跑 -> 室内跑
            2 -> 5 // 骑行 -> 骑车
            3 -> 4 // 越野跑 -> 越野
            4, 5 -> 6 // 泳池游泳,室外游泳 -> 游泳
            6 -> 3 // 登山 -> 徒步
            7 -> 3 // 徒步 -> 徒步
            8 -> 100 // 滑雪 -> 其它
            9 -> 100 // 间歇训练 -> 其它
            11 -> 100 // 计时码表 -> 其它
            12 -> 1 // 无轨迹户外跑 -> 户外跑
            13 -> 5 // 无轨迹骑行 -> 骑车
            14 -> 3 // 无轨迹登山 -> 徒步
            15 -> 3 // 无轨迹徒步 -> 徒步
            16 -> 100 // 羽毛球 -> 其它
            17 -> 6  // 泳池游泳 -> 游泳
            18 -> 2  // 室内跑步 -> 室内跑
            20 -> 100 // 椭圆机 -> 其它
            21 -> 5 // 动感单车 -> 骑车
            22 -> 100 // 瑜伽 -> 其它
            23 -> 100 // 健身 -> 其它
            24 -> 100 // 力量训练 -> 其它
            25 -> 100 // 秒表 -> 其它
            26 -> 100 // 有氧运动 -> 其它
            27 -> 3  // 健步 -> 徒步
            28 -> 2  // 跑步机 -> 室内跑
            29 -> 100 // 跳绳 -> 其它
            30 -> 100 // 铁三 -> 其它
            31 -> 1  // 操场自由跑 -> 户外跑
            32 -> 5  // 室内骑行 -> 骑车
            33 -> 100 // 赛艇 -> 其它
            34 -> 100 // 激流赛艇 -> 其它
            35 -> 100 // 划船 -> 其它
            36 -> 100 // 激流划船 -> 其它
            37 -> 100  // 室内有氧 -> 其它
            38 -> 100  // 户外有氧 -> 其它
            39 -> 1  // 户外间歇跑 -> 户外跑
            40 -> 2  // 室内间歇跑 -> 室内跑
            41 -> 1  // 操场间歇跑 -> 户外跑
            42 -> 5  // 户外间歇骑行 -> 骑车
            43 -> 100 // 划船机 -> 其它
            44 -> 100 // 训练计划 -> 其它
            45 -> 100  // 室内燃脂 -> 其它
            46 -> 100  // 室内减压 -> 其它
            47 -> 100  // 室内进阶 -> 其它
            48 -> 100  // 户外燃脂 -> 其它
            49 -> 100  // 户外减压 -> 其它
            50 -> 100  // 户外进阶 -> 其它
            51 -> 1  // 马拉松 -> 户外跑
            else -> 100 // 默认其它
        }
    }

    /**
     * 上传跑步记录和明细
     */
    private fun uploadRunAndDetail(run: NewUserRun, activity: EzonActivity, authInfo: EzonUserAuthInfo): Boolean {
        val activityDetail =
            getActivityDetail(authInfo, activity.id) ?: return newRunApplicationService.handlerAndCreate(run)
        val locList = activityDetail.locList ?: emptyList()
        val eleList = locList.mapNotNull { it.ele }
        val hrList = activityDetail.hrList ?: emptyList()
        val cadenceList = activityDetail.cadenceList ?: emptyList()
        val paceList = activityDetail.paceList ?: emptyList()
        run.apply {
            maxPace = paceList.filter { it > 0 }.minOrNull()?.toDouble() ?: 0.0
            if (hrList.isNotEmpty()) {
                averageHeartRate = hrList.filter { it > 0 }.average().toInt()
                maxHeartRate = hrList.maxOrNull()?.toInt() ?: 0
            }
            if (cadenceList.isNotEmpty()) {
                maxStepRate = cadenceList.maxOrNull()?.toInt() ?: 0
            }
            if (eleList.isNotEmpty()) {
                minAltitude = eleList.minOrNull() ?: 0.0
                maxAltitude = eleList.maxOrNull() ?: 0.0
                // val pair = buildAscentAndDescent(eleList)
                // totalAscent = pair.first
                // totalDescent = pair.second
            }
            partTimeKmList = buildPartTimeMap(activityDetail)
        }
        val createSuccess = newRunApplicationService.handlerAndCreate(run)
        if (createSuccess) {
            val startTime = run.startTime
            val locationList = locList.map { buildLocation(startTime, it, paceList) }
            val lapList =
                (activityDetail.laps ?: emptyList()).map { buildLap(it) }.toMutableList()
            val sumLapDistance = lapList.sumOf { it.distance }
            if (run.totalDistance - sumLapDistance > 0) {
                val lastLap = NewUserRunDetailLapItem()
                lastLap.distance = run.totalDistance - sumLapDistance
                lastLap.duration = run.totalDuration - lapList.sumOf { it.duration }
                lapList.add(lastLap)
            }
            val heartRateList =
                hrList.mapIndexed { index, heart -> buildHeartRate(startTime, index, heart) }
            val stepRateList =
                cadenceList.mapIndexed { index, stepRate -> buildStepRate(startTime, index, stepRate) }
            val altitudeList = locList
                .filter { it.ele != null }
                .map { buildAltitude(startTime, it) }
            val detail = mapUtils.toNewUserRunDetail(run)
            detail.lapList = lapList
            detail.locationList = locationList
            detail.heartRateList = heartRateList
            detail.stepRateList = stepRateList
            detail.altitudeList = altitudeList
            detail.partTimeKmList = run.partTimeKmList
            asyncExecutor.execute {
                // 上传到七牛
//                qiniuService.uploadString(
//                    objectMapper.writeValueAsString(detail),
//                    "${run.trackFile}.gzip"
//                )
//                qiniuService.uploadString(
//                    objectMapper.writeValueAsString(activityDetail),
//                    "ezon_activity_detail_${run.trackFile}.gzip"
//                )
                qiniuService.uploadObject(
                    detail,
                    "${run.trackFile}.gzip"
                )
                qiniuService.uploadObject(
                    activityDetail,
                    "ezon_activity_detail_${run.trackFile}.gzip"
                )
            }
        }
        return createSuccess
    }

    /**
     * 创建每公里用时
     */
    private fun buildPartTimeMap(activityDetail: EzonActivityDetail): Map<String, Int> {
        if (activityDetail.laps == null || activityDetail.laps.isEmpty()) {
            return emptyMap()
        }
        val partTimeKmMap = mutableMapOf<String, Int>()
        var currentKm = 0.0
        var currentDuration = 0.0
        activityDetail.laps.forEach {
            if (it.lapMetre == 1000) {
                currentKm += 1
                currentDuration += it.lapSec
                partTimeKmMap[currentKm.toString()] = currentDuration.toInt()
            }
        }
        return partTimeKmMap
    }

    /**
     * 获取累计爬升和下降
     */
    private fun buildAscentAndDescent(altitudeList: List<Double>): Pair<Double, Double> {
        var ascent = 0.0
        var descent = 0.0
        for (i in 1 until altitudeList.size) {
            val prevElevation = altitudeList[i - 1]
            val currentElevation = altitudeList[i]
            if (currentElevation > prevElevation) {
                ascent += currentElevation - prevElevation
            } else if (currentElevation < prevElevation) {
                descent += prevElevation - currentElevation
            }
        }
        return Pair(ascent, descent)
    }

    /**
     * 创建轨迹
     */
    private fun buildLocation(
        startTime: Long,
        location: EzonActivityDetailLocationPoint,
        paceList: List<Int>
    ): NewUserRunDetailLocationItem {
        val timestamp = startTime + location.dur * 1000
        val item = NewUserRunDetailLocationItem()
        item.timestamp = timestamp
        item.latitude = location.lat
        item.longitude = location.lon
        val pace = paceList.getOrElse(location.dur) { 0 }
        item.speed = if (pace > 0) {
            (1000.0 / pace).roundTwo()
        } else {
            0.0
        }
        return item
    }

    /**
     * 创建海拔
     */
    private fun buildAltitude(
        startTime: Long,
        location: EzonActivityDetailLocationPoint
    ): NewUserRunDetailAltitudeItem {
        val item = NewUserRunDetailAltitudeItem()
        item.timestamp = startTime + location.dur * 1000
        item.altitude = location.ele
        return item
    }

    /**
     * 创建心率
     */
    private fun buildHeartRate(startTime: Long, index: Int, heartRate: Int): NewUserRunDetailHeartRateItem {
        val item = NewUserRunDetailHeartRateItem()
        item.timestamp = startTime + index * 20 * 1000
        item.heartRate = heartRate
        return item
    }

    /**
     * 创建步频
     */
    private fun buildStepRate(startTime: Long, index: Int, stepRate: Int): NewUserRunDetailStepRateItem {
        val item = NewUserRunDetailStepRateItem()
        item.timestamp = startTime + index * 20 * 1000
        item.stepRate = stepRate
        return item
    }

    /**
     * 创建每圈信息
     */
    private fun buildLap(ezonLap: EzonActivityDetailLap): NewUserRunDetailLapItem {
        val item = NewUserRunDetailLapItem()
        item.distance = ezonLap.lapMetre.toDouble()
        item.duration = ezonLap.lapPace
        return item
    }

    /**
     * 获取运动详情
     */
    private fun getActivityDetail(authInfo: EzonUserAuthInfo, activityId: Long): EzonActivityDetail? {
        val rsp = ezonClient.getActivityDetail(authInfo.accessToken, activityId)
        if (rsp.isError) {
            log.error("[getActivityDetail] get activity detail error, error = ${rsp.content}")
            return null
        }
        if (rsp.result.resultId != 0) {
            log.error("[getActivityDetail] get activity detail, rsp = ${rsp.result.formatToJson()}")
            return null
        }
        return rsp.result.data
    }

}
