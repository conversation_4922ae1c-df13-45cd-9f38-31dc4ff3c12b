package com.gusto.upload.core.vivo

import com.dtflys.forest.annotation.BaseRequest
import com.dtflys.forest.annotation.HTTPProxy
import com.dtflys.forest.annotation.Header
import com.dtflys.forest.annotation.JSONBody
import com.dtflys.forest.annotation.LogEnabled
import com.dtflys.forest.annotation.Post
import com.dtflys.forest.annotation.Retry
import com.dtflys.forest.http.ForestResponse
import com.gusto.upload.model.entity.vivo.req.VivoGetAccessTokenReq
import com.gusto.upload.model.entity.vivo.req.VivoGetActivityDetailReq
import com.gusto.upload.model.entity.vivo.req.VivoGetActivityListReq
import com.gusto.upload.model.entity.vivo.req.VivoRefreshAccessTokenReq
import com.gusto.upload.model.entity.vivo.rsp.VivoAccessTokenRsp
import com.gusto.upload.model.entity.vivo.rsp.VivoCommonRsp
import com.gusto.upload.model.entity.vivo.rsp.VivoGetActivityDetailRsp
import com.gusto.upload.model.entity.vivo.rsp.VivoGetUserInfoRsp

/**
 * <AUTHOR>
 * @since 2024-04-15
 */
@BaseRequest(
    baseURL = "#{upload.vivo.apiHost}",
    headers = ["Content-type: application/json; charset=UTF-8"]
)
// @HTTPProxy(host = "************", port = "23333", username = "gusto", password = "gusto1234.")
@Retry(maxRetryCount = "5", maxRetryInterval = "100")
interface VivoClient {

    /**
     * 1、获取用户token
     */
    @Post("/cloud/token")
    fun getAccessToken(
        @JSONBody req: VivoGetAccessTokenReq,
        @Header headers: Map<String, Any>
    ): ForestResponse<VivoCommonRsp<VivoAccessTokenRsp>>

    /**
     * 2、刷新用户token
     */
    @Post("/cloud/refreshToken")
    fun refreshAccessToken(
        @JSONBody req: VivoRefreshAccessTokenReq,
        @Header headers: Map<String, Any>
    ): ForestResponse<VivoCommonRsp<VivoAccessTokenRsp>>

    /**
     * 27、应用解绑
     */
    @Post("/cloud/oauth/unAuthorize")
    fun cancelAuth(
        @JSONBody empty: Any,
        @Header headers: Map<String, Any>
    ): ForestResponse<VivoCommonRsp<Any>>

    /**
     * 31、获取健康app运动记录列表
     */
    @Post("/cloud/exercises")
    fun getActivityList(
        @JSONBody req: VivoGetActivityListReq,
        @Header headers: Map<String, Any>
    ): ForestResponse<VivoCommonRsp<List<String>>>

    /**
     * 32、获取健康app运动记录详情
     */
    @LogEnabled(logResponseContent = false)
    @Post("/cloud/exercise")
    fun getActivityDetail(
        @JSONBody req: VivoGetActivityDetailReq,
        @Header headers: Map<String, Any>
    ): ForestResponse<VivoCommonRsp<VivoGetActivityDetailRsp>>

    /**
     * 4、查询健康服务用户信息列表
     */
    @Post("/cloud/user/query")
    fun getUserInfo(
        @JSONBody empty: Any,
        @Header headers: Map<String, Any>
    ): ForestResponse<VivoCommonRsp<List<VivoGetUserInfoRsp>>>

}
