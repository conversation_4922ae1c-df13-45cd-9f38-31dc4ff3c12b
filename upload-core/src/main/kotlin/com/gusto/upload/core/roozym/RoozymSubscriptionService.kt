package com.gusto.upload.core.roozym

import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.core.roozym.producer.RoozymActivityProducer
import com.gusto.upload.core.roozym.util.toMessageOrNull
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.roozym.RoozymAuthInfo
import com.gusto.upload.model.entity.roozym.RoozymSubscriptionEventType
import com.gusto.upload.model.entity.roozym.req.RoozymNotificationReq
import com.gusto.upload.model.entity.roozym.req.RoozymUpsertSubscriptionReq
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.util.DigestUtils

/**
 * 如骏-订阅服务
 *
 * <AUTHOR>
 * @since 2022/6/1
 */
@Service
class RoozymSubscriptionService {
    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var activityProducer: RoozymActivityProducer

    @Autowired
    lateinit var authInfoService: RoozymUserAuthInfoService

    @Autowired
    lateinit var roozymClientService: RoozymClientService

    /**
     * 用户事件处理
     */
    fun handler(req: RoozymNotificationReq) {
        val user = authInfoService.getOneByOpenId(req.openId)
        if (user == null) {
            log.warn("[roozym:handler] openId = ${req.openId}, sync error, message = authInfo not found")
            return
        }
        log.debug("[roozym:handler] userId = ${user.userId}, req = ${req.formatToJson()}")
        when (RoozymSubscriptionEventType.forDesc(req.eventType)) {
            // 创建/更新运动记录事件
            RoozymSubscriptionEventType.EVENT_SPORT_ADD, RoozymSubscriptionEventType.EVENT_SPORT_UPDATE -> {
                val activityMessage = req.toMessageOrNull(user.userId)
                log.debug("[roozym:handler] userId = ${user.userId}, activityMessage = $activityMessage")
                activityProducer.sendSubscriptionMessage(activityMessage)
            }

            else -> return
        }
    }

    /**
     * 新增/更新订阅记录（只能有一处调用，避免authInfo被乱更新）
     */
    fun upsert(uncheckAuthInfo: RoozymAuthInfo) {
        val req = RoozymUpsertSubscriptionReq()
        req.openId = uncheckAuthInfo.openId
        val eventTypeList = emptyList<String>().toMutableList()
        eventTypeList.add("event_sport_add")
        eventTypeList.add("event_auth_cancel")
        req.eventType = eventTypeList
        req.status = 1
        val (rsp, authInfo) = roozymClientService.upsertSubscription(req, uncheckAuthInfo)
        if (rsp.isError) {
            log.warn("[Roozym:upsert] userId = ${authInfo.userId} authInfo = ${authInfo} req error, error = ${rsp.content}")
            throw ServiceException(UploadErrorCode.ROOZYM_UPSERT_SUBSCRIPTION_ERROR)
        }
        // authInfo.openId = rsp.result.first().openId
        authInfoService.updateWithCache(authInfo)
    }

    /**
     * 如骏签名认证
     */
    fun sign(req: RoozymNotificationReq, timestamp: String): String? {
        val authInfo = authInfoService.getOneByOpenId(req.openId)
        if (authInfo == null) {
            log.warn("[roozym:sign] openId = ${req.openId}, sync error, message = authInfo not found")
            return null
        }
        val sign =
            "${req.openId}${req.startTime}${req.endTime}${req.sportType}${req.eventType}$timestamp${authInfo.refreshToken}"
        log.debug("[Roozym:sign] sign = $sign")
        return DigestUtils.md5DigestAsHex(sign.toByteArray())
    }
}
