package com.gusto.upload.core.common

import com.gusto.upload.core.service.app.OnlineConfigService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class MatchConfigService {

    @Autowired
    lateinit var configService: OnlineConfigService

    fun getPushTokenName(): String =
        configService.getString("Push_API_Token_Name", "gutoken-biz")

    fun getPushTokenValue(): String =
        configService.getString("Push_API_Token_Value", "12bf28f6f8ac46efa0d101f0cdf68e94")

    fun getTestDeviceId(): Long =
        configService.getLong("Upload_TestDeviceId", 7)

    fun getTestUserIdList(): List<Long> =
        configService.getLongList("Upload_TestUserIdList", listOf(1152948L))

    fun getLogLevelMap(): Map<Int, String> = configService.getIntStringMap("Upload_LogLevelMap", emptyMap())

    fun getNotifyRecordAlertCount(): Int = configService.getInteger("Upload_NotifyRecordAlertCount", 30)

    fun getCheatDistanceLimit(): Double = configService.getDouble("Upload_CheatDistanceLimit", 22.0)

    fun getMiniProgramPagePath(): String =
        configService.getString("Upload_MiniProgramPagePath", "/pages/sport/sport-detail/sport-detail?recordId=")

    // 高德API key
    fun getAmapApiKey(): String = configService.getString("Amap_ApiKey", "7e59bc5a734f31023bad62aa5db68d86")

}
