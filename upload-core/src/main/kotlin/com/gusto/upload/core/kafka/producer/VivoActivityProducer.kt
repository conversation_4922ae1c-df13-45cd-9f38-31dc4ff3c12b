package com.gusto.upload.core.kafka.producer

import com.gusto.upload.model.entity.message.VivoActivityMessage
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.support.SendResult
import org.springframework.stereotype.Component
import org.springframework.util.concurrent.ListenableFutureCallback

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Component
class VivoActivityProducer {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var kafkaTemplate: KafkaTemplate<String, Any>

    fun sendNotifyActivityMessage(message: VivoActivityMessage) {
        val topic = VivoActivityMessage.NOTIFY_ACTIVITY_TOPIC
        val feature = kafkaTemplate.send(topic, message)
        feature.addCallback(object : ListenableFutureCallback<SendResult<String, Any>> {
            override fun onFailure(ex: Throwable) {
                log.error("[sendNotifyActivityMessage] [onFailure] message = $message, cause = ${ex.message}")
            }

            override fun onSuccess(result: SendResult<String, Any>) {
                log.info("[sendNotifyActivityMessage] [onSuccess] topic = $topic, message = ${result.producerRecord.value()}")
            }
        })
    }

}
