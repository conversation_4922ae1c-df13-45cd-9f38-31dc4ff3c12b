package com.gusto.upload.core.service

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.gusto.upload.core.dao.DeviceDao
import com.gusto.upload.model.entity.Device
import org.springframework.stereotype.Service

/**
 * 设备 服务类
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Service
class DeviceService : ServiceImpl<DeviceDao, Device>() {
    /**
     * 根据上线状态获取列表
     */
    fun getListByOnlineState(onlineState: Int): List<Device> {
        val list = list().sortedByDescending { it.sort }
        return when (onlineState) {
            // 默认-全部
            0 -> list
            else -> list.filter { it.onlineState == onlineState }
        }
    }

    /**
     * 根据设备ID获取
     */
    fun getOneByDeviceId(deviceId: Long): Device {
        val query = QueryWrapper<Device>()
        query.eq(Device.DEVICE_ID_FIELD, deviceId)
        return getOne(query)
    }
}
