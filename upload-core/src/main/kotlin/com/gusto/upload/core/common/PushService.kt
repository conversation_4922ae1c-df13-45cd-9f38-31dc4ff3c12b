package com.gusto.upload.core.common

import cn.hutool.core.util.IdUtil
import cn.hutool.core.util.StrUtil
import com.gusto.push.common.model.entity.wx.req.WxMpTemplateDataDTO
import com.gusto.push.common.model.entity.wx.req.WxMpTemplateMessageDTO
import com.gusto.push.common.model.entity.wx.req.WxMpTemplateMessageDTOList
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.util.formatDateHourMinuteSecond
import com.gusto.upload.core.utils.formatDuration
import com.gusto.upload.core.utils.formatPaceString
import com.gusto.upload.model.entity.user.NewUserRun
import com.gusto.upload.model.entity.user.User
import com.gusto.upload.model.entity.user.old.UserRun
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant

/**
 * 微信公众号提醒工具类
 *
 * <AUTHOR>
 * @since 2021-11-04
 */
@Service
class PushService {

    private val log = LoggerFactory.getLogger(javaClass)

    // 运动记录同步成功提醒 / 文体娱乐 - 体育
    private val syncSuccessNotifyTemplateId = "sO9V0z4h-7DaH2fhVEwpyYIvODKVDN7g_s6oDM_dIuk"

    @Autowired
    lateinit var pushClient: PushClient

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var configService: MatchConfigService

    private fun getHeader(): Map<String, Any> {
        val pushTokenName: String = configService.getPushTokenName()
        val pushTokenValue: String = configService.getPushTokenValue()
        val headers = HashMap<String, Any>()
        headers["Content-type"] = "application/json; charset=UTF-8"
        headers[pushTokenName] = pushTokenValue
        return headers
    }

    /**
     * 如骏手表同步成功通知
     */
    fun notifySyncSuccessFromRoozym(latestRun: UserRun, syncSuccessCount: Int, user: User) {
        if (StrUtil.isEmpty(user.openId)) {
            log.info("[notifySyncSuccessFromRoozym] notify skip, userId = ${user.userId} openId is empty or null")
            return
        }
        val first = WxMpTemplateDataDTO(
            name = "first",
            value = "您的如骏运动已成功同步至第一赛道，新增${syncSuccessCount}条运动记录，最新一条记录：",
            color = ""
        )
        val keyword1 = WxMpTemplateDataDTO(
            name = "keyword1",
            value = "【如骏】${String.format("%.2f", latestRun.distance / 1000f)}km",
            color = ""
        )
        val keyword2 = WxMpTemplateDataDTO(
            name = "keyword2",
            value = "【${
                Instant.ofEpochMilli(latestRun.startTime).formatDateHourMinuteSecond()
            }】${latestRun.duration.toInt().formatDuration()}",
            color = ""
        )
        val keyword3 = WxMpTemplateDataDTO(
            name = "keyword3",
            value = latestRun.averagePace.toDouble().formatPaceString(),
            color = ""
        )
        val remark = WxMpTemplateDataDTO(
            name = "remark",
            value = "太赞了，继续加油哦！",
            color = ""
        )
        val message = WxMpTemplateMessageDTO(
            toUser = user.openId,
            templateId = syncSuccessNotifyTemplateId,
            dataList = listOf(first, keyword1, keyword2, keyword3, remark),
            url = "",
            bizPushId = IdUtil.simpleUUID()
        )
        val req = WxMpTemplateMessageDTOList(messageList = listOf(message))
        pushClient.batchSendWxMpTemplateMessageAsync(req, getHeader())
        log.info("[notifySyncSuccessFromRoozym] notify success")
    }

    /**
     * 手表同步成功通知
     */
    fun notifySyncSuccess(latestRun: NewUserRun, syncSuccessCount: Int, userId: Long) {
        val user = userService.getOneById(userId)
        if (StrUtil.isEmpty(user.openId)) {
            log.info("[notifySyncSuccess] notify skip, userId = ${user.userId} openId is empty or null")
            return
        }
        val device = when (latestRun.deviceType) {
            301 -> "佳明"
            302 -> "宜准"
            303 -> "高驰"
            304 -> "华为运动健康"
            305 -> "Apple健康"
            306 -> "如骏"
            307 -> "佳明海外"
            308 -> "欢太健康"
            309 -> "vivo健康"
            310 -> "腕宝"
            311 -> "荣耀运动健康"
            312 -> "Zepp华米"
            else -> "手表"
        }
        val first = WxMpTemplateDataDTO(
            name = "first",
            value = "您的${device}已成功同步至第一赛道，新增${syncSuccessCount}条运动记录，最新一条记录：",
            color = ""
        )
        val keyword1 = WxMpTemplateDataDTO(
            name = "keyword1",
            value = "【${device}】${String.format("%.2f", latestRun.totalDistance / 1000f)}km",
            color = ""
        )
        val keyword2 = WxMpTemplateDataDTO(
            name = "keyword2",
            value = "【${
                Instant.ofEpochMilli(latestRun.startTime).formatDateHourMinuteSecond()
            }】 ${latestRun.totalDuration.toInt().formatDuration()}",
            color = ""
        )
        val keyword3 = WxMpTemplateDataDTO(
            name = "keyword3",
            value = latestRun.averagePace.toDouble().formatPaceString(),
            color = ""
        )
        val remark = WxMpTemplateDataDTO(
            name = "remark",
            value = "太赞了，继续加油哦！",
            color = ""
        )
        val message = WxMpTemplateMessageDTO(
            toUser = user.openId,
            templateId = syncSuccessNotifyTemplateId,
            dataList = listOf(first, keyword1, keyword2, keyword3, remark),
            url = configService.getMiniProgramPagePath() + latestRun.recordId,
            bizPushId = IdUtil.simpleUUID(),
            jumpType = 2
        )
        val req = WxMpTemplateMessageDTOList(messageList = listOf(message))
        pushClient.batchSendWxMpTemplateMessageAsync(req, getHeader())
        log.info("[notifySyncSuccess] notify success")
    }

}
