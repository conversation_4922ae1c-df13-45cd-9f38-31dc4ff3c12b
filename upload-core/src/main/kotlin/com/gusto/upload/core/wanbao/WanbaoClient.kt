package com.gusto.upload.core.wanbao

import com.dtflys.forest.annotation.Body
import com.dtflys.forest.annotation.Get
import com.dtflys.forest.annotation.Header
import com.dtflys.forest.annotation.JSONBody
import com.dtflys.forest.annotation.LogEnabled
import com.dtflys.forest.annotation.Post
import com.dtflys.forest.annotation.Query
import com.dtflys.forest.http.ForestResponse
import com.gusto.upload.model.entity.wanbao.rsp.WanbaoCommonRsp
import com.gusto.upload.model.entity.wanbao.rsp.WanbaoGetActivityDetailRsp
import com.gusto.upload.model.entity.wanbao.rsp.WanbaoGetActivityPageRsp
import com.gusto.upload.model.entity.wanbao.rsp.WanbaoOAuthTokenRsp

/**
 * <AUTHOR>
 * @since 2024-09-12
 */
interface WanbaoClient {

    /**
     * 根据授权码换取accessToken和refreshToken
     * accessToken有效期30天
     */
    @Post("#{upload.wanbao.authHost}/oauth/token")
    fun getAccessToken(
        @Body("grant_type") grantType: String = "authorization_code",
        @Body("scope") scope: String = "all",
        @Body("code") code: String,
        @Body("redirect_uri") redirectUri: String,
        @Body("tenantId") tenantId: String = "000000",
        @Header headers: Map<String, Any>
    ): ForestResponse<WanbaoOAuthTokenRsp>

    /**
     * 根据refreshToken刷新accessToken
     * refreshToken有效期90天，刷新accessToken同时会刷新refreshToken的有效期。若refreshToken失效，需要重新授权。
     */
    @Post("#{upload.wanbao.authHost}/oauth/token", contentType = "application/x-www-form-urlencoded")
    fun refreshAccessToken(
        @Body("grant_type") grantType: String = "refresh_token",
        @Body("scope") scope: String = "all",
        @Body("source") source: String = "gusto",
        @Body("tenant_id") tenantId: String = "000000",
        @Body("refresh_token") refreshToken: String,
        @Header headers: Map<String, Any>
    ): ForestResponse<WanbaoOAuthTokenRsp>

    /**
     * 解除授权
     */
    @Get("#{upload.wanbao.apiHost}/diving-member/app/member/thirdConfig/unbind")
    fun cancelAuth(
        @Query("source") source: String = "gusto",
        @Header headers: Map<String, Any>
    ): ForestResponse<WanbaoCommonRsp<Any>>

    /**
     * 运动记录查询（不包含轨迹GPS点等额外信息），日期最大范围30天
     */
    @Post("#{upload.wanbao.apiHost}/diving-sport/app/sport/record/list-by-third")
    fun getActivityListByDate(
        @JSONBody("source") source: String = "gusto",
        @JSONBody("current") current: Int,
        @JSONBody("size") size: Int,
        @JSONBody("startTime") startTime: String,
        @JSONBody("endTime") endTime: String,
        @Header headers: Map<String, Any>
    ): ForestResponse<WanbaoCommonRsp<WanbaoGetActivityPageRsp>>

    /**
     * 运动记录查询（包含轨迹GPS点等额外信息）
     */
    @LogEnabled(logResponseContent = false)
    @Post("#{upload.wanbao.apiHost}/diving-sport/app/sport/record/detail-by-third")
    fun getActivityDetail(
        @JSONBody("sportType") sportType: String,
        @JSONBody("countId") countId: Long,
        @Header headers: Map<String, Any>
    ): ForestResponse<WanbaoCommonRsp<WanbaoGetActivityDetailRsp>>

    /**
     * 运动记录查询（包含轨迹GPS点等额外信息）
     */
    @LogEnabled(logResponseContent = false)
    @Get("{0}")
    fun getActivityDetailByFileUrl(
        fileUrl: String
    ): ForestResponse<String>

}
