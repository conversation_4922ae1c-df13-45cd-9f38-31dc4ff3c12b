package com.gusto.upload.core.huawei

import cn.hutool.core.util.StrUtil
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.isNotNullOrEmpty
import com.gusto.upload.core.kafka.producer.HuaweiActivityProducer
import com.gusto.upload.core.qiniu.QiniuService
import com.gusto.upload.core.utils.huawei.toUserAuthInfo
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.huawei.HuaweiProperties
import com.gusto.upload.model.entity.huawei.HuaweiUserAuthInfo
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import java.net.URLEncoder
import java.time.Instant

/**
 * 华为 通用 服务类
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
abstract class HuaweiService {
    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var config: HuaweiProperties

    @Autowired
    lateinit var huaweiClient: HuaweiClient

    @Autowired
    lateinit var authInfoService: HuaweiUserAuthInfoService

    @Autowired
    lateinit var activityProducer: HuaweiActivityProducer

    @Autowired
    lateinit var qiniuService: QiniuService

    /**
     * 检查用户授权信息是否过期，如果过期则刷新
     */
    fun checkOrRefreshAccessToken(authInfo: HuaweiUserAuthInfo, fromSub: Boolean = false): HuaweiUserAuthInfo? {
        return refreshAccessToken(authInfo, fromSub)
    }

    /**
     * 刷新AT（access_type=offline）
     * PS：access_type为offline，刷新AT时，会一并返回RT，需要比较新的RT与当前正在使用的RT是否有变化，如果有变化，则立即更换并使用新的RT
     */
    private fun refreshAccessToken(authInfo: HuaweiUserAuthInfo, fromSub: Boolean = false): HuaweiUserAuthInfo? {
        val rsp = huaweiClient.refreshAccessToken(
            refreshToken = URLEncoder.encode(authInfo.refreshToken, "UTF-8"),
            clientId = config.clientId,
            clientSecret = config.clientSecret
        )
        if (rsp.isError) {
            log.warn("[refreshAccessToken] refresh AT error, error = ${rsp.content}")
            authInfo.deleted = true
            authInfoService.updateWithCache(authInfo)
            if (fromSub) {
                return null
            } else {
                throw ServiceException(UploadErrorCode.HUAWEI_REFRESH_ACCESS_TOKEN_ERROR) // TODO 报错后续如何提示用户
            }
        }
        val newAuthInfo = rsp.result.toUserAuthInfo(authInfo.userId)
        authInfo.updateAccessToken(newAuthInfo.accessToken)
        if (newAuthInfo.refreshToken.isNotNullOrEmpty()) {
            authInfo.refreshToken = newAuthInfo.refreshToken
        }
        authInfo.nickname = newAuthInfo.nickname
        if (StrUtil.isEmpty(authInfo.avatar) && StrUtil.isNotEmpty(newAuthInfo.avatar)) {
            val avatar = "huawei_avatar_${authInfo.userId}_${Instant.now().epochSecond}.jpg"
            qiniuService.uploadUrl(newAuthInfo.avatar, avatar)
            authInfo.avatar = "https://file.gusto.cn/$avatar"
        }
        authInfoService.updateWithCache(authInfo)
        return authInfo
    }

    /**
     * 获取统一HTTPS请求头
     */
    protected fun getHttpsHeaders(authInfo: HuaweiUserAuthInfo): Map<String, Any> {
        val headers = HashMap<String, Any>()
        // 必填
        headers["Content-type"] = "application/json"
        // 必填
        headers["Authorization"] = "Bearer ${authInfo.accessToken}"
        // 非必填 开放联盟分配的应用标识。服务端可以基于其进行灰度路由，建议携带
        headers["x-client-id"] = config.clientId
        // 非必填 接口调用方的软件版本号。即当前客户端版本号，服务端可以基于其进行灰度，建议携带
        headers["x-version"] = config.outApiVersion
        // 非必填 请求跟踪ID。用于串联服务调用方与服务端整体请求链条，建议携带
        headers["x-caller-trace-id"] = "${authInfo.userId}-${Instant.now().toEpochMilli()}"
        return headers
    }
}