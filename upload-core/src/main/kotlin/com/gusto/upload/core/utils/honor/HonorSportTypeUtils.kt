package com.gusto.upload.core.utils.honor

/**
 * 荣耀-运动类型工具类
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
object HonorSportTypeUtils {
    /**
     * 运动类型转换
     */
    fun formatSportType(dataType: String): Int {
        // 1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它
        // https://developer.honor.com/cn/docs/11005/guides/code-guide/web/request-sports-record
        return when (dataType) {
            // 户外跑
            "RECORD_RUNNING_OUTDOOR" -> 1
            // 室内跑
            "RECORD_RUNNING_INDOOR" -> 2
            // 徒步
            "RECORD_WALKING_OUTDOOR" -> 3
            // 骑车
            "RECORD_RIDING" -> 5
            else -> 100
        }
    }
}