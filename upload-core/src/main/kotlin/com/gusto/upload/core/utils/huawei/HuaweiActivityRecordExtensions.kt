package com.gusto.upload.core.utils.huawei

import com.gusto.upload.core.utils.huawei.HuaweiSportTypeUtils.formatSportType
import com.gusto.upload.model.entity.huawei.HuaweiActivityRecord
import com.gusto.upload.model.entity.user.NewUserRun
import com.gusto.upload.model.entity.user.old.UserRun
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId

/**
 * 将华为采样点转化为轨迹明细
 */
fun HuaweiActivityRecord.toUserRun(userId: Long): UserRun {
    val summaryMap = this.activitySummary.dataSummary.associateBy { it.dataTypeName }
    // 总距离
    val totalDistance = summaryMap[HuaweiConst.HUAWEI_SUMMARY_TOTAL_DISTANCE]?.value
        ?.find { it.fieldName == "distance" }?.floatValue?.toFloat() ?: 0F
    // 总步数
    val totalStepCount = summaryMap[HuaweiConst.HUAWEI_SUMMARY_TOTAL_STEPS]?.value
        ?.find { it.fieldName == "steps" }?.integerValue ?: 0
    // 总消耗
    val totalCalorie = summaryMap[HuaweiConst.HUAWEI_SUMMARY_TOTAL_CALORIES]?.value
        ?.find { it.fieldName == "calories_total" }?.floatValue?.toFloat() ?: 0F
    // 平均心率
    val averageHeartRate = summaryMap[HuaweiConst.HUAWEI_SUMMARY_HEART_RATE_STATS]?.value
        ?.find { it.fieldName == "avg" }?.floatValue?.toFloat() ?: 0F
    // 平均步频
    val stepFrequency = summaryMap[HuaweiConst.HUAWEI_SUMMARY_STEPS_RATE_STATS]?.value
        ?.find { it.fieldName == "avg" }?.floatValue?.toInt() ?: 0

    val startTime = LocalDate.ofInstant(Instant.ofEpochMilli(this.startTime), ZoneId.systemDefault())
    val run = UserRun()
    run.userId = userId
    run.year = startTime.year
    run.month = startTime.monthValue
    run.day = startTime.dayOfMonth
    run.runId = this.startTime
    run.duration = this.activeTime / 1000
    run.distance = totalDistance
    run.stepCount = totalStepCount
    run.calorie = totalCalorie
    run.averageHeartRate = averageHeartRate.toInt()
    run.averagePace = this.activeTime.div(totalDistance)
    run.stepStride = if (totalStepCount == 0) 0F else totalDistance * 100 / totalStepCount
    run.stepFrequency = stepFrequency
    run.startTime = this.startTime
    run.endTime = this.endTime
    run.event = this.activityType.toString()
    run.eventId = this.id
    run.abnormal = 7 // 华为手表
    run.subSource = 304 // 华为
    run.sportType = formatSportType(this.activityType)


    try {
        val totalAscent = summaryMap[HuaweiConst.HUAWEI_SUMMARY_ALTITUDE]?.value
            ?.find { it.fieldName == "ascent_total" }?.floatValue?.toFloat() ?: 0F
        val totalDescent = summaryMap[HuaweiConst.HUAWEI_SUMMARY_ALTITUDE]?.value
            ?.find { it.fieldName == "descent_total" }?.floatValue?.toFloat() ?: 0F
        run.ascent = totalAscent
        run.descent = totalDescent
    } catch (e: Exception) {
        run.ascent = 0F
        run.descent = 0F
        println(e)
    }

    return run
}

/**
 * 将华为采样点转化为轨迹明细
 */
fun HuaweiActivityRecord.toNewUserRun(userId: Long): NewUserRun {
    val summaryMap = activitySummary?.dataSummary
        ?.filter { it.dataTypeName != null }
        ?.associateBy { it.dataTypeName }
        ?: emptyMap()
    // 总距离
    val totalDistance = summaryMap[HuaweiConst.HUAWEI_SUMMARY_TOTAL_DISTANCE]?.value
        ?.find { it.fieldName == "distance" }?.floatValue?.toDouble() ?: 0.0
    // 总步数
    val totalStep = summaryMap[HuaweiConst.HUAWEI_SUMMARY_TOTAL_STEPS]?.value
        ?.find { it.fieldName == "steps" }?.integerValue ?: 0
    // 总消耗
    val totalCalorie = summaryMap[HuaweiConst.HUAWEI_SUMMARY_TOTAL_CALORIES]?.value
        ?.find { it.fieldName == "calories_total" }?.floatValue?.toDouble() ?: 0.0
    // 最快速度
//    val maxSpeed = summaryMap[HuaweiConst.HUAWEI_SUMMARY_SPEED_STATS]?.value
//        ?.find { it.fieldName == "max" }?.floatValue?.toDouble() ?: 0.0
//    val maxPace = 1000 / maxSpeed
    // 平均心率
    val averageHeartRate = summaryMap[HuaweiConst.HUAWEI_SUMMARY_HEART_RATE_STATS]?.value
        ?.find { it.fieldName == "avg" }?.floatValue?.toInt() ?: 0
    // 最大心率
    val maxHeartRate = summaryMap[HuaweiConst.HUAWEI_SUMMARY_HEART_RATE_STATS]?.value
        ?.find { it.fieldName == "max" }?.floatValue?.toInt() ?: 0
    // 平均步频
    val averageStepRate = summaryMap[HuaweiConst.HUAWEI_SUMMARY_STEPS_RATE_STATS]?.value
        ?.find { it.fieldName == "avg" }?.floatValue?.toInt() ?: 0
    // 最快步频
    val maxStepRate = summaryMap[HuaweiConst.HUAWEI_SUMMARY_STEPS_RATE_STATS]?.value
        ?.find { it.fieldName == "max" }?.floatValue?.toInt() ?: 0
    // 最低海拔
    val minAltitude = summaryMap[HuaweiConst.HUAWEI_SUMMARY_ALTITUDE]?.value
        ?.find { it.fieldName == "min" }?.floatValue?.toDouble() ?: 0.0
    // 最高海拔
    val maxAltitude = summaryMap[HuaweiConst.HUAWEI_SUMMARY_ALTITUDE]?.value
        ?.find { it.fieldName == "max" }?.floatValue?.toDouble() ?: 0.0
    // 累计上升
    val totalAscent = summaryMap[HuaweiConst.HUAWEI_SUMMARY_ALTITUDE]?.value
        ?.find { it.fieldName == "ascent_total" }?.floatValue?.toDouble() ?: 0.0
    // 累计下降
    val totalDescent = summaryMap[HuaweiConst.HUAWEI_SUMMARY_ALTITUDE]?.value
        ?.find { it.fieldName == "descent_total" }?.floatValue?.toDouble() ?: 0.0

    val run = NewUserRun()
    run.activityId = startTime
    run.userId = userId
    if (deviceInfo != null) {
        run.deviceId = when (deviceInfo.devType) {
            "Smart watch", "Third watch" -> 3
            "Smart band" -> 4
            "Treadmill" -> 5
            else -> 3
        }
        run.deviceModel = if (deviceInfo.modelNum == "unknown") {
            "华为运动健康"
        } else {
            deviceInfo.modelNum
        }
    } else {
        run.deviceId = 3
        run.deviceModel = ""
    }
    run.deviceType = 304
    run.activityType = formatSportType(activityType)
    run.startTime = startTime
    run.endTime = endTime
    run.totalDistance = totalDistance
    run.totalDuration = (activeTime / 1000).toInt()
    run.totalStep = totalStep
    run.totalCalorie = totalCalorie
    run.averagePace = activeTime.div(totalDistance)
    run.maxPace = try {
        activitySummary.paceSummary.paceMap.minOf { it.value }
    } catch (e: Exception) {
        val maxSpeed = summaryMap[HuaweiConst.HUAWEI_SUMMARY_SPEED_STATS]?.value
            ?.find { it.fieldName == "max" }?.floatValue?.toDouble() ?: 0.0
        if (maxSpeed == 0.0) {
            0.0
        } else {
            1000 / maxSpeed
        }
    }
    run.averageHeartRate = averageHeartRate
    run.maxHeartRate = maxHeartRate
    run.averageStepRate = averageStepRate
    run.maxStepRate = maxStepRate
    run.averageStride = if (totalStep == 0) 0.0 else totalDistance * 100.0 / totalStep
    run.minAltitude = minAltitude
    run.maxAltitude = maxAltitude
    run.totalAscent = totalAscent
    run.totalDescent = totalDescent
    run.weather = ""
    run.province = ""
    run.city = ""
    run.temperature = ""
    run.humidity = ""
    run.partTimeKmList = activitySummary?.paceSummary?.partTimeMap?.entries
        ?.sortedBy { it.key.toDouble() }
        ?.associateBy({ it.key }, { it.value.toInt() })
        ?: emptyMap()
    run.thirdActivityType = activityType.toString()
    run.thirdActivityId = id
    run.abnormal = 7
    run.externalSportType = run.thirdActivityType
    run.mapTrackImage = ""
    run.trackImage = ""
    return run
}