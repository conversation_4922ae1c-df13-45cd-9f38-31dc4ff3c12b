package com.gusto.upload.core.utils.huawei

import cn.hutool.jwt.JWTUtil
import com.gusto.upload.model.entity.huawei.HuaweiAccessToken
import com.gusto.upload.model.entity.huawei.HuaweiUserAuthInfo

/**
 * 华为accessToken转为用户授权信息
 */
fun HuaweiAccessToken.toUserAuthInfo(userId: Long): HuaweiUserAuthInfo {
    val jwt = JWTUtil.parseToken(this.idToken)
    val info = HuaweiUserAuthInfo()
    info.userId = userId
    info.accessToken = this.accessToken
    info.refreshToken = this.refreshToken
    info.unionId = jwt.getPayload("sub").toString()
    info.avatar = jwt.getPayload("picture")?.toString() ?: ""
    info.nickname = jwt.getPayload("nickname")?.toString() ?: ""
    return info
}