package com.gusto.upload.core.roozym

import cn.hutool.core.date.LocalDateTimeUtil
import com.gusto.framework.core.util.isNotNullOrBlank
import com.gusto.framework.core.util.time.minusDays
import com.gusto.upload.core.application.user.UserRunApplicationService
import com.gusto.upload.core.common.PushService
import com.gusto.upload.core.qiniu.QiniuService
import com.gusto.upload.core.roozym.util.toUserRun
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.util.formatDateHourMinuteSecond
import com.gusto.upload.core.utils.JsonUtils
import com.gusto.upload.model.entity.response.ManualSyncRsp
import com.gusto.upload.model.entity.roozym.RoozymAuthInfo
import com.gusto.upload.model.entity.roozym.RoozymGpsData
import com.gusto.upload.model.entity.roozym.RoozymSport
import com.gusto.upload.model.entity.roozym.RoozymSportType
import com.gusto.upload.model.entity.roozym.RoozymSubscriptionEventType
import com.gusto.upload.model.entity.roozym.req.RoozymQuerySportReq
import com.gusto.upload.model.entity.user.old.UserRun
import com.gusto.upload.model.entity.user.old.UserRunDetailAltitudeItemTemp
import com.gusto.upload.model.entity.user.old.UserRunDetailHeartRateItemTemp
import com.gusto.upload.model.entity.user.old.UserRunDetailLocationItemTemp
import com.gusto.upload.model.entity.user.old.UserRunDetailSpeedItemTemp
import com.gusto.upload.model.entity.user.old.UserRunDetailStepItemTemp
import com.gusto.upload.model.entity.user.old.UserRunDetailTemp
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant
import java.time.ZoneOffset
import java.util.concurrent.Executor
import javax.annotation.Resource
import kotlin.math.abs

/**
 * 活动服务
 *
 * <AUTHOR>
 * @since 2022/5/26
 */
@Service
class RoozymActivityService {
    private val log = LoggerFactory.getLogger(javaClass)
    private val objectMapper = JsonUtils.initObjectMapper(lowerCamelCase = true)

    @Autowired
    lateinit var runApplicationService: UserRunApplicationService

    @Autowired
    lateinit var qiniuService: QiniuService

    @Autowired
    lateinit var pushService: PushService

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    @Autowired
    lateinit var authInfoService: RoozymUserAuthInfoService

    @Autowired
    lateinit var roozymClientService: RoozymClientService

    @Autowired
    lateinit var userService: UserService

    /**
     * 根据订阅事件同步活动记录列表
     */
    fun syncUserActivityRecordListBySubscription(
        openId: String,
        startTime: String,
        endTime: String,
        sportType: Int,
        eventType: Int
    ) {
        when (eventType) {
            RoozymSubscriptionEventType.EVENT_SPORT_ADD.number, RoozymSubscriptionEventType.EVENT_SPORT_UPDATE.number -> {
                val req = RoozymQuerySportReq()
                val uncheckAuthInfo = authInfoService.getOneByOpenId(openId)
                if (uncheckAuthInfo == null) {
                    log.warn("[Roozym:getUserActivityRecordList] openId = $openId, sync error, message = authInfo not found")
                    return
                }
                req.startTime = startTime
                req.endTime = endTime
                req.openId = openId
                req.sportType = if (sportType == 0) null else sportType
                val (rsp, authInfo) = roozymClientService.querySport(req, uncheckAuthInfo)
                if (rsp.isError) {
                    log.warn("[Roozym:getUserActivityRecordList] userId = ${authInfo.userId}, sync error, message = ${rsp.content}")
                    return
                }
                // 创建跑步记录头部和明细
                // val user = userService.getOneById(userId)
                createUserRunAndDetail(authInfo, rsp.result)
            }
        }
    }

    /**
     * 根据订阅事件同步活动记录列表
     */
    fun syncUserActivityRecordListByManual(userId: Long): ManualSyncRsp? {
        val uncheckAuthInfo = authInfoService.getOneByUserId(userId)
        val req = RoozymQuerySportReq()
        req.openId = uncheckAuthInfo.openId
        val now = Instant.now()

        req.endTime = now.formatDateHourMinuteSecond()
        req.startTime = now.minusDays(7).formatDateHourMinuteSecond()
        // req.startTime = now.minusSeconds(5356800).formatDateHourMinuteSecond()
        val (rsp, authInfo) = roozymClientService.querySport(req, uncheckAuthInfo)
        val outRsp = ManualSyncRsp()
        outRsp.result = true
        outRsp.reason = "同步成功，请稍后在跑步记录页查看"
        if (rsp.isError) {
            log.error("[getUserActivityRecordList] userId = $userId, sync error, message = ${rsp.content}")
            outRsp.result = false
            outRsp.reason = "同步失败，请取消授权重新绑定"
            return outRsp
        }

        // 创建跑步记录头部和明细
        asyncExecutor.execute {
            log.debug("[getUserActivityRecordList] userId = $userId, sync async start, rspResult:${rsp.result}")
            createUserRunAndDetail(authInfo, rsp.result)
            log.debug("[getUserActivityRecordList] userId = $userId, sync async success")
        }

        return outRsp
    }


    /**
     * 创建用户跑步记录和详情
     */
    fun createUserRunAndDetail(authInfo: RoozymAuthInfo, roozymSportList: List<RoozymSport>) {
        var syncSuccessCount = 0
        val runList = emptyList<UserRun>().toMutableList()
        roozymSportList.forEach {
            val run = it.toUserRun(authInfo.userId)
            val createSuccess = runApplicationService.handlerAndCreate(run)
            if (!createSuccess) return@forEach
            syncSuccessCount++
            getPolymerizeSampleSet(it, run)
//            qiniuService.uploadString(
//                objectMapper.writeValueAsString(it),
//                "roomzym_activity_record_${run.trackFile}.gzip"
//            )
            qiniuService.uploadObject(
                it,
                "roomzym_activity_record_${run.trackFile}.gzip"
            )
            runList.add(run)
        }
        log.info("[getUserActivityRecordList] userId = ${authInfo.userId}, sync success, count $syncSuccessCount")
        if (syncSuccessCount == 0) return
        asyncExecutor.execute {
            val latestRun = runList.maxByOrNull { it.startTime }
            // 创建跑步记录头部和明细
            if (latestRun != null) {
                val user = userService.getOneById(latestRun.userId)
                pushService.notifySyncSuccessFromRoozym(latestRun, syncSuccessCount, user)
            }
        }
    }

    private fun getPolymerizeSampleSet(roozymSport: RoozymSport, run: UserRun) {
        val locationList = emptyList<UserRunDetailLocationItemTemp>().toMutableList()
        val heartRateList = emptyList<UserRunDetailHeartRateItemTemp>().toMutableList()
        val speedList = emptyList<UserRunDetailSpeedItemTemp>().toMutableList()
        val stepList = emptyList<UserRunDetailStepItemTemp>().toMutableList()
        val altitudeList = emptyList<UserRunDetailAltitudeItemTemp>().toMutableList()


        val speedSampleList =
            if (roozymSport.speedData.isNotNullOrBlank()) roozymSport.speedData.split(",") else emptyList()
        val heartSampleList =
            if (roozymSport.heartrateData.isNotNullOrBlank()) roozymSport.heartrateData.split(",") else emptyList()
        val altitudeSampleList =
            if (roozymSport.heightData.isNotNullOrBlank()) roozymSport.heightData.split(",") else emptyList()

        // 步频和步幅计算
        val stepSampleList =
            if (roozymSport.stepData.isNotNullOrBlank()) roozymSport.stepData.split(",") else emptyList()
        // 距离比速度少一个点 所有补一个
        val distanceSampleList = if (roozymSport.distanceData.isNotNullOrBlank()) roozymSport.distanceData.split(",")
            .toMutableList() else emptyList<String>().toMutableList()
        distanceSampleList.add(distanceSampleList[distanceSampleList.size - 1])

        val gpsSampleList = if (roozymSport.gpsData.isNotNullOrBlank()) roozymSport.gpsData.split(",") else emptyList()
        val reduceGpsList = groupArray(gpsSampleList)

        // val minutes = stepSampleList.size
        val second = roozymSport.actTime.toInt()

        val startTime = LocalDateTimeUtil.parse(roozymSport.startDate, "yyyy-MM-dd HH:mm:ss")
        val runStartTime = startTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()
        for (i in 0 until second) {
            // 数据
            val detailLocationItemTemp = UserRunDetailLocationItemTemp()
            // 计算当前时间
            detailLocationItemTemp.isTooFast = false
            detailLocationItemTemp.runindex = 1
            detailLocationItemTemp.time = runStartTime + (i + 1) * 1000
            if (
                (roozymSport.sportType == RoozymSportType.CROSS_COUNTRY_RACE.number
                        || roozymSport.sportType == RoozymSportType.RUNNING.number
                        || roozymSport.sportType == RoozymSportType.RUNNING_IN_ROOM.number
                        || roozymSport.sportType == RoozymSportType.MARATHON.number
                        || roozymSport.sportType == RoozymSportType.AI_TRAINING.number) && abs(roozymSport.actTime - heartSampleList.size) < 3
            ) {
                detailLocationItemTemp.heartRate = heartSampleList[i].toInt()
            } else {
                detailLocationItemTemp.heartRate =
                    if (i / 60 >= heartSampleList.size) heartSampleList[heartSampleList.size - 1].toDouble()
                        .toInt() else heartSampleList[i / 60].toDouble().toInt()
            }
            // 当前点数据
            val curLatitude: BigDecimal
            val curLongitude: BigDecimal

            if (reduceGpsList.isEmpty()) {
                curLatitude = BigDecimal.ZERO
                curLongitude = BigDecimal.ZERO
            } else {
                curLatitude =
                    if (i < reduceGpsList.size) reduceGpsList[i].latitude else reduceGpsList[reduceGpsList.size - 1].latitude
                curLongitude =
                    if (i < reduceGpsList.size) reduceGpsList[i].longitude else reduceGpsList[reduceGpsList.size - 1].longitude
            }
            val curAltitude = if (altitudeSampleList.isEmpty()) {
                0.0
            } else {
                if (i / 120 < altitudeSampleList.size)
                    BigDecimal.valueOf(altitudeSampleList[i / 120].toDouble()).setScale(2, RoundingMode.DOWN).toDouble()
                else
                    BigDecimal.valueOf(altitudeSampleList[altitudeSampleList.size - 1].toDouble())
                        .setScale(2, RoundingMode.DOWN).toDouble()
            }
            val curHeartRate = detailLocationItemTemp.heartRate


            val curStep = if (stepSampleList.size != 1 && stepSampleList.isNotEmpty()) {
                if (i / 60 < stepSampleList.size)
                    stepSampleList[i / 60].toDouble()
                else
                    stepSampleList[stepSampleList.size - 1].toDouble()
            } else {
                0.0
            }

            val curDistance = if (distanceSampleList.isNotEmpty()) {
                if (i / 60 < distanceSampleList.size)
                    distanceSampleList[i / 60].toDouble()
                else
                    distanceSampleList[distanceSampleList.size - 1].toDouble()
            } else 0.0

            val curSpeed = getSpeed(roozymSport, speedSampleList, i)

            if (curLatitude.compareTo(BigDecimal.ZERO) != 0 && curLatitude.compareTo(BigDecimal.ZERO) != 0) {
                detailLocationItemTemp.latitude = curLatitude.toDouble()
                detailLocationItemTemp.longitude = curLongitude.toDouble()
            }

            if (curAltitude != 0.0) {
                detailLocationItemTemp.altitude = curAltitude
            }

            if (curSpeed > 0) {
                detailLocationItemTemp.speed = BigDecimal(1000 / curSpeed).setScale(2, RoundingMode.DOWN).toDouble()
            } else {
                detailLocationItemTemp.speed = 0.0
            }

            locationList.add(detailLocationItemTemp)

            // 心跳
            val heartRateItemTemp = UserRunDetailHeartRateItemTemp()
            heartRateItemTemp.heartRate = curHeartRate
            heartRateList.add(heartRateItemTemp)

            // speedList
            val speedItemTemp = UserRunDetailSpeedItemTemp()

            if (curSpeed > 0) {
                speedItemTemp.curSpeed = BigDecimal(1000 / curSpeed).setScale(2, RoundingMode.DOWN).toDouble()
            } else {
                speedItemTemp.curSpeed = 0.0
            }

            speedList.add(speedItemTemp)

            // altitudeList
            val altitudeItemTemp = UserRunDetailAltitudeItemTemp()
            altitudeItemTemp.altitude = curAltitude
            altitudeList.add(altitudeItemTemp)

            // UserRunDetailStepItemTemp
            val stepItemTemp = UserRunDetailStepItemTemp()

            if (curSpeed > 0) {
                stepItemTemp.curSpeed = BigDecimal(1000 / curSpeed).setScale(2, RoundingMode.DOWN).toDouble()
            } else {
                stepItemTemp.curSpeed = 0.0
            }
            // stepItemTemp.curSpeed = BigDecimal(1000/curSpeed).setScale(2, RoundingMode.DOWN).toDouble()

            stepItemTemp.stepfrequency = curStep
            if (stepItemTemp.stepfrequency > 0) {
                stepItemTemp.stepstride =
                    BigDecimal(curDistance / stepItemTemp.stepfrequency).setScale(2, RoundingMode.DOWN).toDouble()
            } else {
                stepItemTemp.stepstride = 0.0
            }
            stepList.add(stepItemTemp)
        }

        stepList.onEachIndexed { index, item ->
            val speed = speedList.getOrNull(index)?.curSpeed ?: 0.toDouble()
            item.curSpeed = BigDecimal(speed * 3.6).setScale(2, RoundingMode.DOWN).toDouble()
        }

        val detail = UserRunDetailTemp()
        detail.location = locationList
        detail.step = stepList

        if (roozymSport.lapsData.isNotNullOrBlank()) {
            val lapData = roozymSport.lapsData.split(",").map { it.toInt() }
            detail.lap = if (lapData.size == 1 && lapData[0] == 0) emptyList() else roozymSport.lapsData.split(",")
                .map { it.toInt() }
        }

//        qiniuService.uploadString(objectMapper.writeValueAsString(detail), "${run.trackFile}.gzip")
        qiniuService.uploadObject(detail, "${run.trackFile}.gzip")
        // qiniuService.refresh("https://file.gusto.cn/${run.trackFile}.gzip")
    }

    private fun groupArray(gpsData: List<String>): List<RoozymGpsData> {
        if (gpsData.size % 2 != 0) return emptyList()
        val ans = emptyList<RoozymGpsData>().toMutableList()
        for (i in gpsData.indices step 2) {
            val cur = RoozymGpsData()
            val longitude = BigDecimal(gpsData[i]).setScale(8)
            val latitude = BigDecimal(gpsData[i + 1]).setScale(8)
            cur.latitude = latitude
            cur.longitude = longitude
            ans.add(cur)
        }
        return ans
    }

    /**
     * 获取index位置的速度
     */
    fun getSpeed(run: RoozymSport, speedSampleList: List<String>, index: Int): Double {
        if (speedSampleList.isEmpty()) return 0.0
        when (run.sportType) {
            RoozymSportType.CYCLING.number -> {
                return if (index / 60 < speedSampleList.size) {
                    if (BigDecimal.ZERO.compareTo(BigDecimal(speedSampleList[index / 60])) != 0) {
                        BigDecimal(1000).divide(
                            BigDecimal(speedSampleList[index / 60]).divide(
                                BigDecimal(10),
                                8,
                                RoundingMode.DOWN
                            ).divide(BigDecimal(3.6), 8, RoundingMode.DOWN), 8, RoundingMode.DOWN
                        ).setScale(2, RoundingMode.DOWN).toDouble()
                    } else 0.0
                } else {
                    return if (BigDecimal.ZERO.compareTo(BigDecimal(speedSampleList[speedSampleList.size - 1])) != 0) {
                        BigDecimal(1000).divide(
                            BigDecimal(speedSampleList[speedSampleList.size - 1]).divide(
                                BigDecimal(10),
                                8,
                                RoundingMode.DOWN
                            ).divide(BigDecimal(3.6), 8, RoundingMode.DOWN), 8, RoundingMode.DOWN
                        ).setScale(2, RoundingMode.DOWN).toDouble()
                    } else 0.0
                }
            }

            RoozymSportType.MOUNTAIN_CLIMBING.number -> {
                // run.averagePace = (1000.0 / (this.avgSpeed.toDouble() / 3600)).toFloat()
                return if (index / 60 < speedSampleList.size) {
                    if (BigDecimal.ZERO.compareTo(BigDecimal(speedSampleList[index / 60])) != 0) {
                        BigDecimal(1000).divide(
                            BigDecimal(speedSampleList[index / 60]).divide(BigDecimal(3600), 8, RoundingMode.DOWN),
                            8,
                            RoundingMode.DOWN
                        ).setScale(2, RoundingMode.DOWN).toDouble()
                    } else 0.0
                } else {
                    return if (BigDecimal.ZERO.compareTo(BigDecimal(speedSampleList[speedSampleList.size - 1])) != 0) {
                        BigDecimal(1000).divide(
                            BigDecimal(speedSampleList[speedSampleList.size - 1]).divide(
                                BigDecimal(3600),
                                8,
                                RoundingMode.DOWN
                            ), 8, RoundingMode.DOWN
                        ).setScale(2, RoundingMode.DOWN).toDouble()
                    } else 0.0
                }
            }

            RoozymSportType.SWIMMING_POOL.number, RoozymSportType.SWIMMING_OPEN_WATER.number -> {
                // run.averagePace = this.avgSpeed.toFloat() * 10.0f
                return if (index / 60 < speedSampleList.size)
                    BigDecimal(speedSampleList[index / 60]).multiply(BigDecimal(10)).setScale(2, RoundingMode.DOWN)
                        .toDouble()
                else
                    BigDecimal(speedSampleList[speedSampleList.size - 1]).multiply(BigDecimal(10))
                        .setScale(2, RoundingMode.DOWN).toDouble()
            }

            else -> {
                return if (index / 60 < speedSampleList.size)
                    BigDecimal(speedSampleList[index / 60]).setScale(2, RoundingMode.DOWN).toDouble()
                else
                    BigDecimal(speedSampleList[speedSampleList.size - 1]).setScale(2, RoundingMode.DOWN).toDouble()
            }
        }
    }
}
