package com.gusto.upload.core.application.user

import cn.hutool.core.date.DateTime
import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.StrUtil
import com.alicp.jetcache.Cache
import com.alicp.jetcache.anno.CreateCache
import com.gusto.framework.core.exception.FrameworkErrorInfo
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwError
import com.gusto.framework.core.util.isNotNullOrBlank
import com.gusto.upload.core.common.AmapClient
import com.gusto.upload.core.common.MatchClient
import com.gusto.upload.core.common.MatchConfigService
import com.gusto.upload.core.service.app.AppRunErrorLogService
import com.gusto.upload.core.service.user.NewUserRunService
import com.gusto.upload.core.service.user.NewUserRunUploadNotifyService
import com.gusto.upload.core.service.user.UserRunAdcodeService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.util.NewUserRunMapUtils
import com.gusto.upload.core.util.formatDateHourMinuteSecond
import com.gusto.upload.core.utils.formatDuration
import com.gusto.upload.core.utils.formatPaceString
import com.gusto.upload.core.utils.run.fillBestResult
import com.gusto.upload.core.utils.run.roundTwo
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.app.AppRunErrorLog
import com.gusto.upload.model.entity.user.NewUserRun
import com.gusto.upload.model.entity.user.old.UserRunAdcode
import com.gusto.upload.model.entity.user.req.AppUploadNewUserRunReq
import com.gusto.upload.model.entity.user.rsp.GetNewUserRunListStatRsp
import me.chanjar.weixin.cp.api.WxCpService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.util.concurrent.Executor
import javax.annotation.Resource
import javax.servlet.http.HttpServletRequest

/**
 * 新用户跑步记录 应用服务类
 *
 * <AUTHOR>
 * @since 2022-07-20
 */
@Service
class NewUserRunApplicationService {

    private val log = LoggerFactory.getLogger(javaClass)

    // 【极速版】其他运动提醒 - 其他运动提醒
    private val webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cd53c79a-c35c-4d99-9f36-58f37310318b"

    @Autowired
    lateinit var runService: NewUserRunService

    @Autowired
    lateinit var matchClient: MatchClient

    @Autowired
    lateinit var mapUtils: NewUserRunMapUtils

    @Autowired
    lateinit var wxCpService: WxCpService

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var adcodeService: UserRunAdcodeService

    @Autowired
    lateinit var logService: AppRunErrorLogService

    @CreateCache(name = "NewUserRunApplicationService.getMonthStat.", expire = 3600 * 24 * 7)
    lateinit var runMonthStatCache: Cache<Long, GetNewUserRunListStatRsp>

    @CreateCache(name = "NewUserRunApplicationService.handlerAndCreateCache.", expire = 3600 * 3)
    private lateinit var handlerAndCreateCache: Cache<String, Long>

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    @Autowired
    lateinit var uploadNotifyService: NewUserRunUploadNotifyService

    @Autowired
    lateinit var configService: MatchConfigService

    @Autowired
    lateinit var amapClient: AmapClient

    /**
     * 创建日志前缀
     */
    private fun buildLogPre(run: NewUserRun): String {
        return "[handlerAndCreate] " +
                "userId=${run.userId}, " +
                "deviceId=${run.deviceId}, " +
                "deviceType=${run.deviceType}, " +
                "deviceModel=${run.deviceModel}, " +
                "activityId=${run.activityId}"
    }

    /**
     * 处理跑步记录并插入
     *
     * @return 是否插入成功
     */
    fun handlerAndCreate(run: NewUserRun, fromApp: Boolean = false): Boolean {
        val cacheKey = "${run.userId}-${run.activityId}"
        val cache = handlerAndCreateCache.GET(cacheKey)
        if (cache.isSuccess) {
            if (fromApp) {
                throwError(ServiceException(UploadErrorCode.OPERATION_DUPLICATE))
            }
            return false
        } else {
            handlerAndCreateCache.PUT(cacheKey, 0)
        }

        val startTime = LocalDate.ofInstant(Instant.ofEpochMilli(run.startTime), ZoneId.systemDefault())
        val now = Instant.now()
        run.year = startTime.year
        run.month = startTime.monthValue
        run.day = startTime.dayOfMonth
        run.week = DateUtil.weekOfYear(DateTime.of(run.startTime))
        run.activityId = run.startTime
        run.version = 1
        run.trackFile = "${run.userId}_${run.startTime}_${run.version}"
        run.uploadTime = now
        run.updateTime = now
        run.adminUpdate = false
        run.adminUpdateTime = null
        // 默认为其他运动，下面会有合法判断将其改为跑步
        run.runRecord = 2
        run.unusual = 0
        run.unusualMessage = ""
        run.duplicate = false
        run.duplicateGroupId = 0
        run.state = 1
        run.type = 0
        run.roundTwo()

        val logPre = buildLogPre(run)
        if (run.userId == 0L || run.activityId == 0L) {
            log.debug("$logPre, one of (userId, activityId) is zero or null, return false")
            handlerAndCreateCache.REMOVE(cacheKey)
            return false
        }
        if (run.deviceId > 2) {
            // VIVO第三方活动记录ID因数据库字段长度不足（DDL时间太慢），暂存fitUrl字段中
            if (run.deviceType == 309 && run.fitUrl.isNullOrEmpty()) {
                log.debug("$logPre, fitUrl(thirdActivityId) is zero or null, return false")
                handlerAndCreateCache.REMOVE(cacheKey)
                return false
            }
            if (run.deviceType != 309 && run.thirdActivityId.isNullOrEmpty()) {
                log.debug("$logPre, thirdActivityId is zero or null, return false")
                handlerAndCreateCache.REMOVE(cacheKey)
                return false
            }
        }

        // 2020 年之前的记录直接忽略
        if (run.startTime < 1577808000) {
            val dateStr = DateTime.of(run.startTime).toDateStr()
            log.debug("$logPre, startTime=$dateStr, not in [2020..2100], return false")
            handlerAndCreateCache.REMOVE(cacheKey)
            return false
        }

        // 小于 100 米直接忽略
        if (run.totalDistance < 100F) {
            log.debug("$logPre, distance=${run.totalDistance}, less then 100, return false")
            handlerAndCreateCache.REMOVE(cacheKey)
            return false
        }

        // [重要] 判断跑步记录是否合法，默认为其他运动，满足条件的才设为跑步
        // 补偿措施
        try {
            if (run.averageStepRate == 0 && run.totalDuration != 0 && run.totalStep != 0) {
                run.averageStepRate = run.totalStep / (run.totalDuration / 60)
            }
        } catch (e: Exception) {
            log.error(e.message)
        }
        if (run.activityType >= 5) {
            // 除了跑步、徒步、越野，其他设为其他运动
            run.runRecord = 2
            run.unusual = 1
            run.unusualMessage += "非跑步/徒步/越野运动"
        } else {
            // 如果是跑步、徒步、越野
            if (run.deviceId == 2) {
                // 手动录入有效
                run.runRecord = 1
            } else {
                // 非手动录入需判断配速和步频
                if (run.averagePace in 130.0..600.0 && run.averageStepRate in 70..300 && run.averageStride in 30.0..200.0) {
                    run.runRecord = 1
                } else if (run.averagePace in 600.0..3600.0 && run.averageStepRate in 1..300 && run.averageStride in 30.0..200.0) {
                    run.runRecord = 1
                }

                // APP跑步，判断是否利用APP补偿机制作弊
                if (run.deviceId == 1) {
                    // 总公里数
                    val totalKm = (run.totalDistance / 1000).toInt()
                    // 正常公里数
                    val partTimeKmListSize = run.partTimeKmList
                        .filter { it.key.toDouble() % 1 == 0.0 }
                        .size
                    if (totalKm >= 3 && partTimeKmListSize < totalKm) {
                        // 补偿距离大于50%
                        val cheatDistanceLimit = configService.getCheatDistanceLimit()
                        if (totalKm >= cheatDistanceLimit && partTimeKmListSize / totalKm.toDouble() < 0.5) {
                            run.runRecord = 2
                            run.unusual = 2
                            run.unusualMessage += "客户端补偿距离超过50%且总公里数大于等于${cheatDistanceLimit}公里"
                            log.debug("$logPre, totalKm=$totalKm, cheatDistanceLimit=${cheatDistanceLimit}KM, partTimeKmListSize=$partTimeKmListSize, partTimeKmListSize/totalKm=${partTimeKmListSize / totalKm.toDouble()}<0.5, return false")
                        }
                    }
                }
            }
        }

        // [重要] 判断当前记录是否已入库
        val dbRun = runService.getListByUserIdAndRunId(run.userId, run.activityId)
            .firstOrNull { it.deviceType == run.deviceType }
        if (dbRun != null) {
            log.debug("$logPre, has been inserted into the database, return false")
            handlerAndCreateCache.REMOVE(cacheKey)
            return false
        }

        // 关联tapd 【【重复记录】处理错误问题】https://www.tapd.cn/32401252/bugtrace/bugs/view?bug_id=1132401252001001231
        // [重要] 判断是否重复
        // 查出时间有重合的记录列表
        val duplicateList = runService.getListByUserIdAndTimeForDuplicate(run.userId, run.startTime, run.endTime)
        if (duplicateList.isNotEmpty()) {
            val duplicateRun = duplicateList.minByOrNull { it.startTime }!!

            // 有重复记录
            run.duplicate = true
            run.duplicateGroupId = if (duplicateRun.duplicateGroupId > 0) {
                duplicateRun.duplicateGroupId
            } else {
                duplicateRun.recordId
            }
            run.unusualMessage = if (run.unusualMessage.isNotNullOrBlank()) {
                "${run.unusualMessage} 重复记录"
            } else {
                "重复记录"
            }
            duplicateRun.duplicateGroupId = run.duplicateGroupId
            runService.updateById(duplicateRun)
        }

        // 记录正常，可以入库
        // 填充最佳距离用时
        run.fillBestResult()

        // 填充天气和省市区
        fillWeatherAndLocation(run)

        var result: Boolean
        try {
            result = runService.save(run)
            log.debug("$logPre, create success")
        } catch (e: Exception) {
            result = false
            log.error("$logPre, catch error, $e")
            throwError(ServiceException(FrameworkErrorInfo.SYSTEM_INTERNAL_ERROR.errorCode, "系统内部错误"))
        }

        // 清除缓存
        runMonthStatCache.REMOVE(run.userId)

        // 异步通知 match-server 处理跑步记录上传通知
        asyncExecutor.execute {
            // PS：有可能出现502
            val rsp = matchClient.userRunUploadNotify(run.userId, run.recordId)
            if (rsp.isError || rsp.status_4xx() || rsp.status_5xx()) {
                val uploadNotify = run.buildNewUserRunUploadNotify(1)
                uploadNotifyService.save(uploadNotify)
            }
            // 其他运动通知 通知范围：极速版且其他运动且运动类型非跑步
            if (run.runRecord == 2 && run.appVersion != null && run.appVersion.startsWith("1.") && run.activityType <= 4) {
                notifyError(run)
            }
        }

        handlerAndCreateCache.REMOVE(cacheKey)
        return result
    }

    /**
     * 填充天气和省市区
     */
    fun fillWeatherAndLocation(run: NewUserRun) {
        if (StrUtil.isEmpty(run.location)) return
        val amapKey = configService.getAmapApiKey()
        val adcodeRsp = amapClient.getReGeo(amapKey, run.location)
        if (adcodeRsp.isSuccess) {
            val addressComponent = adcodeRsp.result.regeocode.addressComponent
            run.province = addressComponent.province
            run.city = addressComponent.city
            val adcode = addressComponent.adcode
            val weatherRsp = amapClient.getWeatherInfo(amapKey, adcode)
            if (weatherRsp.isSuccess) {
                val live = weatherRsp.result.lives.firstOrNull()
                if (live != null) {
                    run.weather = live.weather
                    run.temperature = live.temperature
                    run.humidity = live.humidity
                }
            } else {
                log.error("[fillWeatherAndLocation] get weather info error, error = ${weatherRsp.content}")
            }
        } else {
            log.error("[fillWeatherAndLocation] get adcode error, error = ${adcodeRsp.content}")
        }
    }

    // 其他运动通知
    private fun notifyError(run: NewUserRun) {
        val user = userService.getById(run.userId)
        val errorLog = AppRunErrorLog()
        errorLog.startTime = Instant.ofEpochMilli(run.startTime)
        errorLog.activityId = run.activityId
        errorLog.userId = run.userId
        errorLog.mobile = user?.mobile ?: ""
        errorLog.totalDistance = run.totalDistance
        errorLog.totalDuration = run.totalDuration
        errorLog.averagePace = run.averagePace
        errorLog.totalStep = run.totalStep
        errorLog.averageStepRate = run.averageStepRate
        errorLog.averageStride = run.averageStride
        errorLog.appSource = when (run.appSource) {
            1 -> "android"
            2 -> "iOS"
            3 -> "HarmonyOS"
            else -> ""
        }
        errorLog.appBrand = run.appBrand
        errorLog.appVersion = run.appVersion
        val robotMessage = """
            【有新的数据严重异常的跑步记录】
            日期：${errorLog.startTime.formatDateHourMinuteSecond()}
            记录：${errorLog.activityId}
            用户：${user?.nickname ?: ""} | ${user?.mobile ?: ""} | ${user?.userId ?: ""}
            距离：${errorLog.totalDistance}米
            用时：${errorLog.totalDuration.formatDuration()}
            配速：${errorLog.averagePace.formatPaceString()}
            步数：${errorLog.totalStep}
            步频：${errorLog.averageStepRate}
            步幅：${errorLog.averageStride}
            设备：${errorLog.appSource} | ${errorLog.appBrand} | ${errorLog.appVersion} | ${run.deviceModel}
        """.trimIndent()
        wxCpService.groupRobotService.sendText(
            webhook,
            robotMessage,
            emptyList(),
            emptyList()
        )
        logService.save(errorLog)
    }

    /**
     * APP 上传跑步记录
     */
    fun uploadByApp(req: AppUploadNewUserRunReq, httpRequest: HttpServletRequest): Long {
        val userId = httpRequest.getAttribute("userId") as Long
        val run = mapUtils.toNewUserRun(req)
        run.clientDeviceId = httpRequest.getHeader("client-deviceID") ?: ""
        run.appVersion = httpRequest.getHeader("client-version") ?: ""
        run.appSource = when (httpRequest.getHeader("client-source")) {
            null -> 0
            "android" -> 1
            "iOS" -> 2
            "HarmonyOS" -> 3
            else -> 0
        }
        run.appBrand = httpRequest.getHeader("client-brand") ?: ""
        run.appOs = httpRequest.getHeader("client-os") ?: ""
        run.userId = userId
        run.activityId = run.activityId
        run.abnormal = if (run.activityType == 2) {
            2
        } else {
            0
        }
        run.externalSportType = req.thirdActivityType

        val result = handlerAndCreate(run, true)
        if (result && req.adCodeList != null && req.adCodeList.isNotEmpty()) {
            asyncExecutor.execute {
                val adcodeList = req.adCodeList.map {
                    val adcode = UserRunAdcode()
                    adcode.uid = run.userId
                    adcode.runId = run.activityId
                    adcode.adcode = it.adcode
                    adcode.createTime = Instant.now()
                    adcode.latitude = it.latitude
                    adcode.longitude = it.longitude
                    adcode
                }
                adcodeService.saveBatch(adcodeList)
            }
        }

        return run.recordId ?: 0
    }

}
