package com.gusto.upload.core.ezon

import cn.hutool.core.util.StrUtil
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwError
import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.core.qiniu.QiniuService
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.ezon.EzonProperties
import com.gusto.upload.model.entity.ezon.EzonUserAuthInfo
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.concurrent.Executor
import javax.annotation.Resource

/**
 * 宜准-通用
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Service
class EzonCommonService {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authInfoService: EzonUserAuthInfoService

    @Autowired
    lateinit var config: EzonProperties

    @Autowired
    lateinit var ezonClient: EzonClient

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    @Autowired
    lateinit var qiniuService: QiniuService

    /**
     * 检查用户AT是否过期，如果过期则刷新
     */
    fun checkOrRefreshAccessToken(authInfo: EzonUserAuthInfo) {
        val now = Instant.now()
        // AT有效期默认1小时
        val expireTime = authInfo.accessTokenUpdateTime.plusSeconds(3500)
        if (now.isAfter(expireTime)) {
            val result = refreshAccessToken(authInfo)
            if (!result) {
                authInfoService.removeById(authInfo)
                throwError(
                    ServiceException(
                        UploadErrorCode.EZON_REFRESH_ACCESS_TOKEN_ERROR.code,
                        "获取授权失败，请返回并重新绑定"
                    )
                )
            }
        }
        if (StrUtil.isEmpty(authInfo.nickname)) {
            val rsp = ezonClient.getUserInfo(authInfo.accessToken)
            if (rsp.isSuccess) {
                authInfo.nickname = rsp.result.data.nickName
                authInfoService.updateById(authInfo)
            }
        }
    }

    /**
     * 刷新AT
     */
    private fun refreshAccessToken(authInfo: EzonUserAuthInfo): Boolean {
        val rsp = ezonClient.getAccessToken(
            clientId = config.clientId,
            clientSecret = config.clientSecret,
            code = authInfo.refreshToken,
            redirectUri = config.redirectUri
        )
        if (rsp.isError) {
            log.warn("[refreshAccessToken] get AT error, error = ${rsp.content}")
            return false
        }
        if (rsp.result.resultId == 0) {
            authInfo.accessToken = rsp.result.data.accessToken
            authInfo.accessTokenUpdateTime = Instant.now()
            authInfoService.updateById(authInfo)
            return true
        }
        log.warn("[refreshAccessToken] refresh AT error, rsp = ${rsp.result.formatToJson()}")
        return false
    }

}
