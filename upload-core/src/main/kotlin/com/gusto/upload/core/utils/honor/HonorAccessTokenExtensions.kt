package com.gusto.upload.core.utils.honor

import cn.hutool.jwt.JWTUtil
import com.gusto.upload.model.entity.honor.HonorAccessToken
import com.gusto.upload.model.entity.honor.HonorUserAuthInfo

/**
 * 荣耀accessToken转为用户授权信息
 */
fun HonorAccessToken.toUserAuthInfo(userId: Long, openId: String): HonorUserAuthInfo {
    val jwt = JWTUtil.parseToken(this.idToken)
    val info = HonorUserAuthInfo()
    info.userId = userId
    info.accessToken = this.accessToken
    info.refreshToken = this.refreshToken
    info.openId = openId
    info.unionId = jwt.getPayload("sub").toString()
    info.avatar = jwt.getPayload("picture")?.toString() ?: ""
    info.nickname = jwt.getPayload("display_name")?.toString() ?: ""
    return info
}