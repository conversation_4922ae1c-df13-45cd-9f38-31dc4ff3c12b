package com.gusto.upload.core.kafka.comsumer

import com.gusto.upload.core.honor.HonorActivityService
import com.gusto.upload.model.entity.message.HonorActivityMessage
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component

/**
 * 华为-活动消费者
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Component
class HonorActivityConsumer {
    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var activityService: HonorActivityService

    /**
     * 处理订阅事件
     */
    @KafkaListener(
        topics = [HonorActivityMessage.SUBSCRIPTION_TOPIC],
        groupId = HonorActivityMessage.SUBSCRIPTION_TOPIC + "#UpsertUserRun",
        concurrency = "2"
    )
    fun upsertUserRun(message: HonorActivityMessage) {
        log.info("[upsertUserRun] get message = {}", message)
        activityService.syncActivityListBySubscription(
            message.userId,
            message.startTime,
            message.dataType
        )
        log.info("[upsertUserRun] handle success")
    }

}
