package com.gusto.upload.core.huami

import cn.hutool.core.util.StrUtil
import com.baomidou.mybatisplus.core.toolkit.Wrappers
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwError
import com.gusto.upload.core.dao.huami.HuamiUserAuthInfoDao
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.huami.HuamiUserAuthInfo
import org.springframework.stereotype.Service

/**
 * 华米-用户授权信息
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
class HuamiUserAuthInfoService : ServiceImpl<HuamiUserAuthInfoDao, HuamiUserAuthInfo>() {

    /**
     * 根据华米用户ID获取列表
     */
    fun getListByOpenId(openId: String): List<HuamiUserAuthInfo> {
        if (StrUtil.isEmpty(openId)) {
            return emptyList()
        }
        val wrapper = Wrappers.lambdaQuery<HuamiUserAuthInfo>()
            .eq(HuamiUserAuthInfo::getOpenId, openId)
        return list(wrapper)
    }

    /**
     * 根据用户ID获取列表
     */
    fun getListByUserId(userId: Long): List<HuamiUserAuthInfo> {
        val wrapper = Wrappers.lambdaQuery<HuamiUserAuthInfo>()
            .eq(HuamiUserAuthInfo::getUserId, userId)
        return list(wrapper)
    }

    /**
     * 根据华米用户ID获取
     */
    fun getOneByOpenIdOrNull(openId: String): HuamiUserAuthInfo? {
        val wrapper = Wrappers.lambdaQuery<HuamiUserAuthInfo>()
            .eq(HuamiUserAuthInfo::getOpenId, openId)
        val authInfo = getOne(wrapper)
        if (authInfo == null) {
            log.warn("[getOneByOpenId] openId = $openId not found")
        }
        return authInfo
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserId(userId: Long): HuamiUserAuthInfo {
        val wrapper = Wrappers.lambdaQuery<HuamiUserAuthInfo>()
            .eq(HuamiUserAuthInfo::getUserId, userId)
        val authInfo = getOne(wrapper)
        if (authInfo == null) {
            log.error("[getOneByUserId] userId = $userId not found")
            throwError(ServiceException(UploadErrorCode.HUAMI_USER_AUTH_INFO_NOT_FOUND))
        }
        return authInfo!!
    }

    /**
     * 根据用户ID获取
     */
    fun getOneByUserIdOrNull(userId: Long): HuamiUserAuthInfo? {
        val wrapper = Wrappers.lambdaQuery<HuamiUserAuthInfo>()
            .eq(HuamiUserAuthInfo::getUserId, userId)
        return getOne(wrapper)
    }

}
