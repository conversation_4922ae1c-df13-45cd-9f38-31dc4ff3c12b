package com.gusto.upload.core.huawei

import com.dtflys.forest.exceptions.ForestRuntimeException
import com.dtflys.forest.http.ForestRequest
import com.dtflys.forest.http.ForestResponse
import com.dtflys.forest.interceptor.Interceptor
import org.slf4j.LoggerFactory
import java.time.Instant

/**
 * 华为-API拦截器
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
class HuaweiClientInterceptor<T> : Interceptor<T> {

    private val log = LoggerFactory.getLogger(javaClass)

    /**
     * 该方法在请求发送失败时被调用，解决
     */
    override fun onError(ex: ForestRuntimeException?, req: ForestRequest<*>?, rsp: ForestResponse<*>) {
        if (req == null) {
            return
        }
        // https://developer.huawei.com/consumer/cn/doc/HMSCore-Guides/faq-0000001476980529#section15806727142414
        // 请求涉及跨地域站点访问时(如注册地在欧洲的用户在中国区访问)
        if (!rsp.content.contains("\"code\":121001")) {
            return
        }
        log.debug("[onError] req cross, content = ${rsp.content}")
        val reqTime = req.getHeaderValue("x-caller-trace-id")
            .split("-")
            .last()
            .toLong()
        val nowTime = Instant.now().toEpochMilli()
        if (nowTime - reqTime > 30000) {
            log.error("[onError] req cross, timeout")
            return
        }
        val location = rsp.getHeaderValue("Location")
        if (location.isNullOrEmpty()) {
            log.error("[onError] req cross, location is empty")
            return
        }
        req.url = location
        val crossRsp = req.execute() as ForestResponse<*>
        if (crossRsp.isSuccess) {
            rsp.result = crossRsp.result
            // 暂时没办法将crossRsp覆盖到rsp中，只能通过这样让后续可以判断location请求是否成功
            rsp.statusCode = 200
        } else {
            log.error("[onError] req cross error, content = ${crossRsp.content}")
        }
    }

}
