package com.gusto.upload.core.application

import cn.hutool.core.util.StrUtil
import com.alicp.jetcache.Cache
import com.alicp.jetcache.anno.CreateCache
import com.gusto.upload.core.common.MatchConfigService
import com.gusto.upload.core.coros.CorosActivityService
import com.gusto.upload.core.coros.CorosAuthService
import com.gusto.upload.core.coros.CorosUserAuthInfoService
import com.gusto.upload.core.ezon.EzonActivityService
import com.gusto.upload.core.ezon.EzonAuthService
import com.gusto.upload.core.ezon.EzonUserAuthInfoService
import com.gusto.upload.core.garmin.GarminActivityService
import com.gusto.upload.core.garmin.GarminAuthService
import com.gusto.upload.core.garmin.GarminUserAuthInfoService
import com.gusto.upload.core.honor.HonorActivityService
import com.gusto.upload.core.honor.HonorAuthService
import com.gusto.upload.core.honor.HonorUserAuthInfoService
import com.gusto.upload.core.huami.HuamiActivityService
import com.gusto.upload.core.huami.HuamiAuthService
import com.gusto.upload.core.huami.HuamiUserAuthInfoService
import com.gusto.upload.core.huawei.HuaweiActivityService
import com.gusto.upload.core.huawei.HuaweiAuthService
import com.gusto.upload.core.huawei.HuaweiUserAuthInfoService
import com.gusto.upload.core.oppo.OppoActivityService
import com.gusto.upload.core.oppo.OppoAuthService
import com.gusto.upload.core.oppo.OppoUserAuthInfoService
import com.gusto.upload.core.roozym.RoozymActivityService
import com.gusto.upload.core.roozym.RoozymAuthService
import com.gusto.upload.core.roozym.RoozymUserAuthInfoService
import com.gusto.upload.core.service.DeviceService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.util.DeviceMapUtils
import com.gusto.upload.core.vivo.VivoActivityService
import com.gusto.upload.core.vivo.VivoAuthService
import com.gusto.upload.core.vivo.VivoUserAuthInfoService
import com.gusto.upload.core.wanbao.WanbaoActivityService
import com.gusto.upload.core.wanbao.WanbaoAuthService
import com.gusto.upload.core.wanbao.WanbaoUserAuthInfoService
import com.gusto.upload.model.entity.dto.UserDeviceListElementDTO
import com.gusto.upload.model.entity.response.GetUserAuthInfoRsp
import com.gusto.upload.model.entity.response.ManualSyncRsp
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant

/**
 * 设备 服务类
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@Service
class DeviceApplicationService {

    private val log = LoggerFactory.getLogger(javaClass)

    private val scopeList = listOf(
        "https://www.huawei.com/healthkit/activity.read",
        "https://www.huawei.com/healthkit/activityrecord.read",
//        "https://www.huawei.com/healthkit/calories.read",
//        "https://www.huawei.com/healthkit/distance.read",
        "https://www.huawei.com/healthkit/heartrate.read",
        "https://www.huawei.com/healthkit/location.read",
        "https://www.huawei.com/healthkit/speed.read",
        "https://www.huawei.com/healthkit/step.read"
    ).sorted()

    private val scopeListWithDistance = listOf(
        "https://www.huawei.com/healthkit/activity.read",
        "https://www.huawei.com/healthkit/activityrecord.read",
//        "https://www.huawei.com/healthkit/calories.read",
        "https://www.huawei.com/healthkit/distance.read",
        "https://www.huawei.com/healthkit/heartrate.read",
        "https://www.huawei.com/healthkit/location.read",
        "https://www.huawei.com/healthkit/speed.read",
        "https://www.huawei.com/healthkit/step.read"
    ).sorted()

    @Autowired
    lateinit var deviceService: DeviceService

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var mapUtils: DeviceMapUtils

    @Autowired
    lateinit var huaweiAuthInfoService: HuaweiUserAuthInfoService

    @Autowired
    lateinit var huaweiAuthService: HuaweiAuthService

    @Autowired
    lateinit var huaweiActivityService: HuaweiActivityService

    @Autowired
    lateinit var garminAuthInfoService: GarminUserAuthInfoService

    @Autowired
    lateinit var garminAuthService: GarminAuthService

    @Autowired
    lateinit var garminActivityService: GarminActivityService

    @Autowired
    lateinit var roozymActivityService: RoozymActivityService

    @Autowired
    lateinit var roozymAuthService: RoozymUserAuthInfoService

    @Autowired
    lateinit var roozymAuthInfoService: RoozymAuthService

    @Autowired
    lateinit var corosAuthService: CorosAuthService

    @Autowired
    lateinit var corosAuthInfoService: CorosUserAuthInfoService

    @Autowired
    lateinit var corosActivityService: CorosActivityService

    @Autowired
    lateinit var ezonAuthInfoService: EzonUserAuthInfoService

    @Autowired
    lateinit var ezonAuthService: EzonAuthService

    @Autowired
    lateinit var ezonActivityService: EzonActivityService

    @Autowired
    lateinit var oppoAuthService: OppoAuthService

    @Autowired
    lateinit var oppoAuthInfoService: OppoUserAuthInfoService

    @Autowired
    lateinit var oppoActivityService: OppoActivityService

    @Autowired
    lateinit var vivoAuthService: VivoAuthService

    @Autowired
    lateinit var vivoAuthInfoService: VivoUserAuthInfoService

    @Autowired
    lateinit var honorAuthInfoService: HonorUserAuthInfoService

    @Autowired
    lateinit var honorAuthService: HonorAuthService

    @Autowired
    lateinit var honorActivityService: HonorActivityService

    @Autowired
    lateinit var vivoActivityService: VivoActivityService

    @Autowired
    lateinit var wanbaoAuthService: WanbaoAuthService

    @Autowired
    lateinit var wanbaoAuthInfoService: WanbaoUserAuthInfoService

    @Autowired
    lateinit var wanbaoActivityService: WanbaoActivityService

    @Autowired
    lateinit var matchConfigService: MatchConfigService

    @Autowired
    lateinit var huamiAuthInfoService: HuamiUserAuthInfoService

    @Autowired
    lateinit var huamiAuthService: HuamiAuthService

    @Autowired
    lateinit var huamiActivityService: HuamiActivityService

    @Suppress("DEPRECATION")
    @CreateCache(name = "DeviceApplicationService.manualSync.", expire = 60 * 5)
    private lateinit var manualSyncCache: Cache<String, Long>

    /**
     * 获取用户设备列表（所有）
     */
    fun getAllUserDevice(userId: Long, onlineState: Int): List<UserDeviceListElementDTO> {
        val deviceList = deviceService.getListByOnlineState(onlineState)
        val result = emptyList<UserDeviceListElementDTO>().toMutableList()
        val testDeviceId = matchConfigService.getTestDeviceId()
        val testUserIdList = matchConfigService.getTestUserIdList()
        deviceList.forEach {
            if (testDeviceId > 0 && it.deviceId == testDeviceId && !testUserIdList.contains(userId)) {
                return@forEach
            }
            val element = UserDeviceListElementDTO()
            element.device = mapUtils.toDeviceDTO(it)
            element.bindTime = 0L
            element.bind = false
            when (it.deviceId) {
                // 华为运动健康
                1L -> {
                    val authInfo = huaweiAuthInfoService.getOneByUserIdOrNull(userId)
                    if (authInfo != null) {
                        element.bind = true
                        element.bindTime = authInfo.createTime.toEpochMilli()
                        element.nickname = authInfo.nickname
                        element.avatar = authInfo.avatar
                    }
                }

                // 佳明海外
                2L -> {
                    val authInfo = garminAuthInfoService.getOneByUserIdOrNull(userId, 2)
                    if (authInfo != null && StrUtil.isNotEmpty(authInfo.accessToken)) {
                        element.bind = true
                        element.bindTime = authInfo.createTime.toEpochMilli()
                    }
                }

                // 如骏
                3L -> {
                    val authInfo = roozymAuthService.getOneByUserIdOrNull(userId)
                    if (authInfo != null) {
                        element.bind = true
                        element.bindTime = authInfo.createTime.toEpochMilli()
                    }
                }

                // 佳明
                4L -> {
                    val authInfo = garminAuthInfoService.getOneByUserIdOrNull(userId, 1)
                    if (authInfo != null && StrUtil.isNotEmpty(authInfo.accessToken)) {
                        element.bind = true
                        element.bindTime = authInfo.createTime.toEpochMilli()
                    }
                }

                // 高驰
                5L -> {
                    val authInfo = corosAuthInfoService.getOneByUserIdOrNull(userId)
                    if (authInfo != null && StrUtil.isNotEmpty(authInfo.refreshToken)) {
                        element.bind = true
                        element.bindTime = authInfo.createTime.toEpochMilli()
                        element.nickname = authInfo.nickname
                        element.avatar = authInfo.avatar
                    }
                }

                // 宜准
                6L -> {
                    val authInfo = ezonAuthInfoService.getOneByUserIdOrNull(userId)
                    if (authInfo != null) {
                        element.bind = true
                        element.bindTime = authInfo.createTime.toEpochMilli()
                        element.nickname = authInfo.nickname
                        element.avatar = authInfo.avatar
                    }
                }

                // OPPO
                7L -> {
                    val authInfo = oppoAuthInfoService.getOneByUserIdOrNull(userId)
                    if (authInfo != null) {
                        element.bind = true
                        element.bindTime = authInfo.createTime.toEpochMilli()
                        element.nickname = authInfo.nickname
                        element.avatar = authInfo.avatar
                    }
                }

                // VIVO
                8L -> {
                    val authInfo = vivoAuthInfoService.getOneByUserIdOrNull(userId)
                    if (authInfo != null) {
                        element.bind = true
                        element.bindTime = authInfo.createTime.toEpochMilli()
                        element.nickname = authInfo.nickname
                        element.avatar = authInfo.avatar
                    }
                }

                // 腕宝
                9L -> {
                    val authInfo = wanbaoAuthInfoService.getOneByUserIdOrNull(userId)
                    if (authInfo != null) {
                        element.bind = true
                        element.bindTime = authInfo.createTime.toEpochMilli()
                    }
                }

                // 荣耀
                10L -> {
                    val authInfo = honorAuthInfoService.getOneByUserIdOrNull(userId)
                    if (authInfo != null) {
                        element.bind = true
                        element.bindTime = authInfo.createTime.toEpochMilli()
                        element.nickname = authInfo.nickname
                        element.avatar = authInfo.avatar
                    }
                }

                // 华米
                11L -> {
                    val authInfo = huamiAuthInfoService.getOneByUserIdOrNull(userId)
                    if (authInfo != null) {
                        element.bind = true
                        element.bindTime = authInfo.createTime.toEpochMilli()
                        element.nickname = authInfo.nickname
                        element.avatar = authInfo.avatar
                    }
                }
            }
            result.add(element)
        }
        return result
    }

    /**
     * 获取用户授权状态
     */
    fun getUserAuthState(userId: Long, deviceId: Long): GetUserAuthInfoRsp {
        val rsp = GetUserAuthInfoRsp()
        when (deviceId) {
            // 华为运动健康
            1L -> {
                rsp.state = 2
                rsp.reason = ""
                // 检查用户是否存在授权信息
                val authInfo = huaweiAuthInfoService.getOneByUserIdOrNull(userId)
                if (authInfo == null) {
                    rsp.state = 1
                    rsp.reason = "用户未授权"
                    return rsp
                }
                // 检查用户授权权限是否不足
                val userScoreList = huaweiAuthService.getScopeListByUserId(userId, authInfo).sorted()
                if (!userScoreList.containsAll(scopeList) && !userScoreList.containsAll(scopeListWithDistance)) {
                    log.debug("system score list is $scopeListWithDistance or $scopeList")
                    log.debug("user score list is $userScoreList")
                    rsp.state = 4
                    rsp.reason = "用户授权权限不足，请重新授权"
                    huaweiAuthService.cancelAuthPassive(userId)
                    return rsp
                }
                // 检查用户是否开启华为运动健康App到Health Kit的开放授权
                val privateAuthState = huaweiAuthService.getPrivacyAuthStateByUserId(userId, authInfo)
                if (privateAuthState != 1) {
                    rsp.state = 4
                    rsp.reason =
                        "用户未开启华为运动健康App到Health Kit的开放授权，苹果设备请通过华为运动健康App-我的-第三方服务-开启华为运动健康服务，安卓设备请通过华为运动健康App-我的-隐私管理-开启华为运动健康服务，并重新授权"
                    huaweiAuthService.cancelAuthPassive(userId)
                    return rsp
                }
                rsp.nickname = authInfo.nickname
                rsp.avatar = authInfo.avatar
                rsp.bindTime = authInfo.createTime.toEpochMilli()
            }

            // 佳明海外
            2L -> {
                rsp.state = 2
                rsp.reason = ""
                val authInfo = garminAuthInfoService.getOneByUserIdOrNull(userId, 2)
                if (authInfo == null || StrUtil.isEmpty(authInfo.accessToken)) {
                    rsp.state = 1
                    rsp.reason = "用户未授权"
                    return rsp
                }
                rsp.bindTime = authInfo.createTime.toEpochMilli()
            }

            // 如骏
            3L -> {
                rsp.state = 2
                rsp.reason = ""
                val authInfo = roozymAuthService.getOneByUserIdOrNull(userId)
                if (authInfo == null) {
                    rsp.state = 1
                    rsp.reason = "用户未授权"
                    return rsp
                }
                rsp.bindTime = authInfo.createTime.toEpochMilli()
            }

            // 佳明
            4L -> {
                rsp.state = 2
                rsp.reason = ""
                val authInfo = garminAuthInfoService.getOneByUserIdOrNull(userId, 1)
                if (authInfo == null || StrUtil.isEmpty(authInfo.accessToken)) {
                    rsp.state = 1
                    rsp.reason = "用户未授权"
                    return rsp
                }
                rsp.bindTime = authInfo.createTime.toEpochMilli()
            }

            // 高驰
            5L -> {
                rsp.state = 2
                rsp.reason = ""
                // 检查用户是否存在授权信息
                val authInfo = corosAuthInfoService.getOneByUserIdOrNull(userId)
                if (authInfo == null) {
                    rsp.state = 1
                    rsp.reason = "用户未授权"
                    return rsp
                }
                rsp.nickname = authInfo.nickname
                rsp.avatar = authInfo.avatar
                rsp.bindTime = authInfo.createTime.toEpochMilli()
            }

            // 宜准
            6L -> {
                rsp.state = 2
                rsp.reason = ""
                // 检查用户是否存在授权信息
                val authInfo = ezonAuthInfoService.getOneByUserIdOrNull(userId)
                if (authInfo == null) {
                    rsp.state = 1
                    rsp.reason = "用户未授权"
                    return rsp
                }
                rsp.nickname = authInfo.nickname
                rsp.avatar = authInfo.avatar
                rsp.bindTime = authInfo.createTime.toEpochMilli()
            }

            // OPPO
            7L -> {
                rsp.state = 2
                rsp.reason = ""
                // 检查用户是否存在授权信息
                val authInfo = oppoAuthInfoService.getOneByUserIdOrNull(userId)
                if (authInfo == null) {
                    rsp.state = 1
                    rsp.reason = "用户未授权"
                    return rsp
                }
                rsp.nickname = authInfo.nickname
                rsp.avatar = authInfo.avatar
                rsp.bindTime = authInfo.createTime.toEpochMilli()
            }

            // VIVO
            8L -> {
                rsp.state = 2
                rsp.reason = ""
                // 检查用户是否存在授权信息
                val authInfo = vivoAuthInfoService.getOneByUserIdOrNull(userId)
                if (authInfo == null) {
                    rsp.state = 1
                    rsp.reason = "用户未授权"
                    return rsp
                }
                rsp.nickname = authInfo.nickname
                rsp.avatar = authInfo.avatar
                rsp.bindTime = authInfo.createTime.toEpochMilli()
            }

            // 腕宝
            9L -> {
                rsp.state = 2
                rsp.reason = ""
                // 检查用户是否存在授权信息
                val authInfo = wanbaoAuthInfoService.getOneByUserIdOrNull(userId)
                if (authInfo == null) {
                    rsp.state = 1
                    rsp.reason = "用户未授权"
                    return rsp
                }
                rsp.bindTime = authInfo.createTime.toEpochMilli()
            }

            // 荣耀
            10L -> {
                rsp.state = 2
                rsp.reason = ""
                // 检查用户是否存在授权信息
                val authInfo = honorAuthInfoService.getOneByUserIdOrNull(userId)
                if (authInfo == null) {
                    rsp.state = 1
                    rsp.reason = "用户未授权"
                    return rsp
                }
                rsp.nickname = authInfo.nickname
                rsp.avatar = authInfo.avatar
                rsp.bindTime = authInfo.createTime.toEpochMilli()
            }

            // 华米
            11L -> {
                rsp.state = 2
                rsp.reason = ""
                // 检查用户是否存在授权信息
                val authInfo = huamiAuthInfoService.getOneByUserIdOrNull(userId)
                if (authInfo == null) {
                    rsp.state = 1
                    rsp.reason = "用户未授权"
                    return rsp
                }
                rsp.nickname = authInfo.nickname
                rsp.avatar = authInfo.avatar
                rsp.bindTime = authInfo.createTime.toEpochMilli()
            }

            else -> return rsp
        }
        return rsp
    }

    /**
     * 取消授权
     */
    fun unbind(userId: Long, deviceId: Long): Boolean {
        when (deviceId) {
            // 华为运动健康
            1L -> return huaweiAuthService.cancelAuthByUserId(userId)

            // 佳明海外
            2L -> return garminAuthService.cancelAuthByUserId(userId, 2)

            // 如骏
            3L -> return roozymAuthInfoService.cancelAuth(userId)

            // 佳明
            4L -> return garminAuthService.cancelAuthByUserId(userId, 1)

            // 高驰
            5L -> return corosAuthService.cancelAuthByUserId(userId)

            // 宜准
            6L -> return ezonAuthService.cancelAuthByUserId(userId)

            // OPPO
            7L -> return oppoAuthService.cancelAuthByUserId(userId)

            // VIVO
            8L -> return vivoAuthService.cancelAuthByUserId(userId)

            // 腕宝
            9L -> return wanbaoAuthService.cancelAuthByUserId(userId)

            // 荣耀
            10L -> return honorAuthService.cancelAuthByUserId(userId)

            // 华米
            11L -> return huamiAuthService.cancelAuthByUserId(userId)
        }
        return true
    }

    /**
     * 手动同步
     */
    fun manualSync(userId: Long, deviceId: Long, clientSource: String?): ManualSyncRsp? {
        val notNullClientSource = clientSource ?: ""
        val cacheKey = "$userId-$deviceId-$notNullClientSource"
        log.debug("[manualSync] cacheKey = $cacheKey start")
        val cache = manualSyncCache.GET(cacheKey)
        if (cache.isSuccess) {
            return ManualSyncRsp().apply {
                this.result = true
                this.reason = "暂无运动记录可同步"
            }
        } else {
            manualSyncCache.PUT(cacheKey, Instant.now().toEpochMilli())
        }
        when (deviceId) {
            // 华为运动健康
            1L -> return huaweiActivityService.syncUserActivityRecordListByManual(userId)

            // 佳明海外
            2L -> return garminActivityService.syncActivityListByManual(userId, 2)

            // 如骏
            3L -> return roozymActivityService.syncUserActivityRecordListByManual(userId)

            // 佳明
            4L -> return garminActivityService.syncActivityListByManual(userId, 1)

            // 高驰
            5L -> return corosActivityService.syncActivityListByManual(userId)

            // 宜准
            6L -> return ezonActivityService.syncActivityListByManual(userId)

            // OPPO
            7L -> return oppoActivityService.syncActivityListByManual(userId)

            // VIVO
            8L -> return vivoActivityService.syncActivityListByManual(userId)

            // 腕宝
            9L -> return wanbaoActivityService.syncActivityListByManual(userId)

            // 荣耀
            10L -> return honorActivityService.syncActivityListByManual(userId)

            // 华米
            11L -> return huamiActivityService.syncActivityListByManual(userId)
        }
        log.debug("[manualSync] cacheKey = $cacheKey end")
        return null
    }

    /**
     * 获取授权链接
     */
    fun authUrl(deviceId: Long, userId: Long): String? {
        val user = userService.getOneById(userId)
        val device = deviceService.getOneByDeviceId(deviceId)
        when (deviceId) {
            1L, 3L, 5L, 6L, 7L, 8L, 9L, 10L, 11L -> return device.authUrl.replace("<TOKEN>", user.appToken)

            // 佳明海外
            2L -> return garminAuthService.getOAuthConfirmUrl(userId, 2)

            // 佳明
            4L -> return garminAuthService.getOAuthConfirmUrl(userId, 1)
        }
        return null
    }

}
