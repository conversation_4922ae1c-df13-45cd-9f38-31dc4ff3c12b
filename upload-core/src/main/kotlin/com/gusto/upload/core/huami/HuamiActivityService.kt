package com.gusto.upload.core.huami

import cn.hutool.core.io.IoUtil
import cn.hutool.core.util.StrUtil
import cn.hutool.json.JSONUtil
import com.dtflys.forest.Forest
import com.dtflys.forest.http.ForestResponse
import com.garmin.fit.EventMesg
import com.garmin.fit.EventType
import com.garmin.fit.FitDecoder
import com.garmin.fit.FitMessages
import com.garmin.fit.FitRuntimeException
import com.garmin.fit.RecordMesg
import com.gusto.framework.core.util.time.minusDays
import com.gusto.upload.core.application.user.NewUserRunApplicationService
import com.gusto.upload.core.common.PushService
import com.gusto.upload.core.service.user.NewUserRunService
import com.gusto.upload.core.util.DistanceUtils
import com.gusto.upload.core.util.NewUserRunMapUtils
import com.gusto.upload.core.util.format
import com.gusto.upload.core.utils.round
import com.gusto.upload.core.utils.roundTwo
import com.gusto.upload.model.entity.huami.HuamiActivity
import com.gusto.upload.model.entity.huami.HuamiUserAuthInfo
import com.gusto.upload.model.entity.huami.rsp.HuamiGetActivityDetailRsp
import com.gusto.upload.model.entity.response.ManualSyncRsp
import com.gusto.upload.model.entity.user.NewUserRun
import com.gusto.upload.model.entity.user.NewUserRunDetailAltitudeItem
import com.gusto.upload.model.entity.user.NewUserRunDetailHeartRateItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLapItem
import com.gusto.upload.model.entity.user.NewUserRunDetailLocationItem
import com.gusto.upload.model.entity.user.NewUserRunDetailStepRateItem
import org.apache.commons.io.FileUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.io.IOException
import java.io.InputStream
import java.time.Duration
import java.time.Instant
import java.util.zip.GZIPInputStream
import kotlin.math.max
import kotlin.math.pow
import kotlin.math.roundToInt

/**
 * 华米-活动服务
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
class HuamiActivityService : HuamiCommonService() {

    private val log = LoggerFactory.getLogger(javaClass)
    private val halfMarathon = 21.0975
    private val marathon = 42.195

    @Autowired
    lateinit var pushService: PushService

    @Autowired
    lateinit var runService: NewUserRunService

    @Autowired
    lateinit var mapUtils: NewUserRunMapUtils

    @Autowired
    lateinit var newRunApplicationService: NewUserRunApplicationService

    /**
     * 根据订阅事件同步活动
     */
    fun syncActivityBySubscription(openId: String, data: String, trackId: String) {
        val authInfo = authInfoService.getOneByOpenIdOrNull(openId) ?: return
        checkOrRefreshAccessToken(authInfo)
        val rsp = huamiClient.getActivityDetailFile(
            buildAuthorization(authInfo.accessToken),
            authInfo.openId,
            data
        )
        if (rsp.isError) {
            log.error("[syncActivityBySubscription] userId = ${authInfo.userId}, get activity detail file error, response content = ${rsp.content}")
            return
        }

        // 创建跑步记录头部和明细
        createUserRunAndDetailBySubscription(authInfo, rsp.result.dataUrl, trackId, data)
    }

    /**
     * 创建跑步记录并上传到APP
     */
    private fun createUserRunAndDetailBySubscription(
        authInfo: HuamiUserAuthInfo,
        dataUrl: String,
        trackId: String,
        data: String
    ) {
        val (createSuccess, run) = uploadRunAndDetailByFit(authInfo, dataUrl, trackId, data)
        log.info("[createUserRunAndDetail] userId = ${authInfo.userId}, sync success, count = 1")
        if (!createSuccess) return
        asyncExecutor.execute {
            pushService.notifySyncSuccess(run!!, 1, authInfo.userId)
        }
    }

    /**
     * 手动同步活动列表
     */
    fun syncActivityListByManual(
        userId: Long,
        reqStartTime: Instant? = null,
        reqEndTime: Instant? = null
    ): ManualSyncRsp {
        val authInfo = authInfoService.getOneByUserId(userId)
        checkOrRefreshAccessToken(authInfo)
        val now = Instant.now()
        val startDate = reqStartTime?.format("yyyy-MM-dd")
            ?: Instant.ofEpochMilli(max(authInfo.createTime.toEpochMilli(), now.minusDays(7).toEpochMilli()))
                .format("yyyy-MM-dd")
        val endDate = reqEndTime?.format("yyyy-MM-dd")
            ?: now.format("yyyy-MM-dd")
        val rsp = huamiClient.getActivityListByDate(
            buildAuthorization(authInfo.accessToken),
            startDate,
            endDate
        )
        val outRsp = ManualSyncRsp()
        outRsp.result = true
        outRsp.reason = "同步成功，请稍后在跑步记录页查看"
        if (rsp.isError) {
            log.error("[syncActivityListByManual] userId = $userId, get activity list error, response content = ${rsp.content}")
            outRsp.result = false
            outRsp.reason = "同步失败，请稍后再试"
            return outRsp
        }
        if (rsp.result == null || rsp.result.items == null || rsp.result.items.isEmpty()) {
            outRsp.reason = "暂无运动记录可同步"
            return outRsp
        }
        // 创建跑步记录头部和明细
        // createUserRunAndDetailByManual(authInfo, rsp.result.items)
        asyncExecutor.execute {
            log.info("[syncActivityListByManual] userId = $userId, sync async start")
            createUserRunAndDetailByManual(authInfo, rsp.result.items)
            log.info("[syncActivityListByManual] userId = $userId, sync async success")
        }
        return outRsp
    }

    /**
     * 创建跑步记录并上传到APP
     */
    private fun createUserRunAndDetailByManual(authInfo: HuamiUserAuthInfo, activityList: List<HuamiActivity>) {
        var syncSuccessCount = 0
        val runList = mutableListOf<NewUserRun>()
        activityList.forEach { activity ->
            val run = buildNewUserRun(authInfo, activity)
            val createSuccess = uploadRunAndDetailByJson(run, activity, authInfo)
            if (!createSuccess) return@forEach
            syncSuccessCount++
            runList.add(run)
        }
        log.info("[createUserRunAndDetail] userId = ${authInfo.userId}, sync success, count = $syncSuccessCount")
        if (syncSuccessCount == 0) return
        asyncExecutor.execute {
            val latestRun = runList.maxByOrNull { it.startTime }
            if (latestRun != null) {
                pushService.notifySyncSuccess(latestRun, syncSuccessCount, authInfo.userId)
            }
        }
    }

    /**
     * 上传跑步记录和明细
     */
    private fun uploadRunAndDetailByJson(
        run: NewUserRun,
        activity: HuamiActivity,
        authInfo: HuamiUserAuthInfo
    ): Boolean {
        val activityDetail = getActivityDetail(authInfo, activity, run) ?: return false
        val startTime = activity.startTime * 1000
        val timestampList = buildTimestampList(activityDetail.samplingTime, startTime)
        val paceList = activityDetail.pace
            .removeSuffix(";")
            .split(";")
            .filter { StrUtil.isNotEmpty(it) }
            .map { it.toDouble() }
        val locationList = buildLocationList(
            activityDetail.latitudeLongitude.removeSuffix(";"),
            paceList,
            timestampList
        )
        val heartRateList = buildHeartRateList(activityDetail.heartRate.removeSuffix(";"), startTime)
        val stepRateList = buildStepRateList(activityDetail.gait.removeSuffix(";"), startTime)
        val altitudeList = buildAltitudeList(activityDetail.altitude.removeSuffix(";"), timestampList)
        run.apply {
            if (paceList.isNotEmpty()) {
                val maxSpeed = paceList
                    .filter { it > 0 }
                    .minOrNull() ?: 0.0
                maxPace = (1000 * maxSpeed).round(0)
            }
            if (heartRateList.isNotEmpty()) {
                maxHeartRate = heartRateList.maxOfOrNull { it.heartRate } ?: 0
            }
            if (altitudeList.isNotEmpty()) {
                minAltitude = altitudeList.minOfOrNull { it.altitude } ?: 0.0
                maxAltitude = altitudeList.maxOfOrNull { it.altitude } ?: 0.0
            }
            if (stepRateList.isNotEmpty()) {
                maxStepRate = stepRateList.maxOfOrNull { it.stepRate } ?: 0
            }
            partTimeKmList = buildPartTimeMap(activityDetail.kilometerPace.removeSuffix(";"), this)
            location = locationList
                .firstOrNull { it.longitude > 0.0 && it.latitude > 0.0 }
                ?.let {
                    "${it.longitude.round(6)},${it.latitude.round(6)}"
                }
        }
        val lapList = buildLapList(run.partTimeKmList, run)
        val createSuccess = newRunApplicationService.handlerAndCreate(run)
        if (createSuccess) {
            val detail = mapUtils.toNewUserRunDetail(run)
            detail.lapList = lapList
            detail.locationList = locationList
            detail.heartRateList = heartRateList
            detail.stepRateList = stepRateList
            detail.altitudeList = altitudeList
            detail.partTimeKmList = run.partTimeKmList
            asyncExecutor.execute {
                // 上传到七牛
                qiniuService.uploadObject(
                    detail,
                    "${run.trackFile}.gzip"
                )
                qiniuService.uploadObject(
                    activityDetail,
                    "oppo_activity_detail_${run.trackFile}.gzip"
                )
            }
        }
        return createSuccess
    }

    /**
     * 每圈信息列表
     */
    private fun buildLapList(partTimeKmList: Map<String, Int>, run: NewUserRun): List<NewUserRunDetailLapItem> {
        val fakePartTimeKmList = partTimeKmList
            .filter { it.key != halfMarathon.toString() && it.key != marathon.toString() }
            .toSortedMap { a, b -> a.toDouble().compareTo(b.toDouble()) }
        val itemList = mutableListOf<NewUserRunDetailLapItem>()
        var currentDuration = 0
        fakePartTimeKmList.forEach {
            if (it.key.toDouble() % 1 == 0.0) {
                val item = NewUserRunDetailLapItem()
                item.distance = 1000.0
                item.duration = it.value - currentDuration
                itemList.add(item)
                currentDuration = it.value
            }
        }
        if (run.totalDistance > itemList.size * 1000) {
            val item = NewUserRunDetailLapItem()
            item.distance = run.totalDistance - itemList.size * 1000
            item.duration = run.totalDuration - itemList.sumOf { it.duration }
            itemList.add(item)
        }
        return itemList
    }

    /**
     * 创建分段千米时间列表
     *
     * 每公里配速统计，格式: {公里索引, 耗费时间(秒), geohash, 点索引, 平均心率, 与开始时间的差值(秒);..}，
     * 公里索引从0开始，例如 0,365,wx4ey4yqd7zt,130,79,366;
     */
    private fun buildPartTimeMap(kilometerPace: String, run: NewUserRun): MutableMap<String, Int> {
        val partTimeMap = mutableMapOf<String, Int>()
        if (StrUtil.isEmpty(kilometerPace)) {
            return partTimeMap
        }
        var currentDuration = 0
        kilometerPace
            .split(";")
            .map {
                val itemList = it.split(",")
                Pair(itemList[0].toInt(), itemList[1].toInt())
            }
            .forEach {
                val currentLap = (it.first + 1).toDouble()
                currentDuration += it.second
                partTimeMap[currentLap.toString()] = currentDuration
                if (currentLap == 21.0 && run.totalDistance >= 21097.5 && run.totalDistance < 22000) {
                    partTimeMap[halfMarathon.toString()] = run.totalDuration
                } else if (currentLap == 42.0 && run.totalDistance >= 42195 && run.totalDistance < 43000) {
                    partTimeMap[marathon.toString()] = run.totalDuration
                }
            }
        return partTimeMap
    }

    /**
     * 创建分段千米时间列表
     */
    private fun buildPartTimeMap(
        recordList: List<RecordMesg>,
        eventList: List<EventMesg>,
        run: NewUserRun
    ): Map<String, Int> {
        var startTime = 0L
        var pauseTime = 0L
        var isPaused = false
        var lastTimestamp = 0L
        var halfMarathonFlag = false
        var marathonFlag = false
        var currentLap = 1
        var currentDuration = 0L
        var currentDistance = 0.0
        val partTimeKmMap = mutableMapOf<String, Int>()
        val eventMap = eventList.associateBy { it.timestamp.date.time }
        recordList.forEachIndexed { index, it ->
            val currentTimestamp = it.timestamp.date.time
            if (startTime == 0L) {
                // 找到第一个开始事件
                val startEvent = eventList.first()
                startTime = startEvent.timestamp.date.time
                lastTimestamp = currentTimestamp
            }

            // 计算duration
            val timeDiff = currentTimestamp - lastTimestamp
            if (isPaused) {
                pauseTime = 2L
            } else {
                currentDuration += timeDiff / 1000
                val distanceDiff = if (index > 1) {
                    val latitude = if (it.positionLat != null) {
                        buildLocationItem(it.positionLat)
                    } else {
                        0.0
                    }
                    val longitude = if (it.positionLong != null) {
                        buildLocationItem(it.positionLong)
                    } else {
                        0.0
                    }
                    val preLatitude = if (recordList[index - 1].positionLat != null) {
                        buildLocationItem(recordList[index - 1].positionLat)
                    } else {
                        0.0
                    }
                    val preLongitude = if (recordList[index - 1].positionLong != null) {
                        buildLocationItem(recordList[index - 1].positionLong)
                    } else {
                        0.0
                    }
                    DistanceUtils.getDistance(
                        latitude,
                        longitude,
                        preLatitude,
                        preLongitude
                    ).roundTwo()
                } else {
                    0.0
                }
                currentDistance = (currentDistance + distanceDiff).roundTwo()
            }
            lastTimestamp = currentTimestamp

            // 处理暂停事件
            if (eventMap.containsKey(currentTimestamp)) {
                val event = eventMap[currentTimestamp]!!
                when (event.eventType) {
                    EventType.STOP, EventType.STOP_ALL -> {
                        isPaused = true
                        pauseTime = 0L
                    }

                    EventType.START -> {
                        isPaused = false
                        currentDuration -= pauseTime
                        pauseTime = 0L
                    }

                    else -> {}
                }
            }

            // 计算分段累计用时
            if (!halfMarathonFlag && currentDistance >= halfMarathon * 1000) {
                val halfMarathonItem = partTimeKmMap[halfMarathon.toString()]
                if (halfMarathonItem == null) {
                    partTimeKmMap[halfMarathon.toString()] = currentDuration.toInt()
                    halfMarathonFlag = true
                }
            } else if (!marathonFlag && currentDistance >= marathon * 1000) {
                val marathonItem = partTimeKmMap[marathon.toString()]
                if (marathonItem == null) {
                    partTimeKmMap[marathon.toString()] = currentDuration.toInt()
                    marathonFlag = true
                }
            } else {
                if (currentDistance >= 1000) {
                    if (currentDistance / 1000 >= currentLap) {
                        partTimeKmMap[currentLap.toDouble().toString()] = currentDuration.toInt()
                        currentLap++
                    }
                }
            }
        }
        // 适配“recordMesgs最后一个点的距离<=sessionMesgs的距离”
        if (run.totalDistance - (currentLap - 1) * 1000 > 1000) {
            partTimeKmMap[currentLap.toDouble().toString()] = run.totalDuration
        }
        return partTimeKmMap
    }

    private fun buildLocationItem(raw: Int): Double {
        return (raw.toDouble() * (180.0 / (2.0.pow(31)))).round(8)
    }

    /**
     * 创建时间戳列表
     */
    private fun buildTimestampList(samplingTime: String, startTime: Long): List<Long> {
        val rawList = samplingTime
            .split(";")
            .filter { StrUtil.isNotEmpty(it) }
            .map { it.toLong() * 1000 }
        var currentStartTime = startTime
        val timestampList = mutableListOf<Long>()
        rawList.forEach {
            currentStartTime += it
            timestampList.add(currentStartTime)
        }
        return timestampList
    }

    /**
     * 创建定位点列表
     */
    private fun buildLocationList(
        latitudeLongitude: String,
        paceList: List<Double>,
        timestampList: List<Long>
    ): List<NewUserRunDetailLocationItem> {
        if (StrUtil.isEmpty(latitudeLongitude)) return emptyList()
        val rawPairList = latitudeLongitude
            .split(";")
            .map {
                if (StrUtil.isNotEmpty(it)) {
                    val itemList = it.split(",")
                    Pair(itemList[0].toDouble(), itemList[1].toDouble())
                } else {
                    Pair(0.0, 0.0)
                }
            }
        var currentLatitude = 0.0
        var currentLongitude = 0.0
        val locationList = mutableListOf<NewUserRunDetailLocationItem>()
        rawPairList.forEachIndexed { index, pair ->
            currentLatitude += pair.first
            currentLongitude += pair.second
            val location = NewUserRunDetailLocationItem()
            location.timestamp = timestampList.getOrNull(index) ?: 0
            location.latitude = currentLatitude / 100000000
            location.longitude = currentLongitude / 100000000
            val currentSpeed = paceList.getOrNull(index) ?: 0.0
            location.speed = if (currentSpeed > 0) {
                (1 / currentSpeed).roundTwo()
            } else {
                0.0
            }
            locationList.add(location)
        }
        return locationList
    }

    /**
     * 创建心率列表
     *
     * 心率列表，格式: {时间差值(秒),心率差值}，第一个时间是与startTime的差值，时间差值为1时用空字符串，
     * 例如11,80;0,10;7,-6;4,1;,-1
     */
    private fun buildHeartRateList(heartRate: String, startTime: Long): List<NewUserRunDetailHeartRateItem> {
        if (StrUtil.isEmpty(heartRate)) return emptyList()
        val rawPairList = heartRate
            .split(";")
            .map {
                val itemList = it.split(",")
                val timeDiff = itemList[0]
                val heartRateDiff = itemList[1]
                if (StrUtil.isNotEmpty(timeDiff)) {
                    Pair(timeDiff.toInt(), heartRateDiff.toInt())
                } else {
                    Pair(1, heartRateDiff.toInt())
                }
            }
        var currentTime = startTime
        var currentHeartRate = 0
        val heartRateList = mutableListOf<NewUserRunDetailHeartRateItem>()
        rawPairList.forEach {
            currentTime += it.first * 1000
            currentHeartRate += it.second
            val item = NewUserRunDetailHeartRateItem()
            item.timestamp = currentTime
            item.heartRate = currentHeartRate
            heartRateList.add(item)
        }
        return heartRateList
    }

    /**
     * 创建步频列表
     *
     * 步态列表，格式: {时间差(秒),步数差,步幅(厘米),步频;…}，
     * 例如2,0,71,0;2,0,0,0;147,1,0,0
     */
    private fun buildStepRateList(gait: String, startTime: Long): List<NewUserRunDetailStepRateItem> {
        if (StrUtil.isEmpty(gait)) return emptyList()
        val rawPairList = gait
            .split(";")
            .map {
                val itemList = it.split(",")
                Pair(itemList[0].toInt(), itemList[3].toInt())
            }
        var currentTime = startTime
        val stepRateList = mutableListOf<NewUserRunDetailStepRateItem>()
        rawPairList.forEach {
            currentTime += it.first * 1000
            val item = NewUserRunDetailStepRateItem()
            item.timestamp = currentTime
            item.stepRate = it.second
            stepRateList.add(item)
        }
        return stepRateList
    }

    /**
     * 创建海拔列表
     *
     * 海拔列表，格式: {海拔;海拔;…}，单位厘米，2016-08之后默认值-2000000，之前为0，
     * 例如7800;7772;7763;7763;-2000000
     */
    private fun buildAltitudeList(
        altitude: String,
        timestampList: List<Long>
    ): List<NewUserRunDetailAltitudeItem> {
        if (StrUtil.isEmpty(altitude)) return emptyList()
        val rawList = altitude
            .split(";")
        val altitudeList = mutableListOf<NewUserRunDetailAltitudeItem>()
        rawList.forEachIndexed { index, it ->
            if (it == "-2000000") return@forEachIndexed
            val currentTime = timestampList.getOrNull(index) ?: 0
            val altitudeItem = NewUserRunDetailAltitudeItem()
            altitudeItem.timestamp = currentTime
            altitudeItem.altitude = it.toDouble() / 100
            altitudeList.add(altitudeItem)
        }
        return altitudeList
    }

    /**
     * 获取运动详情
     */
    private fun getActivityDetail(
        authInfo: HuamiUserAuthInfo,
        activity: HuamiActivity,
        run: NewUserRun
    ): HuamiGetActivityDetailRsp? {
        val dbRun = runService.getListByUserIdAndRunId(run.userId, run.activityId).firstOrNull { it.deviceType == 312 }
        if (dbRun != null) {
            val logPre = "[handlerAndCreate] " +
                    "userId=${run.userId}, " +
                    "deviceId=${run.deviceId}, " +
                    "deviceType=${run.deviceType}, " +
                    "deviceModel=${run.deviceModel}, " +
                    "activityId=${run.activityId}"
            log.debug("$logPre, has been inserted into the database, return false")
            return null
        }
        val rsp = huamiClient.getActivityDetail(buildAuthorization(authInfo.accessToken), activity.trackId)
        if (rsp.isError || rsp.result == null) {
            log.error("[getActivityDetail] get activity detail error, error = ${rsp.content}")
            return null
        }
        return rsp.result
    }

    /**
     * 上传跑步记录和明细
     */
    private fun uploadRunAndDetailByFit(
        authInfo: HuamiUserAuthInfo,
        fitFileUrl: String,
        trackId: String,
        data: String
    ): Pair<Boolean, NewUserRun?> {
        val dbRun =
            runService.getListByUserIdAndRunId(authInfo.userId, trackId.toLong()).firstOrNull { it.deviceType == 312 }
        if (dbRun != null) {
            val logPre = "[handlerAndCreate] " +
                    "userId=${dbRun.userId}, " +
                    "deviceId=${dbRun.deviceId}, " +
                    "deviceType=${dbRun.deviceType}, " +
                    "deviceModel=${dbRun.deviceModel}, " +
                    "activityId=${dbRun.activityId}"
            log.debug("$logPre, has been inserted into the database, return false")
            return Pair(false, null)
        }
        val fitMessages = getFitMessagesByForest(fitFileUrl) ?: return Pair(false, null)
        val session = fitMessages.sessionMesgs.first()
        val recordList = fitMessages.recordMesgs.sortedBy { it.timestamp }
        val deviceInfo = fitMessages.deviceInfoMesgs.firstOrNull()
        val run = NewUserRun().apply {
            activityId = session.startTime.date.time
            userId = authInfo.userId
            deviceId = 3
            deviceType = 312
            deviceModel = deviceInfo?.productName ?: "华米"
            // activityType = formatActivityType(session.sport.name)
            startTime = session.startTime.date.time
            endTime = startTime + session.totalElapsedTime.roundToInt() * 1000
            totalDistance = session.totalDistance.toDouble()
            totalDuration = session.totalTimerTime.toInt()
            // totalStep = session.totalStrides * 2 * (session.totalTimerTime / 60.0).toInt()
            totalCalorie = session.totalCalories.toDouble()
            // averagePace = if (session.avgSpeed != null && session.avgSpeed > 0) {
            //     (1000 / session.avgSpeed).toDouble().roundToInt().toDouble()
            // } else {
            //     0.0
            // }
            maxPace = if (session.maxSpeed != null && session.maxSpeed > 0) {
                (1000 / session.maxSpeed).toDouble().roundToInt().toDouble()
            } else {
                0.0
            }
            averageHeartRate = session.avgHeartRate?.toInt() ?: 0
            maxHeartRate = session.maxHeartRate?.toInt() ?: 0
            averageStepRate = (session.avgCadence ?: 0) * 2
            maxStepRate = (session.maxCadence ?: 0) * 2
            maxStepRate = (session.maxCadence ?: 0) * 2
            // averageStride = totalDistance / totalStep * 100
            // minAltitude = session.minAltitude?.toDouble() ?: 0.0
            // maxAltitude = session.maxAltitude?.toDouble() ?: 0.0
            totalAscent = session.totalAscent?.toDouble() ?: 0.0
            totalDescent = session.totalDescent?.toDouble() ?: 0.0
            weather = ""
            province = ""
            city = ""
            temperature = ""
            humidity = ""
            partTimeKmList = buildPartTimeMap(recordList, fitMessages.eventMesgs.sortedBy { it.timestamp }, this)
            thirdActivityId = trackId
            // thirdActivityType = session.sport.name
            abnormal = 15
            // externalSportType = session.sport.name
            mapTrackImage = ""
            trackImage = ""
            // 用于请求fit文件的参数
            fitUrl = data
        }
        val activityListRsp = huamiClient.getActivityListByDate(
            buildAuthorization(authInfo.accessToken),
            Instant.ofEpochMilli(run.startTime).format("yyyy-MM-dd"),
            Instant.ofEpochMilli(run.endTime).format("yyyy-MM-dd")
        )
        if (activityListRsp.isSuccess) {
            val currentActivity = activityListRsp.result.items.firstOrNull { it.trackId == trackId }
            if (currentActivity != null) {
                run.activityType = formatActivityType(currentActivity.type)
                run.totalStep = currentActivity.totalStep
                run.averageStride = currentActivity.averageStrideLength.toDouble()
                run.thirdActivityType = currentActivity.type
                run.externalSportType = currentActivity.type
                run.averagePace = (currentActivity.averagePace * 1000).round(0)
            } else {
                log.error("[uploadRunAndDetailByFit] get activity list success, but not found activity, trackId = $trackId")
                return Pair(false, null)
            }
        } else {
            log.error("[uploadRunAndDetailByFit] get activity list error, error = ${activityListRsp.content}")
            return Pair(false, null)
        }
        var lapList = mutableListOf<NewUserRunDetailLapItem>()
        fitMessages.lapMesgs.forEach {
            val lap = NewUserRunDetailLapItem()
            lap.distance = it.totalDistance?.toDouble()?.roundTwo() ?: 0.0
            lap.duration = it.totalTimerTime.roundToInt()
            lapList.add(lap)
        }
        if (lapList.all { it.distance == 0.0 }) {
            lapList = mutableListOf()
        }
        val locationList = mutableListOf<NewUserRunDetailLocationItem>()
        val heartRateList = mutableListOf<NewUserRunDetailHeartRateItem>()
        val stepRateList = mutableListOf<NewUserRunDetailStepRateItem>()
        val altitudeList = mutableListOf<NewUserRunDetailAltitudeItem>()
        recordList
            .forEach {
                val timestamp = it.timestamp.date.time
                val speedValue = it.speed?.toDouble()?.roundTwo() ?: 0.0
                val location = NewUserRunDetailLocationItem()
                location.timestamp = timestamp
                location.latitude = if (it.positionLat != null) {
                    buildLocationItem(it.positionLat)
                } else {
                    0.0
                }
                location.longitude = if (it.positionLong != null) {
                    buildLocationItem(it.positionLong)
                } else {
                    0.0
                }
                location.speed = speedValue
                locationList.add(location)

                val heartRate = NewUserRunDetailHeartRateItem()
                heartRate.timestamp = timestamp
                heartRate.heartRate = it.heartRate?.toInt() ?: 0
                heartRateList.add(heartRate)

                val stepRate = NewUserRunDetailStepRateItem()
                stepRate.timestamp = timestamp
                stepRate.stepRate = (it.cadence ?: 0) * 2
                stepRateList.add(stepRate)

                if (it.enhancedAltitude != null) {
                    val altitude = NewUserRunDetailAltitudeItem()
                    altitude.timestamp = timestamp
                    altitude.altitude = it.enhancedAltitude.toDouble().roundTwo()
                    altitudeList.add(altitude)
                }
            }
        run.minAltitude = altitudeList.minOfOrNull { it.altitude } ?: 0.0
        run.maxAltitude = altitudeList.maxOfOrNull { it.altitude } ?: 0.0
        run.location = locationList
            .firstOrNull { it.longitude > 0.0 && it.latitude > 0.0 }
            ?.let {
                "${it.longitude.round(6)},${it.latitude.round(6)}"
            }

        val createSuccess = newRunApplicationService.handlerAndCreate(run)
        if (createSuccess) {
            val detail = mapUtils.toNewUserRunDetail(run)
            detail.lapList = lapList
            detail.locationList = locationList
            detail.heartRateList = heartRateList.filter { it.heartRate > 0 }
            detail.stepRateList = stepRateList
            detail.altitudeList = altitudeList
            detail.partTimeKmList = run.partTimeKmList
            asyncExecutor.execute {
                // 上传到七牛
                qiniuService.uploadObject(
                    detail,
                    "${run.trackFile}.gzip"
                )
                qiniuService.uploadUrl(fitFileUrl!!, "huami_activity_detail_${run.trackFile}.fit")
            }
            return Pair(true, run)
        }
        return Pair(false, null)
    }

    /**
     * 获取并解析fit文件
     */
    fun getFitMessagesByForest(fitFileUrl: String?): FitMessages? {
        if (StrUtil.isEmpty(fitFileUrl)) {
            log.warn("[getFitMessages] url = $fitFileUrl, return null")
            return null
        }
        val startTime = System.currentTimeMillis()
        val response = Forest.get(fitFileUrl)
            .connectTimeout(3000)
            .readTimeout(3000)
            .maxRetryCount(5)
            .maxRetryInterval(100)
            .execute(ForestResponse::class.java)
        val endTime = System.currentTimeMillis()
        if (!response.isSuccess) {
            log.error("[getFitMessages] HttpException downloading file: $fitFileUrl, status = ${response.statusCode}")
            response.contentLength
        }
        val downloadTime = Duration.ofMillis(endTime - startTime).toMillis()
        val fileSize = (response.byteArray.size.toDouble() / FileUtils.ONE_KB).roundTwo()
        log.debug("[getFitMessages] url = $fitFileUrl, download time = $downloadTime ms")
        log.debug("[getFitMessages] url = $fitFileUrl, size = $fileSize kb")
        val fitDecoder = FitDecoder()
        var inputStream: InputStream? = null
        try {
            inputStream = if (fitFileUrl!!.contains("fit.gz")) {
                GZIPInputStream(response.inputStream)
            } else {
                response.inputStream
            }
            return fitDecoder.decode(inputStream)
        } catch (e: IOException) {
            log.error("[getFitMessages] IOException opening file: $fitFileUrl")
        } catch (e: FitRuntimeException) {
            log.error("[getFitMessages] FitRuntimeException decoding file: ${e.message}")
        } catch (e: Exception) {
            log.error("[getFitMessages] Exception decoding file: ${e.message}")
        } finally {
            IoUtil.close(inputStream)

            val runtime = Runtime.getRuntime()
            val totalMemory = runtime.totalMemory() / 1024 / 1024 // 总内存
            val maxMemory = runtime.maxMemory() / 1024 / 1024 // 最大内存
            val freeMemory = runtime.freeMemory() / 1024 / 1024 // 可用内存
            val usedMemory = totalMemory - freeMemory // 已使用内存
            // 内存使用率
            val memoryRate = (usedMemory.toDouble() / maxMemory.toDouble() * 100).roundTwo()
            log.debug("[getFitMessages] 总内存 = $maxMemory MB | 最大内存 = $totalMemory MB | 已使用内存 = $usedMemory MB | 可用内存 = $freeMemory MB | 内存使用率 =  $memoryRate%")
        }
        return null
    }

    /**
     * 创建新跑步记录
     */
    private fun buildNewUserRun(authInfo: HuamiUserAuthInfo, activity: HuamiActivity): NewUserRun {
        val deviceNameJson = JSONUtil.parseObj(activity.deviceName)
        val deviceName = deviceNameJson.getOrDefault("zh-CN", "").toString()
        val run = NewUserRun().apply {
            activityId = activity.startTime * 1000
            userId = authInfo.userId
            deviceId = 3
            deviceType = 312
            deviceModel = deviceName
            activityType = formatActivityType(activity.type)
            startTime = activity.startTime * 1000
            endTime = activity.endTime * 1000
            totalDistance = activity.distance.toDouble()
            totalDuration = activity.sportTime
            totalStep = activity.totalStep
            totalCalorie = activity.calories.toDouble()
            averagePace = (activity.averagePace * 1000).round(0)
            maxPace = 0.0
            averageHeartRate = activity.averageHeartRate
            maxHeartRate = 0
            averageStepRate = activity.averageStepFrequency
            maxStepRate = 0
            averageStride = activity.averageStrideLength.toDouble()
            minAltitude = 0.0
            maxAltitude = 0.0
            totalAscent = activity.altitudeAscend.toDouble()
            totalDescent = activity.altitudeDescend.toDouble()
            weather = ""
            province = ""
            city = ""
            temperature = ""
            humidity = ""
            partTimeKmList = mutableMapOf()
            thirdActivityId = activity.trackId
            thirdActivityType = activity.type
            abnormal = 15
            externalSportType = activity.type
            mapTrackImage = ""
            trackImage = ""
            fitUrl = ""
        }
        return run
    }

    /**
     * 转换活动类型
     *
     * 运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它
     */
    fun formatActivityType(typeString: String): Int {
        return when (typeString) {
            // 户外跑
            "OUTDOOR_RUN",       // 户外跑步
            "DISTANCE_RUN",      // 距离跑
            "TIME_RUN",          // 时间跑
            "CALORIE_RUN",       // 卡路里跑
            "INTERVAL_RUN",      // 间歇跑
            "ROUTE_RUN" -> 1     // 路线跑

            // 室内跑
            "THRADMILL_RUN",     // 跑步机
            "TREADMILL",         // 室内跑
            "INDOOR_RUN" -> 2    // 室内跑步

            // 徒步
            "WALKING",             // 步行
            "HIKING",              // 徒步
            "INDOOR_WALK",         // 室内步行
            "SNOWSHOE_HIKING" -> 3 // 雪鞋徒步

            // 越野
            "TRAILRUN",          // 越野跑
            "MOUNTAINEER",       // 登山
            "ROCK_CLIMBING",     // 攀岩
            "CLIMBING_STAIRS",   // 爬楼
            "ORIENTEERING" -> 4  // 定向越野

            // 骑车
            "OUTDOOR_CYCLING",   // 户外骑行
            "INDOOR_CYCLING",    // 室内骑行
            "MOUNTAIN_RIDING",   // 山地骑行
            "BMX",               // BMX
            "SPINNING_BIKE",     // 动感单车
            "ELECTRIC_BICYCLE",  // 电动自行车
            "ELECTRICMOUNTAINBIKE", // 电动山地车
            "GRAVEL_CYCLING",    // 砾石骑行
            "BIKE_COMMUTING",    // 自行车通勤
            "BIKE_TOURING",      // 自行车旅行
            "ROAD_CYCLING",      // 公路骑行
            "MOUNTAIN_BIKING" -> 5 // 山地骑行

            // 游泳
            "INDOOR_SWIM",        // 室内游泳
            "OPENWATERSWIM",      // 开放水域游泳
            "FIN_SWIMMING",       // 蹼泳
            "SYNCHRONIZED_SWIMMING", // 花样游泳
            "SNORKELING",         // 浮潜
            "OUTDOOR_FREEDIVING", // 户外自由潜水
            "INDOOR_FREEDIVING" -> 6 // 室内自由潜水

            else -> 100
        }
    }

}
