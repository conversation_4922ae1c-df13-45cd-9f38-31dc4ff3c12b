package com.gusto.upload.core.vivo

import cn.hutool.core.util.IdUtil
import cn.hutool.core.util.StrUtil
import cn.hutool.json.JSONConfig
import cn.hutool.json.JSONUtil
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwError
import com.gusto.framework.core.util.json.formatToJson
import com.gusto.framework.core.util.time.minusDays
import com.gusto.upload.core.util.VivoUtils
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.vivo.VivoProperties
import com.gusto.upload.model.entity.vivo.VivoUserAuthInfo
import com.gusto.upload.model.entity.vivo.req.VivoRefreshAccessTokenReq
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.concurrent.Executor
import javax.annotation.Resource

/**
 * <AUTHOR>
 * @since 2024-04-15
 */
@Service
class VivoCommonService {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authInfoService: VivoUserAuthInfoService

    @Autowired
    lateinit var config: VivoProperties

    @Autowired
    lateinit var vivoClient: VivoClient

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    /**
     * 检查用户AT是否过期，如果过期则刷新
     */
    fun checkOrRefreshAccessToken(authInfo: VivoUserAuthInfo) {
        if (authInfo.accessTokenExpireTime.minusDays(1).isAfter(Instant.now())) {
            if (StrUtil.isEmpty(authInfo.nickname)) {
                val userInfoRsp = vivoClient.getUserInfo(Any(), buildHeaders(authInfo.accessToken, null))
                if (userInfoRsp.isSuccess) {
                    authInfo.nickname = userInfoRsp.result.data.firstOrNull()?.nickName ?: ""
                    authInfoService.updateById(authInfo)
                }
            }

            // AT未过期，直接返回
            return
        }
        val result = refreshAccessToken(authInfo)
        if (!result) {
            authInfoService.removeById(authInfo)
            throwError(
                ServiceException(
                    UploadErrorCode.VIVO_REFRESH_ACCESS_TOKEN_ERROR.code,
                    "用户授权已过期，请重新绑定"
                )
            )
        }
    }

    /**
     * 刷新AT
     */
    private fun refreshAccessToken(authInfo: VivoUserAuthInfo): Boolean {
        if (authInfo.refreshTokenExpireTime.isBefore(Instant.now())) {
            // RT已过期
            return false
        }
        val req = VivoRefreshAccessTokenReq().apply {
            refreshToken = authInfo.refreshToken
        }
        val rsp = vivoClient.refreshAccessToken(
            req = req,
            headers = buildHeaders(authInfo.accessToken, req)
        )
        if (rsp.isError) {
            log.warn("[refreshAccessToken] refresh AT error, error = ${rsp.content}")
            return false
        }
        if (rsp.result.code != 200) {
            log.warn("[refreshAccessToken] refresh AT error, rsp = ${rsp.result.formatToJson()}")
            return false
        }
        val rspData = rsp.result.data
        authInfo.accessToken = rspData.accessToken
        authInfo.accessTokenExpireTime = Instant.ofEpochMilli(rspData.accessTokenExpireTime)
        authInfo.refreshToken = rspData.refreshToken
        authInfo.refreshTokenExpireTime = Instant.ofEpochMilli(rspData.refreshTokenExpireTime)
        authInfoService.updateById(authInfo)
        return true
    }

    /**
     * 创建请求头
     */
    protected fun buildHeaders(accessToken: String?, body: Any?): Map<String, Any> {
        val bodyJson = if (body != null) {
            JSONUtil.toJsonStr(body, JSONConfig.create().setNatureKeyComparator())
        } else {
            "{}"
        }
        val headers = mutableMapOf<String, Any>()
        headers["apiVersion"] = "1.0"
        headers["reqId"] = IdUtil.getSnowflakeNextIdStr()
        headers["reqTime"] = Instant.now().toEpochMilli()
        headers["appId"] = config.appId
        if (StrUtil.isNotEmpty(accessToken)) {
            headers["accessToken"] = accessToken!!
        }
        headers["signature"] = VivoUtils.getSign(headers, bodyJson, config.appSecret)
        return headers
    }

}
