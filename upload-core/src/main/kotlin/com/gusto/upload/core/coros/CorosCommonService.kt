package com.gusto.upload.core.coros

import cn.hutool.core.util.StrUtil
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.util.ExceptionUtils.throwError
import com.gusto.framework.core.util.json.formatToJson
import com.gusto.framework.core.util.time.plusDays
import com.gusto.upload.core.qiniu.QiniuService
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.coros.CorosProperties
import com.gusto.upload.model.entity.coros.CorosUserAuthInfo
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.concurrent.Executor
import javax.annotation.Resource

/**
 * 高驰-通用
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Service
class CorosCommonService {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authInfoService: CorosUserAuthInfoService

    @Autowired
    lateinit var config: CorosProperties

    @Autowired
    lateinit var corosClient: CorosClient

    @Autowired
    lateinit var qiniuService: QiniuService

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    /**
     * 检查用户AT是否过期，如果过期则刷新
     */
    fun checkOrRefreshAccessToken(authInfo: CorosUserAuthInfo, fromSub: Boolean = false) {
        val now = Instant.now()
        // AT有效期默认30天
        val expireTime = authInfo.accessTokenUpdateTime.plusDays(25)
        if (now.isAfter(expireTime)) {
            val result = refreshAccessToken(authInfo)
            if (!result) {
                if (!fromSub) {
                    // 从订阅过来的不删除授权
                    authInfoService.removeById(authInfo)
                    throwError(
                        ServiceException(
                            UploadErrorCode.COROS_REFRESH_ACCESS_TOKEN_ERROR.code,
                            "获取授权失败，请返回并重新绑定"
                        )
                    )
                } else {
                    // do nothing
                }
            }
        }
        if (StrUtil.isEmpty(authInfo.nickname)) {
            val rsp = corosClient.getUserInfo(authInfo.accessToken, authInfo.openId)
            if (rsp.isSuccess && rsp.result.message == "OK") {
                authInfo.nickname = rsp.result.data.nick
                if (StrUtil.isNotEmpty(rsp.result.data.profilePhoto)) {
                    val avatar = "coros_avatar_${authInfo.userId}_${Instant.now().epochSecond}.jpg"
                    qiniuService.uploadUrl(rsp.result.data.profilePhoto, avatar)
                    authInfo.avatar = "https://file.gusto.cn/$avatar"
                }
                authInfoService.updateById(authInfo)
            }
        }
    }

    /**
     * 刷新AT
     */
    private fun refreshAccessToken(authInfo: CorosUserAuthInfo): Boolean {
        val rsp = corosClient.refreshAccessToken(
            clientId = config.clientId,
//            refreshToken = java.net.URLEncoder.encode(authInfo.refreshToken, "UTF-8"),
            refreshToken = authInfo.refreshToken,
            clientSecret = config.clientSecret
        )
        if (rsp.isError) {
            log.warn("[refreshAccessToken] refresh AT error, error = ${rsp.content}")
            return false
        }
        if (rsp.result.message == "OK") {
            authInfo.accessTokenUpdateTime = Instant.now()
            authInfoService.updateById(authInfo)
            return true
        }
        log.warn("[refreshAccessToken] refresh AT error, rsp = ${rsp.result.formatToJson()}")
        return false
    }

}
