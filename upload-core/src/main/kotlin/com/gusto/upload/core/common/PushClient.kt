package com.gusto.upload.core.common

import com.dtflys.forest.annotation.BaseRequest
import com.dtflys.forest.annotation.Header
import com.dtflys.forest.annotation.JSONBody
import com.dtflys.forest.annotation.Post
import com.dtflys.forest.http.ForestResponse
import com.gusto.push.common.model.entity.wx.req.WxMpTemplateMessageDTOList

/**
 * 统一推送REST API调用入口
 *
 * <AUTHOR>
 * @since 2021-11-04
 */
@BaseRequest(
    baseURL = "\${pushApiUrl}"
)
interface PushClient {
    /**
     * 批量异步发送服务号模板消息
     */
    @Post("/wx/mp/template/message/batch/send/async")
    fun batchSendWxMpTemplateMessageAsync(
        @JSONBody req: WxMpTemplateMessageDTOList,
        @Header headers: Map<String, Any>
    ): ForestResponse<String>
}