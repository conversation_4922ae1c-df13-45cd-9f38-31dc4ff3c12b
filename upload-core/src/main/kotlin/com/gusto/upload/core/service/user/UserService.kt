package com.gusto.upload.core.service.user

import com.alicp.jetcache.anno.Cached
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.gusto.framework.core.exception.ServiceException
import com.gusto.framework.core.exception.enums.GlobalErrorCode
import com.gusto.framework.core.util.ExceptionUtils.throwIf
import com.gusto.upload.core.common.CacheKeyPrefix.USER_BY_ID
import com.gusto.upload.core.common.CacheKeyPrefix.USER_BY_TOKEN
import com.gusto.upload.core.dao.user.UserDao
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.user.User
import org.springframework.stereotype.Service

/**
 * 用户 服务类
 *
 * <AUTHOR>
 * @since 2021-11-04
 */
@Service
class UserService : ServiceImpl<UserDao, User>() {
    /**
     * 根据token获取
     */
    @Cached(name = USER_BY_TOKEN, key = "#token", expire = 3600 * 12)
    fun getOneByToken(token: String): User {
        val query = QueryWrapper<User>()
        query.eq(User.APP_TOKEN_FIELD, token)
        val user = this.getOne(query)
        throwIf(user == null, ServiceException(GlobalErrorCode.USER_TOKEN_INVALID))
        return user
    }

    /**
     * 根据用户ID获取
     */
    @Cached(name = USER_BY_ID, key = "#userId", expire = 3600 * 12)
    fun getOneById(userId: Long): User {
        val query = QueryWrapper<User>()
        query.eq(User.USER_ID_FIELD, userId)
        val user = this.getOne(query)
        throwIf(user == null, ServiceException(UploadErrorCode.USER_NOT_EXISTS))
        return user
    }
}