package com.gusto.upload.core.qiniu

import com.gusto.upload.model.entity.qiniu.QiniuProperties
import com.qiniu.cdn.CdnManager
import com.qiniu.processing.OperationManager
import com.qiniu.storage.BucketManager
import com.qiniu.storage.Region
import com.qiniu.storage.UploadManager
import com.qiniu.util.Auth
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

/**
 * 七牛-自动配置
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Configuration
@ConditionalOnClass(OperationManager::class)
@EnableConfigurationProperties(QiniuProperties::class)
class QiniuAutoConfiguration {
    @Autowired
    lateinit var qiniuProperties: QiniuProperties

    @Bean
    @ConditionalOnMissingBean(com.qiniu.storage.Configuration::class)
    fun qiniuConfiguration(): com.qiniu.storage.Configuration {
        return com.qiniu.storage.Configuration(Region.huanan())
    }

    @Bean
    @ConditionalOnMissingBean(Auth::class)
    fun qiniuAuth(): Auth {
        return Auth.create(qiniuProperties.accessKey, qiniuProperties.secretKey)
    }

    @Bean
    @ConditionalOnMissingBean(UploadManager::class)
    fun qiniuUploadManager(): UploadManager {
        return UploadManager(qiniuConfiguration())
    }

    @Bean
    @ConditionalOnMissingBean(CdnManager::class)
    fun qiniuCdnManager(): CdnManager {
        return CdnManager(qiniuAuth())
    }

    @Bean
    @ConditionalOnMissingBean(BucketManager::class)
    fun qiniuBucketManager(): BucketManager {
        return BucketManager(qiniuAuth(), qiniuConfiguration())
    }
}