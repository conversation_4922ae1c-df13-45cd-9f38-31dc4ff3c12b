package com.gusto.upload.core

import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation
import com.alicp.jetcache.anno.config.EnableMethodCache
import com.gusto.upload.core.async.AsyncAutoConfiguration
import com.gusto.upload.core.qiniu.QiniuAutoConfiguration
import org.mybatis.spring.annotation.MapperScan
import org.springframework.boot.autoconfigure.ImportAutoConfiguration
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.context.annotation.ComponentScan

/**
 * 启动类
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@EnableMethodCache(basePackages = ["com.gusto.upload"])
@EnableCreateCacheAnnotation
@SpringBootApplication
@ComponentScan(basePackages = ["com.gusto.upload"])
@MapperScan(basePackages = ["com.gusto.upload.core.dao"])
@ImportAutoConfiguration(
    classes = [
        QiniuAutoConfiguration::class,
        AsyncAutoConfiguration::class
    ]
)
class UploadBootstrap {
}