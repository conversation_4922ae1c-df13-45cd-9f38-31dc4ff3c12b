package com.gusto.upload.core.oppo

import com.dtflys.forest.annotation.BaseRequest
import com.dtflys.forest.annotation.Body
import com.dtflys.forest.annotation.Get
import com.dtflys.forest.annotation.Header
import com.dtflys.forest.annotation.LogEnabled
import com.dtflys.forest.annotation.Post
import com.dtflys.forest.annotation.Query
import com.dtflys.forest.annotation.Retry
import com.dtflys.forest.http.ForestResponse
import com.gusto.upload.model.entity.oppo.rsp.OppoCommonRsp
import com.gusto.upload.model.entity.oppo.rsp.OppoGetAccessTokenRsp
import com.gusto.upload.model.entity.oppo.rsp.OppoGetActivityDetailRsp
import com.gusto.upload.model.entity.oppo.rsp.OppoGetActivityRsp
import com.gusto.upload.model.entity.oppo.rsp.OppoGetUserInfoRsp

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@BaseRequest(
    baseURL = "#{upload.oppo.apiHost}"
)
@Retry(maxRetryCount = "5", maxRetryInterval = "100")
interface OppoClient {

    /**
     * 根据授权码换取accessToken和refreshToken
     * accessToken有效期24小时
     */
    @Post("v1/oauth/token")
    fun getAccessToken(
        @Body("clientId") clientId: String,
        @Body("clientSecret") clientSecret: String,
        @Body("grantType") grantType: String = "authorizationCode",
        @Body("authorizationCode") authorizationCode: String,
        @Body("redirectUri") redirectUri: String,
        @Header headers: Map<String, Any>
    ): ForestResponse<OppoCommonRsp<OppoGetAccessTokenRsp>>

    /**
     * 根据refreshToken刷新accessToken
     * refreshToken有效期30天，刷新accessToken同时会刷新refreshToken的有效期。若refreshToken失效，需要重新授权。
     */
    @Post("v1/oauth/token")
    fun refreshAccessToken(
        @Body("clientId") clientId: String,
        @Body("clientSecret") clientSecret: String,
        @Body("grantType") grantType: String = "refreshToken",
        @Body("refreshToken") refreshToken: String,
        @Header headers: Map<String, Any>
    ): ForestResponse<OppoCommonRsp<OppoGetAccessTokenRsp>>

    /**
     * 解除授权
     */
    @Post("v1/oauth/revoke")
    fun cancelAuth(
        @Body("accessToken") accessToken: String,
        @Body("clientSecret") clientSecret: String,
        @Header headers: Map<String, Any>
    ): ForestResponse<OppoCommonRsp<Any>>

    /**
     * 运动记录查询（不包含轨迹GPS点等额外信息），日期最大范围30天
     */
    @Get("v1/data/sport/record")
    fun getActivityListByDate(
        @Query("startTimeMillis") startTimeMillis: Long,
        @Query("endTimeMillis") endTimeMillis: Long,
        @Header headers: Map<String, Any>
    ): ForestResponse<OppoCommonRsp<List<OppoGetActivityRsp>>>

    /**
     * 运动记录查询（包含轨迹GPS点等额外信息）
     */
    @LogEnabled(logResponseContent = false)
    @Get("v2/data/sport/record")
    fun getActivityDetailByDate(
        @Query("startTimeMillis") startTimeMillis: Long,
        @Query("endTimeMillis") endTimeMillis: Long,
        @Header headers: Map<String, Any>
    ): ForestResponse<OppoCommonRsp<List<OppoGetActivityDetailRsp>>>

    /**
     * 查询用户信息
     */
    @Get("v1/data/user/profile")
    fun getUserInfo(
        @Header headers: Map<String, Any>
    ): ForestResponse<OppoCommonRsp<OppoGetUserInfoRsp>>

}
