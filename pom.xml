<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.gusto.upload</groupId>
    <artifactId>upload-server</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>

    <modules>
        <module>upload-api-server</module>
        <module>upload-core</module>
        <module>upload-model</module>
    </modules>

    <properties>
        <revision>latest-dev</revision>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>

        <kotlin.version>1.9.22</kotlin.version>
        <kotlin.compiler.incremental>true</kotlin.compiler.incremental>

        <spring-boot.version>2.7.18</spring-boot.version>
        <gusto-framework.version>1.3.7</gusto-framework.version>
        <push-common-model.version>1.3.3</push-common-model.version>

        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <elasticjob-lite.version>3.0.3</elasticjob-lite.version>
        <forest.version>1.6.3</forest.version>

        <docker.version>0.40.1</docker.version>
        <docker.registry>registry.cn-hangzhou.aliyuncs.com</docker.registry>
        <docker.username><EMAIL></docker.username>
        <docker.password>gusto123$</docker.password>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.gusto.upload</groupId>
                <artifactId>upload-api-server</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.gusto.upload</groupId>
                <artifactId>upload-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.gusto.upload</groupId>
                <artifactId>upload-model</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.gusto.framework</groupId>
                <artifactId>gusto-dependencies</artifactId>
                <version>${gusto-framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!-- 编译 kotlin 和 java 混合代码 https://kotlinlang.org/docs/maven.html#compile-kotlin-and-java-sources -->
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <executions>
                    <execution>
                        <id>compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/main/kotlin</sourceDir>
                                <sourceDir>${project.basedir}/src/main/java</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
                                <sourceDir>${project.basedir}/src/test/java</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <jvmTarget>11</jvmTarget>
                    <args>
                        <arg>-Xuse-experimental=kotlin.contracts.ExperimentalContracts</arg>
                        <arg>-Xjvm-default=all-compatibility</arg>
                    </args>
                    <!-- 解决 spring aop 需要类 open 的问题 https://kotlinlang.org/docs/all-open-plugin.html#maven -->
                    <compilerPlugins>
                        <plugin>spring</plugin>
                    </compilerPlugins>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                </dependencies>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <executions>
                    <execution>
                        <id>default-compile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>default-testCompile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>java-compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>java-test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <!-- 解决 lombok 和 mapstruct 冲突 https://mapstruct.org/faq/#Can-I-use-MapStruct-together-with-Project-Lombok -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <!-- 跳过 mvn 生命周期的测试阶段 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <!-- Docker 插件 -->
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>${docker.version}</version>
                <!-- 全局配置 -->
                <configuration>
                    <!-- Docker 镜像仓库地址 -->
                    <registry>${docker.registry}</registry>
                    <!-- 认证信息 -->
                    <authConfig>
                        <push>
                            <username>${docker.username}</username>
                            <password>${docker.password}</password>
                        </push>
                    </authConfig>
                    <images>
                        <image>
                            <name>${docker.registry}/gusto/${project.name}:${project.version}</name>
                            <build>
                                <dockerFile>${project.basedir}/Dockerfile</dockerFile>
                                <createImageOptions>
                                    <platform>linux/amd64</platform>
                                </createImageOptions>
                            </build>
                        </image>
                    </images>
                    <buildArgs>
                        <!-- Dockerfile 参数，指定 jar 路径 -->
                        <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                    </buildArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
