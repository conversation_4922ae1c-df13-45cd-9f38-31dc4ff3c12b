create table upload_user_auth_info_huami
(
    record_id                int auto_increment comment '记录ID'
        primary key,
    user_id                  int                                     default 0  null comment '用户ID',
    refresh_token            varchar(255)                            default '' null comment 'RefreshToken',
    access_token             varchar(255)                            default '' null comment 'AccessToken',
    access_token_update_time timestamp                                          null comment 'AccessToken更新时间',
    open_id                  varchar(255)                            default '' null comment '高驰用户ID',
    create_time              timestamp                                          null,
    update_time              timestamp                                          null,
    avatar                   varchar(255)                            default '' not null comment '头像',
    nickname                 varchar(255) collate utf8mb4_unicode_ci default '' not null comment '昵称'
);