package com.gusto.upload.api

import cn.hutool.core.codec.Base64
import cn.hutool.core.util.StrUtil
import cn.hutool.crypto.digest.DigestUtil
import cn.hutool.crypto.digest.HmacAlgorithm
import cn.hutool.http.HttpRequest
import cn.hutool.json.JSONConfig
import cn.hutool.json.JSONUtil
import com.baomidou.mybatisplus.core.toolkit.Wrappers
import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.api.job.ReUploadNotifyRecordJob
import com.gusto.upload.core.UploadBootstrap
import com.gusto.upload.core.common.MatchClient
import com.gusto.upload.core.common.PushService
import com.gusto.upload.core.coros.CorosActivityService
import com.gusto.upload.core.ezon.EzonActivityService
import com.gusto.upload.core.ezon.EzonClient
import com.gusto.upload.core.ezon.EzonUserAuthInfoService
import com.gusto.upload.core.huami.HuamiActivityService
import com.gusto.upload.core.huawei.HuaweiActivityService
import com.gusto.upload.core.huawei.HuaweiAuthService
import com.gusto.upload.core.huawei.HuaweiUserAuthInfoService
import com.gusto.upload.core.qiniu.QiniuService
import com.gusto.upload.core.service.user.NewUserRunService
import com.gusto.upload.core.service.user.NewUserRunUploadNotifyService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.util.VivoUtils
import com.gusto.upload.core.utils.run.fillBestResult
import com.gusto.upload.core.vivo.VivoAuthService
import com.gusto.upload.core.wanbao.WanbaoActivityService
import com.gusto.upload.model.entity.user.NewUserRun
import com.gusto.upload.model.entity.vivo.req.VivoGetActivityListReq
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.junit.jupiter.SpringExtension


/**
 * <AUTHOR>
 * @since 2021-11-09
 */
@ExtendWith(SpringExtension::class)
@SpringBootTest(
    classes = [UploadBootstrap::class],
    webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT
)
class SignTest {

    @Test
    fun test() {
        val correctSignature = "aHJUGdrQhsm1TFNlUSNCoGXCTXzsgcGtWPHQRnDkrFI="
        val s = "dM+iHf/5BiG5K6Dq7qYE8OMfDZNDCXn5tGF+BpZFu1w="
        val openId =
            "MDFAMTA0NDczNzQxQDljYzk0YmNiaMTY3MjliaNDNmNjJhMTAxMmVhZDdjYzlmQGM1NWViaMDgyNjNjOTIzN2FhNmJkYjdkNThmYTU5NjRhZjA4ZTQ4MTEyOWUzM2MyNWI5NTE0MDgx"
        val type = "CONNECTION_VERIFY_EVENT\$VERIFY"
        val time = "1636472507224"
        val toSignString = listOf(openId, type, time).joinToString("_")
        println(toSignString)
        val hmac = DigestUtil.hmac(HmacAlgorithm.HmacSHA256, Base64.decode(s))
        val byteArray = hmac.digestBase64(toSignString, false)
        println("req sign: ${correctSignature}")
        println("out sign: ${byteArray}")
    }

    @Autowired
    lateinit var qiniuService: QiniuService

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var huaweiActivityService: HuaweiActivityService

    @Autowired
    lateinit var huaweiAuthService: HuaweiAuthService

    @Autowired
    lateinit var huaweiUserAuthInfoService: HuaweiUserAuthInfoService

    @Test
    fun test2() {
//        val userId = 73881
        val userId = 1152948L
        val authInfo = huaweiUserAuthInfoService.getOneByUserId(userId)
        val scopeList = huaweiAuthService.getScopeListByUserId(userId, authInfo)
        scopeList.forEach {
            println(it)
        }
    }

    @Test
    fun test3() {
        val authList = huaweiUserAuthInfoService.list()
            .filter { it.deleted == false }
        authList.forEachIndexed { index, huaweiUserAuthInfo ->
            if (index % 100 == 99) {
                Thread.sleep(1000)
            }
            try {
                val user = userService.getOneById(huaweiUserAuthInfo.userId)
                if (StrUtil.isNotEmpty(user.appToken)) {
                    val reqMap = mutableMapOf<String, Any>()
                    reqMap["deviceId"] = 1
                    val result = HttpRequest
                        .post("https://uploadapi.gusto.cn/api/device/user/manual/sync")
                        .header("client-token", user.appToken)
                        .body(reqMap.formatToJson())
                        .execute()
                        .body()
                    println("$index | $result")
                }
            } catch (e: Exception) {
                println(e.message)
            }
        }
    }

    @Autowired
    lateinit var corosActivityService: CorosActivityService

    @Test
    fun test4() {
//        val url = "https://oss.coros.com/fit/451936718799519744/461571829121712140.fit"
        val url = "https://oss.coros.com/fit/451936718799519744/464232374555672581_2.fit"
//        corosActivityService.getFitMessages(url)
        val result = corosActivityService.getFitMessagesByForest(url)
        println(result)
    }

    @Autowired
    lateinit var ezonUserAuthInfoService: EzonUserAuthInfoService

    @Autowired
    lateinit var ezonClient: EzonClient

    @Autowired
    lateinit var ezonActivityService: EzonActivityService

    @Test
    fun test5() {
        val authInfo = ezonUserAuthInfoService.getOneByUserId(1152948)
        ezonActivityService.checkOrRefreshAccessToken(authInfo)
        ezonClient.getActivityDetail(authInfo.accessToken, 1681286912813314048)
        return
    }

    @Test
    fun test6() {
        ezonActivityService.syncActivityListByManual(1152948)
    }

    @Autowired
    lateinit var newUserRunService: NewUserRunService

    @Test
    fun test7() {
        val run = newUserRunService.getById(591092)
        run.fillBestResult()
        println(run.formatToJson())
    }

    @Autowired
    lateinit var vivoAuthService: VivoAuthService

    @Test
    fun test8() {
        val req = VivoGetActivityListReq()
        req.startTime = 1713857252949L
        req.endTime = 1713848055000L
        val reqId = "1782672642015383552"
        val reqTime = "1713857252969"
        val secret = "73DFA874C45498436A5044B692639F7A"
        val appId = "5LRZH9KS"
        val bodyJson = if (req != null) {
            JSONUtil.toJsonStr(req, JSONConfig.create().setNatureKeyComparator())
        } else {
            "{}"
        }
        val headers = mutableMapOf<String, Any>()
        headers["apiVersion"] = "1.0"
        headers["reqId"] = reqId
        headers["reqTime"] = reqTime
        headers["appId"] = appId
        headers["accessToken"] = "5d6f986386944056815ecaf4671d1910"
        headers["signature"] = VivoUtils.getSign(headers, bodyJson, secret)
        return
    }

    @Autowired
    lateinit var matchClient: MatchClient

    @Autowired
    lateinit var uploadNotifyService: NewUserRunUploadNotifyService

    @Test
    fun test9() {
        val wrapper = Wrappers.lambdaQuery<NewUserRun>()
            .eq(NewUserRun::getYear, 2024)
            .eq(NewUserRun::getMonth, 9)
            .eq(NewUserRun::getDay, 9)
            .ge(NewUserRun::getUploadTime, "2024-09-09 17:00:00")
            .le(NewUserRun::getUploadTime, "2024-09-09 18:00:00")
        val runList = newUserRunService.list(wrapper)
        println(runList.size)
        var a = 0
        runList
            .filter { it.state == 1 }
            .groupBy { it.userId }
            .forEach { group ->
                group.value
                    .groupBy { v -> v.activityId }
                    .forEach {
                        if (it.value.size > 1) {
                            a++
                            println(group.key.toString() + " | " + it.key + " | " + it.value.size)
                            println(it.value.map { i -> i.recordId })
                            println("--------------------------------------------")
                            // 只保留第一条，其余的删掉
                            val user = userService.getOneById(group.key)
                            it.value.forEachIndexed { index, newUserRun ->
                                if (index > 0) {
                                    println("删除${newUserRun.recordId}，${user.appToken}")
//                                    matchClient.delete(newUserRun.recordId, user.appToken)
                                }
                            }
                        }
                    }
            }
        println(a)
    }

    @Autowired
    lateinit var wanbaoActivityService: WanbaoActivityService

    @Test
    fun test10() {
        val result =
            wanbaoActivityService.getActivityDetailByFileUrl("https://osstest.9n1m.com/evoc1-diving-test/third_sport_json/8e6f6f0d927531a6f8d5a55237388684.json")
        return
    }

    @Autowired
    lateinit var pushService: PushService

    @Test
    fun test11() {
        val run = newUserRunService.getById(24351466)
        pushService.notifySyncSuccess(run, 1, run.userId)
    }

    @Autowired
    lateinit var reUploadNotifyRecordJob: ReUploadNotifyRecordJob

    @Test
    fun test12() {
        reUploadNotifyRecordJob.execute(null)
    }

    @Autowired
    lateinit var huamiActivityService: HuamiActivityService

    @Test
    fun test13() {
        huamiActivityService.syncActivityBySubscription("**********", "{\"app_name\":\"com.xiaomi.hm.health\",\"source\":\"run.7930112.huami.com\",\"track_id\":**********}", "**********")
    }

}