<?xml version="1.0" encoding="UTF-8"?>
<!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出 -->
<!-- scan:当此属性设置为true时，配置文档如果发生改变，将会被重新加载，默认值为true -->
<!-- scanPeriod:设置监测配置文档是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。
             当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
<!-- debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
<configuration scan="true" scanPeriod="10 seconds" debug="false">
    <springProperty scope="context" name="logging.path" source="logging.file.path"/>
    <springProperty scope="context" name="logging.level" source="logging.level.com.gusto.upload"/>
    <springProperty scope="context" name="logging.fileName" source="logging.file.name"/>
    <springProperty scope="context" name="maxFileSize" source="logging.file.maxFileSize"/>
    <springProperty scope="context" name="maxHistory" source="logging.file.maxHistory"/>
    <springProperty scope="context" name="totalSizeCap" source="logging.file.totalSizeCap"/>

    <!-- 控制台颜色控制-->
    <property name="CONSOLE_LOG_PATTERN"
              value="%red(%date{yyyy-MM-dd HH:mm:ss}) %highlight(%-5level) [%X{trace-id}] %red([%thread]) %boldMagenta(%logger{50}) %cyan(%msg%n)"/>


    <!-- 默认的控制台日志输出，一般生产环境都是后台启动-->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>${CONSOLE_LOG_PATTERN}</Pattern>
        </encoder>
    </appender>

    <!-- 日志配置 -->
    <appender name="GUSTO-LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <!-- 过滤器，只记录 logging.level 级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${logging.level}</level>
        </filter>
        <!-- 日志名称 -->
        <file>${logging.path}/${logging.fileName}.log</file>
        <!-- 每天生成一个日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件输出的文件名:按天回滚 daily -->
            <FileNamePattern>${logging.path}/${logging.fileName}.%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <!-- 日志文件限制大小-->
            <maxFileSize>${maxFileSize}</maxFileSize>
            <!-- 日志文件保留天数-->
            <maxHistory>${maxHistory}</maxHistory>
            <!-- 日志总大小-->
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
        </rollingPolicy>

        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{trace-id}] [%thread] %-5level %logger{50} - %msg%n</pattern>
            <!-- 编码 -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>


    <!-- error配置 -->
    <appender name="GUSTO-ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <!-- 过滤器，只记录 logging.level 级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${logging.level}</level>
        </filter>
        <!-- 日志名称 -->
        <file>${logging.path}/${logging.fileName}-error.log</file>
        <!-- 每天生成一个日志文件，保存7天的日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件输出的文件名:按天回滚 daily -->
            <FileNamePattern>${logging.path}/${logging.fileName}-error.%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <!-- 日志文件限制大小-->
            <maxFileSize>${maxFileSize}</maxFileSize>
            <!-- 日志文件保留天数-->
            <maxHistory>${maxHistory}</maxHistory>
            <!-- 日志总大小-->
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
        </rollingPolicy>

        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{trace-id}] [%thread] %-5level %logger{50} - %msg%n</pattern>
            <!-- 编码 -->
            <charset>UTF-8</charset>
        </encoder>

        <!-- 只记录error-->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>error</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- DRUID配置 -->
    <appender name="GUSTO-DRUID" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <!-- 日志名称 -->
        <file>${logging.path}/${logging.fileName}-druid.log</file>
        <!-- 每天生成一个日志文件，保存7天的日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件输出的文件名:按天回滚 daily -->
            <FileNamePattern>${logging.path}/${logging.fileName}-druid.%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <!-- 日志文件限制大小-->
            <maxFileSize>${maxFileSize}</maxFileSize>
            <!-- 日志文件保留天数-->
            <maxHistory>${maxHistory}</maxHistory>
            <!-- 日志总大小-->
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
        </rollingPolicy>

        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{trace-id}] [%thread] %-5level %logger{50} - %msg%n</pattern>
            <!-- 编码 -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 查询语句 -->
    <!--<logger name="druid.sql.Statement" level="DEBUG">
        <appender-ref ref="GUSTO-DRUID" />
    </logger>-->

    <!-- 慢查询-->
    <logger name="com.alibaba.druid.filter.stat.StatFilter" level="ERROR">
        <appender-ref ref="GUSTO-DRUID"/>
    </logger>

    <!-- 根配置 -->
    <root level="info">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="GUSTO-LOG"/>
        <appender-ref ref="GUSTO-ERROR"/>
    </root>
</configuration>
