server:
  port: 30000
  servlet:
    context-path: /api

spring:
  config:
    activate:
      on-profile:
        - dev

  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

  application:
    name: upload-api-server

  datasource:
    name: datasource
    url: **********************************************************************************************************************************************************************************************
    username: gusto_dev_java
    password: X7Hq4)I_Xd5v@k)fs#1$
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      max-active: 200
      initial-size: 20
      max-wait: 30000
      min-idle: 30
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select 'x'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 200
      remove-abandoned: false
      remove-abandoned-timeout: 1800
      log-abandoned: true
      filter:
        # 配置用于监控慢查询 （参考：https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE_StatFilter）
        stat:
          enabled: true
          slow-sql-millis: 100
          log-slow-sql: true
          db-type: mysql
          # 合并语句 优化sql统计功能
          merge-sql: true
        # 日志 使用logback为slf4j (参考：https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE_LogFilter)
        slf4j:
          enabled: true
          statement-executable-sql-log-enable: true
          statement-parameter-set-log-enabled: false
          statement-execute-query-after-log-enabled: false
          statement-prepare-after-log-enabled: false
          statement-execute-after-log-enabled: false
          statement-prepare-call-after-log-enabled: false
          statement-close-after-log-enabled: false
          statement-execute-batch-after-log-enabled: true
        # 防火墙用于配置预防sql注入 (参考：https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE-wallfilter)
        wall:
          enabled: true
          db-type: mysql
          config:
            drop-table-allow: false
            multi-statement-allow: true

  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    password:
    timeout: 3s
    redisson:
      pool-size: 64
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

  kafka:
    # 生产者配置
    producer:
      bootstrapServers: localhost:9092    # 服务连接地址
      retries: 0                          # 重试次数, 默认 0 不重试
      compression-type: gzip              # 压缩算法
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      properties:
        interceptor.classes: com.gusto.upload.core.handler.TraceProducerMethodInterceptor
    # 消费者配置
    consumer:
      bootstrap-servers: localhost:9092   # 服务连接地址
      group-id: upload-server               # 默认消息组的名称
      auto-offset-reset: latest           # 初始化offset的策略
      enable-auto-commit: true
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        interceptor.classes: com.gusto.upload.core.handler.TraceConsumerMethodInterceptor
        spring:
          json:
            trusted:
              packages: com.gusto.upload.model.entity.message
    properties:
      request.timeout.ms: 3000            # 超时时间
      linger.ms: 0                        # 指定 Batch 以时间为策略, 表示延迟多久才发送一次. 0 表示不停留马上发送
      spring:
        json:
          trusted:
            packages: com.gusto.upload.model.entity.message
    listener:
      concurrency: 2

elasticjob:
  regCenter:
    serverLists: localhost:2181
    namespace: elasticjob-lite-upload-api-server
#  jobs:
#    NewUserRunReUploadNotifyJob:
#      overwrite: true
#      elasticJobClass: com.gusto.upload.api.job.NewUserRunReUploadNotifyJob
#      cron: 0 0/5 * * * ?
#      shardingTotalCount: 1
#    GarminReUploadNotifyJob:
#      overwrite: true
#      elasticJobClass: com.gusto.upload.api.job.garmin.GarminReUploadNotifyJob
#      cron: 0 0/10 * * * ?
#      shardingTotalCount: 1
#    CheckNotifyRecordJob:
#      overwrite: true
#      elasticJobClass: com.gusto.upload.api.job.CheckNotifyRecordJob
#      cron: 0 0/5 * * * ?
#      shardingTotalCount: 1
#    ReUploadNotifyRecordJob:
#      overwrite: true
#      elasticJobClass: com.gusto.upload.api.job.ReUploadNotifyRecordJob
#      cron: 0 0/5 * * * ?
#      shardingTotalCount: 1

jetcache:
  statIntervalMinutes: 60
  areaInCacheName: false
  local:
    default:
      type: caffeine
      keyConvertor: fastjson
  remote:
    default:
      type: redis.lettuce
      keyConvertor: fastjson
      valueEncoder: java
      valueDecoder: java
      poolConfig:
        minIdle: 0
        maxIdle: 8
      uri: redis://127.0.0.1:6379/1

logging:
  config: classpath:logback-spring.xml
  level:
    druid.sql.Statement: debug
    com.gusto.upload.dao: debug
    com.gusto.framework: debug
    org.springframework.web: debug
    com.gusto.upload: debug
    com.gusto: debug
    com.alicp.jetcache: debug
    org.apache.kafka.clients.NetworkClient: error
  file:
    # 日志路径
    path: ./logs
    # 日志名字
    name: upload-api-server
    # 日志保留存活天数
    maxHistory: 7
    # 单日志大小(用于分割)
    maxFileSize: 5MB
    # 总日志大小(用于计算文件数量 number = totalSizeCap/maxFileSize )
    totalSizeCap: 100MB

forest:
  backend: okhttp3 # 后端HTTP框架（默认为 okhttp3）
  max-connections: 500 # 连接池最大连接数（默认为 500）
  max-route-connections: 500 # 每个路由的最大连接数（默认为 500）
  timeout: 10000 # 请求超时时间，单位为毫秒（默认为 3000）
  connect-timeout: 10000 # 连接超时时间，单位为毫秒（默认为 timeout）
  read-timeout: 10000 # 数据读取超时时间，单位为毫秒（默认为 timeout）
  max-retry-count: 3 # 请求失败后重试次数（默认为 0 次不重试）
  ssl-protocol: TLSv1.3 # 单向验证的HTTPS的默认SSL协议（默认为 SSLv3）
  logEnabled: true # 打开或关闭日志（默认为 true）
  log-request: true # 打开/关闭Forest请求日志（默认为 true）
  log-response-status: true # 打开/关闭Forest响应状态日志（默认为 true）
  log-response-content: true # 打开/关闭Forest响应内容日志（默认为 false）
  variables:
    pushApiUrl: https://pushapi.gusto.cn/api
    pushAuthName: gutoken-biz
    pushAuthValue: e189ffb88781432a9ff33a03b8198310
    matchApiUrl: https://matchapi.gusto.cn/api

upload:
  huawei:
    clientId: 104473741
    clientSecret: 2830f4a454bc34f7ce8f01964a9abd4aa9d2456aeed1fb2abf5c8c142ced1889
    redirectUri: https://uploaddevapi.gusto.cn/api/huawei/auth/callback
    outApiVersion: 1.0
    subscriptionSecret: dM+iHf/5BiG5K6Dq7qYE8OMfDZNDCXn5tGF+BpZFu1w=
  roozym:
    clientId: c50cacee971e475dac61cb81bf9a33cf
    clientSecret: c15gK02F7JRBcizYU8guTe2Ko67jDkyW
    redirectUri: https://uploaddevapi.gusto.cn/api/roozym/auth/callback
    outApiVersion: 1.0
    # subscriptionSecret: dM+iHf/5BiG5K6Dq7qYE8OMfDZNDCXn5tGF+BpZFu1w=
  garmin:
    consumerKey: 350a8ef9-673a-4959-894d-1f620b0abb1c
    consumerSecret: h8OXpdSsW4zRl5apl7ogFhb6euamATRO9dq
    redirectUri: https://uploaddevapi.gusto.cn/api/garmin/auth/confirm/callback
    globalConsumerKey: 197adc67-6b74-4fbd-97e2-770c99569c97
    globalConsumerSecret: TEwK4sq2L0iDvUlYTnDEy1kpN1TBaxBQ71m
    globalRedirectUri: https://uploaddevapi.gusto.cn/api/garmin/auth/confirm/global/callback
  coros:
    clientId: 0eb9772cff364648a5992b9d306c603e
    clientSecret: 458271d833bc484da18a317c863a821e
    redirectUri: https://uploaddevapi.gusto.cn/api/coros/auth/callback
    apiHost: https://open.coros.com
  ezon:
    clientId: 3bEQYajqwue
    clientSecret: 461973a2-ac7f-11ea-9bef-702084fb8da0
    redirectUri: https://uploaddevapi.gusto.cn/api/ezon/auth/callback
    apiHost: http://www.ezonsport.com
  oppo:
    clientId: q1b9OYqA
    clientSecret: bba605c49d4ac001f00b6db59bebeaa9348a1b48
    redirectUri: https://uploadapi.gusto.cn/api/oppo/auth/callback
    password: IIj8KV5p63DhIerwzZA6ou2OTgr3vuDF
    apiHost: https://sport.health.heytapmobi.com/open
  vivo:
    appId: 5LRZH9KS
    appSecret: 73DFA874C45498436A5044B692639F7A
    apiHost: https://vivo-health.vivo.com.cn
  wanbao:
    clientId: gusto
    clientSecret: gusto_secret
    redirectUri: https://uploaddevapi.gusto.cn/api/wanbao/auth/callback
    authHost: https://diving-authtest.evocqd.com
    apiHost: https://divingtest.evocqd.com
  honor:
    clientId: 104483969
    clientSecret: 061042c06b58d59c99174852bdf5b4297fefe77932245267bd992e2d960330f1
    redirectUri: https://uploaddevapi.gusto.cn/api/honor/auth/callback
  huami:
    clientId: v2Ze71454f733fc4287bf16ff5c81d6e514
    clientSecret: QmobEfAHziYQs+BPLtluYg==
    redirectUri: https://uploaddevapi.gusto.cn/api/huami/auth/callback

oss:
  qiniu:
    bucket: gusto-app
    accessKey: op_iX9Uq6cnL0rfiy-HJ8cWCtbxL-XU8Q2O_Agke
    secretKey: pHdrlhsRbVmPR0Ahh9S688k-a8mYcV0jzAVMXQ4H

management:
  endpoints:
    web:
      exposure:
        include: "*" # 暴露所有端点，包括自定义端点
  endpoint:
    health:
      show-details: always # 显示详细的健康检查信息
  metrics:
    export:
      prometheus:
        enabled: true # 启用Prometheus
    tags:
      application: ${spring.application.name}
  prometheus:
    enabled: true # 启用Prometheus端点

gusto:
  xss:
    enable: true
    excludeUrls:
  error-code:
    logLevelMap:
      5000: WARN
