package com.gusto.upload.api.controller

import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.core.coros.CorosActivityService
import com.gusto.upload.core.garmin.GarminActivityService
import com.gusto.upload.core.handler.CheckLogin
import com.gusto.upload.core.huawei.HuaweiActivityService
import com.gusto.upload.core.service.NotifyRecordService
import com.gusto.upload.core.service.user.NewUserRunService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Instant
import javax.servlet.http.HttpServletRequest

/**
 * 手动 控制类
 *
 * <AUTHOR>
 * @since 2024-06-06
 */
@Tag(name = "手动")
@RestController
@RequestMapping("manual")
class ManualController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var notifyRecordService: NotifyRecordService

    @Autowired
    lateinit var huaweiActivityService: HuaweiActivityService

    @Autowired
    lateinit var garminActivityService: GarminActivityService

    @Autowired
    lateinit var corosActivityService: CorosActivityService

    @Autowired
    lateinit var runService: NewUserRunService

    /**
     * 处理上传通知记录
     */
    @Operation(summary = "处理上传通知记录")
    @GetMapping("handler/upload/notify/record")
    fun handlerUploadNotifyRecord() {
        val recordList = notifyRecordService.list()
        log.info("[handlerUploadNotifyRecord] get record count = ${recordList.size}")
        recordList.forEach {
            log.info("[handlerUploadNotifyRecord] it = ${it.formatToJson()}")
            val splitString = it.uniqueKey.split("-")
            val userId = splitString.last().toLong()
            val startTime = Instant.ofEpochMilli(splitString[0].toLong())
            val endTime = Instant.ofEpochMilli(splitString[1].toLong())
            try {
                when (it.deviceId) {
                    1L -> huaweiActivityService.syncUserActivityRecordListByManual(
                        userId,
                        startTime,
                        endTime,
                        it.uniqueKey
                    )

                    2L -> garminActivityService.syncActivityListByManual(userId, 2, startTime, endTime)
                    4L -> garminActivityService.syncActivityListByManual(userId, 1, startTime, endTime)
                    5L -> corosActivityService.syncActivityListByManual(userId, startTime, endTime)
                }
            } catch (e: Exception) {
                log.error("[handlerUploadNotifyRecord] userId = $userId, deviceId = ${it.deviceId}, error = ${e.message}")
            }
            Thread.sleep(100)
        }
        log.info("[handlerUploadNotifyRecord] end")
    }

    /**
     * 处理地图缺失
     */
    @Operation(summary = "处理地图缺失")
    @CheckLogin
    @GetMapping("handler/map/missing")
    fun handlerMapMission(
        @RequestParam(value = "runRecordId") runRecordId: Long,
        httpRequest: HttpServletRequest
    ) {
        val userId = httpRequest.getAttribute("userId") as Long
        val run = runService.getById(runRecordId)
        if (run == null || run.userId != userId) return
        when (run.deviceType) {
            304 -> huaweiActivityService.handlerMapMission(run)
        }
    }

}
