package com.gusto.upload.api.job

import com.gusto.upload.core.common.MatchClient
import com.gusto.upload.core.service.user.NewUserRunUploadNotifyService
import org.apache.shardingsphere.elasticjob.api.ShardingContext
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * 新用户跑步记录重新上传通知 服务类
 *
 * <AUTHOR>
 * @since 2023-02-23
 */
@Component
class NewUserRunReUploadNotifyJob : SimpleJob {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var uploadNotifyService: NewUserRunUploadNotifyService

    @Autowired
    lateinit var matchClient: MatchClient

    override fun execute(p0: ShardingContext?) {
        log.info("[NewUserRunReUploadNotifyJob] start")

        val notifyList = uploadNotifyService.getListByState(1)
        notifyList.forEach {
            Thread.sleep(500)
            val rsp = matchClient.userRunUploadNotify(it.userId, it.runRecordId)
            if (rsp.isSuccess) {
                it.state = 2
                uploadNotifyService.updateById(it)
            }
        }

        log.info("[NewUserRunReUploadNotifyJob] end")
    }

}
