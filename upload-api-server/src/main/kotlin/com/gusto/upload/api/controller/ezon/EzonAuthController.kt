package com.gusto.upload.api.controller.ezon

import com.gusto.framework.core.bean.CommonResult
import com.gusto.upload.core.ezon.EzonAuthService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * 宜准-授权
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Tag(name = "宜准-授权")
@RestController
@RequestMapping("ezon/auth")
class EzonAuthController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authService: EzonAuthService

    @Autowired
    lateinit var userService: UserService

    /**
     * 授权回调
     */
    @ApiVersion(UploadVersion.EZON)
    @Operation(summary = "授权回调")
    @GetMapping("callback")
    fun confirmCallback(
        @RequestParam("code") code: String,
        @RequestParam("binding_id") clientToken: String,
        @RequestParam("user_id") openId: String
    ): CommonResult<Boolean> {
        val user = userService.getOneByToken(clientToken)
        return CommonResult.success(authService.upsertAccessTokenByCode(code, user.userId, openId))
    }

}
