package com.gusto.upload.api.controller.huawei

import com.gusto.framework.core.bean.CommonResult
import com.gusto.framework.core.exception.ServiceException
import com.gusto.upload.core.huawei.HuaweiAuthService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import com.gusto.upload.model.entity.UploadErrorCode
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.Parameters
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController


/**
 * 华为-授权 控制类
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Tag(name = "华为-授权")
@RestController
@RequestMapping("huawei/auth")
class HuaweiAuthController {
    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authService: HuaweiAuthService

    @Autowired
    lateinit var userService: UserService

    // https://oauth-login.cloud.huawei.com/oauth2/v3/authorize?response_type=code&access_type=offline&state=bf774b02dfa3d60f89a7772d47d7c00a&client_id=104473741&redirect_uri=https://uploadapi.gusto.cn/api/huawei/auth/callback&scope=openid+profile+https://www.huawei.com/healthkit/step.read+https://www.huawei.com/healthkit/distance.read+https://www.huawei.com/healthkit/speed.read+https://www.huawei.com/healthkit/calories.read+https://www.huawei.com/healthkit/activity.read+https://www.huawei.com/healthkit/location.read+https://www.huawei.com/healthkit/activityrecord.read+https://www.huawei.com/healthkit/heartrate.read&display=touch

    /**
     * 授权回调（忽略）
     *
     * 授权码模式（Authorization Code）：
     *   描述：用户授权，生成授权码Code，开发者应用通过Code获取Tokens（如：Access Token、Refresh Token、ID Token）。
     *   场景：应用有服务器，可存储应用密钥，与华为帐号服务器进行密钥交互。
     */
    @ApiVersion(UploadVersion.HUAWEI)
    @Operation(summary = "授权回调（忽略）")
    @Parameters(
        Parameter(name = "code", description = "授权码", required = true),
        Parameter(name = "state", description = "用户Token", required = true),
        Parameter(name = "error", description = "错误码", required = true),
        Parameter(name = "error_description", description = "错误信息", required = true)
    )
    @GetMapping("callback")
    fun callback(
        @RequestParam(value = "code", defaultValue = "") code: String,
        @RequestParam(value = "state", defaultValue = "") token: String,
        @RequestParam(value = "error", defaultValue = "") error: String,
        @RequestParam(value = "error_description", defaultValue = "") errorDesc: String
    ): CommonResult<Boolean> {
        checkCallbackParams(error, errorDesc, token)
        val user = userService.getOneByToken(token)
        val result = CommonResult.success(authService.upsertAccessTokenByCode(code, user.userId))
        // PS 临时处理安卓华为授权问题
        result.msg = "success"
        return result
    }

    /**
     * 授权回调参数检查
     */
    private fun checkCallbackParams(error: String, errorDesc: String, token: String) {
        if (error.isNotEmpty()) {
            val tip = "clientToken=${token}, error = $error, error_description = $errorDesc"
//            throw ServiceException(UploadErrorCode.HUAWEI_CALLBACK_ERROR.code, tip)
            log.warn("[checkCallbackParams] $tip")
            throw ServiceException(
                UploadErrorCode.HUAWEI_CALLBACK_ERROR.code,
                "授权失败，请稍候重试。如出现多次，请联系客服"
            )
        }
        if (token.isEmpty()) {
            log.warn("[checkCallbackParams] token empty")
            throw ServiceException(
                UploadErrorCode.HUAWEI_CALLBACK_TOKEN_EMPTY.code,
                "授权失败，请稍候重试。如出现多次，请联系客服"
            )
        }
    }
}