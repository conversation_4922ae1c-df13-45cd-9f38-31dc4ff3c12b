package com.gusto.upload.api.controller.coros

import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.core.coros.CorosUserAuthInfoService
import com.gusto.upload.core.kafka.producer.CorosActivityProducer
import com.gusto.upload.core.service.NotifyRecordService
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import com.gusto.upload.model.entity.NotifyRecord
import com.gusto.upload.model.entity.coros.req.CorosNotifyActivityListReq
import com.gusto.upload.model.entity.coros.rsp.CorosCommonRsp
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.concurrent.Executor
import javax.annotation.Resource
import javax.servlet.http.HttpServletRequest

/**
 * 高驰-订阅
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Tag(name = "高驰-订阅")
@RestController
@RequestMapping("coros/subscription")
class CorosSubscribeController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var producer: CorosActivityProducer

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    @Autowired
    lateinit var notifyRecordService: NotifyRecordService

    @Autowired
    lateinit var authInfoService: CorosUserAuthInfoService

    /**
     * 运动数据推送
     */
    @ApiVersion(UploadVersion.COROS)
    @Operation(summary = "运动数据推送")
    @PostMapping("notify/activity/list")
    fun notifyActivityList(
        @RequestBody req: CorosNotifyActivityListReq,
        httpRequest: HttpServletRequest
    ): CorosCommonRsp {
        log.debug("[notifyActivityList] ${req.formatToJson()}")
        log.debug("[notifyActivityList] get notify size = ${req.sportDataList.size}")
        // 异步处理订阅事件
        asyncExecutor.execute {
            if (req.sportDataList.isNotEmpty()) {
                req.sportDataList.forEach {
                    val authInfo = authInfoService.getOneByOpenIdOrNull(it.openId) ?: return@forEach
                    val activityMessage = it.buildMessage(authInfo.userId)
                    val notifyRecord = NotifyRecord().apply {
                        this.uniqueKey = activityMessage.buildUniqueKey()
                        this.deviceId = 5
                    }
                    val dbRecord = notifyRecordService.getOneByUniqueKeyOrNull(activityMessage.buildUniqueKey())
                    if (dbRecord == null) {
                        notifyRecordService.save(notifyRecord)
                        producer.sendNotifyActivityMessage(activityMessage)
                    } else {
                        log.debug("[notifyActivityList] 重复消息：${activityMessage.buildUniqueKey()}")
                    }
                }
            }
        }
        val rsp = CorosCommonRsp()
        rsp.message = "ok"
        rsp.result = "0000"
        return rsp
    }

    /**
     * 健康检查
     */
    @ApiVersion(UploadVersion.COROS)
    @Operation(summary = "健康检查")
    @GetMapping("health/check")
    fun checkHealth(): CorosCommonRsp {
        val rsp = CorosCommonRsp()
        rsp.message = "ok"
        rsp.result = "0000"
        return rsp
    }

}
