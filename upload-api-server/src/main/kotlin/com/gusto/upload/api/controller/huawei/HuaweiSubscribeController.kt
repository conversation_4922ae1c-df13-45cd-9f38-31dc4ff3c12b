package com.gusto.upload.api.controller.huawei

import com.gusto.upload.core.huawei.HuaweiSubscriptionService
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import com.gusto.upload.model.entity.huawei.request.HuaweiNotificationReq
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.concurrent.Executor
import javax.annotation.Resource
import javax.servlet.http.HttpServletRequest

/**
 * 华为-订阅 控制类
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Tag(name = "华为-订阅")
@RestController
@RequestMapping("huawei/subscription")
class HuaweiSubscribeController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var subscriptionService: HuaweiSubscriptionService

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    /**
     * 订阅事件通知（忽略）
     */
    @ApiVersion(UploadVersion.HUAWEI)
    @Operation(summary = "订阅事件通知（忽略）")
    @PostMapping("healthkit/notifications")
    fun notification(
        @RequestBody req: HuaweiNotificationReq,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {
        val huaweiSignature = httpRequest.getHeader("x-notification-signature")
        if (huaweiSignature.isNullOrEmpty()) return ResponseEntity(true, HttpStatus.NO_CONTENT)

        // 校验签名
        val signResult = subscriptionService.sign(req)
        if (huaweiSignature != signResult) {
            log.error("[notification] sign error")
            return ResponseEntity(false, HttpStatus.NOT_FOUND)
        }

        // 异步处理订阅事件
        asyncExecutor.execute {
            subscriptionService.handler(req)
        }

        return ResponseEntity(true, HttpStatus.NO_CONTENT)
    }

}
