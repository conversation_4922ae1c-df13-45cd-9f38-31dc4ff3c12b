package com.gusto.upload.api.controller.wanbao

import com.gusto.framework.core.bean.CommonResult
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import com.gusto.upload.core.wanbao.WanbaoAuthService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @since 2024-09-12
 */
@Tag(name = "腕宝-授权")
@RestController
@RequestMapping("wanbao/auth")
class WanbaoAuthController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authService: WanbaoAuthService

    @Autowired
    lateinit var userService: UserService

    /**
     * 授权回调（忽略）
     */
    @ApiVersion(UploadVersion.WANBAO)
    @Operation(summary = "授权回调（忽略）")
    @GetMapping("callback")
    fun callback(
        @RequestParam(value = "code", defaultValue = "") code: String,
        @RequestParam(value = "state", defaultValue = "") state: String
    ): CommonResult<Boolean> {
        val user = userService.getOneByToken(state)
        return CommonResult.success(authService.upsertAccessTokenByCode(code, user.userId))
    }

}