package com.gusto.upload.api.controller.honor

import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.core.honor.HonorUserAuthInfoService
import com.gusto.upload.core.kafka.producer.HonorActivityProducer
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import com.gusto.upload.core.utils.honor.toMessageOrNull
import com.gusto.upload.model.entity.honor.req.HonorNotificationReq
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.concurrent.Executor
import javax.annotation.Resource
import javax.servlet.http.HttpServletRequest

/**
 * 荣耀-订阅 控制类
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Tag(name = "荣耀-订阅")
@RestController
@RequestMapping("honor/subscription")
class HonorSubscribeController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authInfoService: HonorUserAuthInfoService

    @Autowired
    lateinit var activityProducer: HonorActivityProducer

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    /**
     * 订阅事件通知（忽略）
     */
    @ApiVersion(UploadVersion.HONOR)
    @Operation(summary = "订阅事件通知（忽略）")
    @PostMapping("notify/activity")
    fun notifyActivity(
        @RequestBody req: HonorNotificationReq,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {
        log.debug("[notifyActivity] req = ${req.formatToJson()}")
        // 异步处理订阅事件
        asyncExecutor.execute {
            val userId = authInfoService.getUserIdByOpenId(req.openId)
            val activityMessage = req.toMessageOrNull(userId)
            activityProducer.sendSubscriptionMessage(activityMessage)
        }

        return ResponseEntity(true, HttpStatus.NO_CONTENT)
    }

}
