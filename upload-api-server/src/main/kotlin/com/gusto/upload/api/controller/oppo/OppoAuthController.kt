package com.gusto.upload.api.controller.oppo

import com.gusto.framework.core.bean.CommonResult
import com.gusto.upload.core.oppo.OppoAuthService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController


/**
 * <AUTHOR>
 * @since 2024-01-29
 */
@Tag(name = "OPPO-授权")
@RestController
@RequestMapping("oppo/auth")
class OppoAuthController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authService: OppoAuthService

    @Autowired
    lateinit var userService: UserService

    /**
     * 授权回调（忽略）
     */
    @ApiVersion(UploadVersion.OPPO)
    @Operation(summary = "授权回调（忽略）")
    @GetMapping("callback")
    fun callback(
        @RequestParam(value = "authorizeCode", defaultValue = "") authorizeCode: String,
        @RequestParam(value = "openId", defaultValue = "") openId: String,
        @RequestParam(value = "token", defaultValue = "") token: String
    ): CommonResult<Boolean> {
        val user = userService.getOneByToken(token)
        val result =
            CommonResult.success(authService.upsertAccessTokenByCode(authorizeCode, openId, user.userId))
        return result
    }

}