package com.gusto.upload.api.controller.vivo

import com.gusto.framework.core.bean.CommonResult
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import com.gusto.upload.core.vivo.VivoAuthService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @since 2024-04-15
 */
@Tag(name = "VIVO-授权")
@RestController
@RequestMapping("vivo/auth")
class VivoAuthController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authService: VivoAuthService

    @Autowired
    lateinit var userService: UserService

    /**
     * 授权回调（忽略）
     */
    @ApiVersion(UploadVersion.VIVO)
    @Operation(summary = "授权回调（忽略）")
    @GetMapping("callback")
    fun callback(
        @RequestParam(value = "code", defaultValue = "") code: String,
        @RequestParam(value = "state", defaultValue = "") state: String
    ): CommonResult<Boolean> {
        val user = userService.getOneByToken(state)
        val result = CommonResult.success(authService.upsertAccessTokenByCode(code, user.userId))
        return result
    }

}