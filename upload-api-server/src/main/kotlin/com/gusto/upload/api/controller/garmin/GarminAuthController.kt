package com.gusto.upload.api.controller.garmin

import com.gusto.framework.core.bean.CommonResult
import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.core.garmin.GarminAuthService
import com.gusto.upload.core.garmin.GarminUserAuthInfoService
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import com.gusto.upload.model.entity.garmin.req.GarminCancelAuthCallBackReq
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * 佳明-授权 控制类
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@Tag(name = "佳明-授权")
@RestController
@RequestMapping("garmin/auth")
class GarminAuthController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authService: GarminAuthService

    @Autowired
    lateinit var authInfoService: GarminUserAuthInfoService

    /**
     * 确认授权回调
     */
    @ApiVersion(UploadVersion.GARMIN)
    @Operation(summary = "确认授权回调")
    @GetMapping("confirm/callback")
    fun confirmCallback(
        @RequestParam("oauth_token") requestToken: String,
        @RequestParam("oauth_verifier") oauthVerifier: String
    ): CommonResult<Boolean> {
        return CommonResult.success(authService.confirmCallback(requestToken, oauthVerifier, 1))
    }

    /**
     * 取消授权回调
     */
    @ApiVersion(UploadVersion.GARMIN)
    @Operation(summary = "取消授权回调")
    @PostMapping("cancel/callback")
    fun cancelAuth(@RequestBody req: GarminCancelAuthCallBackReq): ResponseEntity<String> {
        log.debug("[cancelAuth] ${req.formatToJson()}")
        val message = req.deregistrations
        message.forEach {
            val authInfo = authInfoService.getOneByAccessTokenOrNull(it.userAccessToken, 1) ?: return@forEach
            authInfoService.delete(authInfo)
        }
        return ResponseEntity(HttpStatus.OK)
    }

    /**
     * 确认海外授权回调
     */
    @ApiVersion(UploadVersion.GARMIN)
    @Operation(summary = "确认海外授权回调")
    @GetMapping("confirm/global/callback")
    fun confirmGlobalCallback(
        @RequestParam("oauth_token") requestToken: String,
        @RequestParam("oauth_verifier") oauthVerifier: String
    ): CommonResult<Boolean> {
        return CommonResult.success(authService.confirmCallback(requestToken, oauthVerifier, 2))
    }

    /**
     * 取消海外授权回调
     */
    @ApiVersion(UploadVersion.GARMIN)
    @Operation(summary = "取消海外授权回调")
    @PostMapping("cancel/global/callback")
    fun cancelGlobalAuth(@RequestBody req: GarminCancelAuthCallBackReq): ResponseEntity<String> {
        log.debug("[cancelGlobalAuth] ${req.formatToJson()}")
        val message = req.deregistrations
        message.forEach {
            val authInfo = authInfoService.getOneByAccessTokenOrNull(it.userAccessToken, 2) ?: return@forEach
            authInfoService.delete(authInfo)
        }
        return ResponseEntity(HttpStatus.OK)
    }

}
