package com.gusto.upload.api.controller.huami

import com.gusto.framework.core.bean.CommonResult
import com.gusto.upload.core.huami.HuamiAuthService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * 华米-授权
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Tag(name = "华米-授权")
@RestController
@RequestMapping("huami/auth")
class HuamiAuthController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authService: HuamiAuthService

    @Autowired
    lateinit var userService: UserService

    /**
     * 授权回调
     */
    @ApiVersion(UploadVersion.HUAMI)
    @Operation(summary = "授权回调")
    @GetMapping("callback")
    fun confirmCallback(
        @RequestParam("code") code: String,
        @RequestParam("state") state: String
    ): CommonResult<Boolean> {
        val user = userService.getOneByToken(state)
        return CommonResult.success(authService.upsertAccessTokenByCode(code, user))
    }

}
