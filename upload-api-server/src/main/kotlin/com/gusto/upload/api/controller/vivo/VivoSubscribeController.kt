package com.gusto.upload.api.controller.vivo

import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.core.kafka.producer.VivoActivityProducer
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import com.gusto.upload.model.entity.message.VivoActivityMessage
import com.gusto.upload.model.entity.vivo.VivoProperties
import com.gusto.upload.model.entity.vivo.rsp.VivoCommonRsp
import com.gusto.upload.model.entity.vivo.rsp.VivoNotifyActivityRecordRsp
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.concurrent.Executor
import javax.annotation.Resource
import javax.servlet.http.HttpServletRequest

/**
 * <AUTHOR>
 * @since 2024-04-19
 */
@Tag(name = "VIVO-订阅")
@RestController
@RequestMapping("vivo")
class VivoSubscribeController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var producer: VivoActivityProducer

    @Autowired
    lateinit var properties: VivoProperties

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    /**
     * 运动数据推送
     */
    @ApiVersion(UploadVersion.VIVO)
    @Operation(summary = "运动数据推送")
    @PostMapping("healthKit/push/exercise")
    fun notifyActivityList(
        @RequestBody req: VivoNotifyActivityRecordRsp,
        httpRequest: HttpServletRequest
    ): VivoCommonRsp<Any> {
        val rsp = VivoCommonRsp<Any>().apply {
            code = 200
            msg = "SUCCESS"
        }
        val appId = httpRequest.getHeader("appId")
        if (appId != properties.appId) {
            rsp.code = 5003
            rsp.msg = "非法的应用appId"
            return rsp
        }
        log.debug("[notifyActivityList] get activity detail = ${req.formatToJson()}")
        // 异步处理订阅事件
        asyncExecutor.execute {
            val message = VivoActivityMessage().apply {
                openId = req.openId
                exerciseId = req.eid
            }
            producer.sendNotifyActivityMessage(message)
        }
        return rsp
    }

}
