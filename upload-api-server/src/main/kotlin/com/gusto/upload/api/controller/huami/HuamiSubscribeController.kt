package com.gusto.upload.api.controller.huami

import cn.hutool.json.JSONObject
import cn.hutool.json.JSONUtil
import com.gusto.upload.core.huami.HuamiActivityService
import com.gusto.upload.core.huami.HuamiUserAuthInfoService
import com.gusto.upload.core.kafka.producer.HuamiActivityProducer
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import com.gusto.upload.model.entity.huami.HuamiProperties
import com.gusto.upload.model.entity.message.HuamiActivityMessage
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.apache.tomcat.util.codec.binary.Base64
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.util.Base64Utils
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URLDecoder
import java.security.KeyFactory
import java.security.Signature
import java.security.spec.X509EncodedKeySpec
import java.util.concurrent.Executor
import javax.annotation.Resource
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec


/**
 * 华米-订阅
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Tag(name = "华米-订阅")
@RestController
@RequestMapping("huami/subscription")
class HuamiSubscribeController {

    private val log = LoggerFactory.getLogger(javaClass)
    private val AES_KEY_ALGORITHM = "AES"
    private val PUBLIC_KEY =
        "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtZ+frsYls4vb3QPU+o+Vdo69f55sLvdesQZdtSuPxCI0j+LWUT8euN1qOEjW1npdKCaE0W2hUIfmFZHIFpsbWQAxax9cAlxv0ri3gynB+5bCL8cWcFpdFb3lUZbajo8BtaAy0ujn09FoCCD/zcee95Acwh+eXM+gn30+8Sf8dzsdzK0BpejPmsztwvHa9PUXrcX2KnbmNa+wc+ed4u76JZPnw0F2l+owOWevbnqHZx0EwDkIwYSHdXqtucv1fFuLF1Trsra/yCpFYdGL8190vySYFumEdptqEL9WYKJY9iQdL59V7NIhlTT6/yZ0a+jacu3h4gFaEoCwjampJtzOqwIDAQAB"

    @Autowired
    lateinit var config: HuamiProperties

    @Autowired
    lateinit var producer: HuamiActivityProducer

    @Autowired
    lateinit var authInfoService: HuamiUserAuthInfoService

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    @Autowired
    lateinit var activityService: HuamiActivityService

    /**
     * 运动数据推送
     */
    @ApiVersion(UploadVersion.HUAMI)
    @Operation(summary = "运动数据推送")
    @PostMapping("notify/activity/list")
    fun notifyActivityList(
        @RequestBody encryptedData: String,
        @RequestHeader("Huami-Signature") signature: String
    ): ResponseEntity<Any> {
        log.debug("[notifyActivityList] encrypt = $encryptedData, signature = $signature")
        val result = verifySignature(encryptedData, signature)
        if (!result) {
            log.error("[notifyActivityList] verify signature failed")
            return ResponseEntity(false, HttpStatus.UNAUTHORIZED)
        }

        val jsonString = decryptBase64(encryptedData, config.clientSecret)
        log.debug("[notifyActivityList] decrypt = $jsonString")
        val jsonArray = JSONUtil.parseArray(jsonString)
        val activityList = mutableListOf<JSONObject>()
        for (i in 0 until jsonArray.size) {
            val eventStr = jsonArray.getStr(i)
            val eventObj = JSONUtil.parseObj(eventStr)
            if ("sportChange" == eventObj.getStr("eventType") && "add" == eventObj.getStr("changeType")) {
                val dataObj = JSONUtil.parseObj(eventObj.getStr("data"))
                eventObj.set("data", dataObj)
                activityList.add(eventObj)
            }
        }

        if (activityList.isNotEmpty()) {
            val authInfo = authInfoService.getOneByOpenIdOrNull(activityList[0].getOrDefault("userId", "").toString())
            if (authInfo != null) {
                asyncExecutor.execute {
                    activityList.forEach {
                        val message = HuamiActivityMessage().apply {
                            this.openId = it.getOrDefault("userId", "").toString()
                            this.data = it.getOrDefault("data", "").toString()
                            this.trackId = it.getJSONObject("data").getOrDefault("track_id", "").toString()
                        }
                        producer.sendNotifyActivityMessage(message)
                        // activityService.syncActivityBySubscription(message.openId, message.data, message.trackId)
                    }
                }
            }
        }

        return ResponseEntity(true, HttpStatus.NO_CONTENT)
    }

    /**
     * 验证签名
     */
    private fun verifySignature(data: String, sign: String): Boolean {
        // base64 decode
        val publicKeyBytes: ByteArray = Base64Utils.decodeFromString(PUBLIC_KEY)
        val spec = X509EncodedKeySpec(publicKeyBytes)

        // SHA1WithRSA
        val keyFactory = KeyFactory.getInstance("RSA")
        val publicKey = keyFactory.generatePublic(spec)
        val signature = Signature.getInstance("SHA1WithRSA")
        signature.initVerify(publicKey)

        // UTF-8
        signature.update(data.toByteArray(charset("UTF-8")))

        // step1 URLDecoder
        // step2 Base64Decode
        // step3 verify
        return signature.verify(Base64Utils.decodeFromString(URLDecoder.decode(sign, "UTF-8")))
    }

    /**
     * 解密
     */
    private fun decrypt(content: ByteArray?, key: String?): ByteArray {
        try {
            val bytesKey: ByteArray = Base64.decodeBase64(key)
            val secretKey = SecretKeySpec(bytesKey, AES_KEY_ALGORITHM)
            val cipher = Cipher.getInstance(AES_KEY_ALGORITHM)
            cipher.init(Cipher.DECRYPT_MODE, secretKey)
            return cipher.doFinal(content)
        } catch (e: Exception) {
            throw RuntimeException("decrypt content failed", e)
        }
    }

    /**
     * 解密
     */
    private fun decryptBase64(base64Content: String, key: String): String {
        try {
            // 解码 Base64 密文
            val decodedBytes = Base64.decodeBase64(base64Content)
            // 使用解密方法解密
            val decryptedBytes = decrypt(decodedBytes, key)
            // 返回解密后的字符串
            return String(decryptedBytes, charset("UTF-8"))
        } catch (e: Exception) {
            throw RuntimeException("decrypt base64 content failed", e)
        }
    }

}
