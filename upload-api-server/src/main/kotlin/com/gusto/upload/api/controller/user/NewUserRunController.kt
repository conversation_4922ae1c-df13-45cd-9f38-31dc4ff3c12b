package com.gusto.upload.api.controller.user

import com.gusto.framework.core.bean.CommonResult
import com.gusto.upload.core.application.user.NewUserRunApplicationService
import com.gusto.upload.core.handler.CheckLogin
import com.gusto.upload.model.entity.user.req.AppUploadNewUserRunReq
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import javax.servlet.http.HttpServletRequest

/**
 * 新跑步记录 控制类
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Tag(name = "新跑步记录")
@RestController
@RequestMapping("user/run")
class NewUserRunController {

    @Autowired
    lateinit var userRunService: NewUserRunApplicationService

    /**
     * APP 上传跑步记录
     */
    @Operation(summary = "APP 上传跑步记录", description = "登录检查，返回跑步记录ID")
    @CheckLogin
    @PostMapping("upload/app")
    fun uploadByApp(
        @RequestBody req: AppUploadNewUserRunReq,
        httpRequest: HttpServletRequest
    ): CommonResult<Long> {
        return CommonResult.success(userRunService.uploadByApp(req, httpRequest))
    }

}

