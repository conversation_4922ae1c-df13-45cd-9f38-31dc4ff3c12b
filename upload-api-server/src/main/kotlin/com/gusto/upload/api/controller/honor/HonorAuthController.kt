package com.gusto.upload.api.controller.honor

import com.gusto.framework.core.bean.CommonResult
import com.gusto.framework.core.exception.ServiceException
import com.gusto.upload.core.honor.HonorAuthService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import com.gusto.upload.model.entity.UploadErrorCode
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.Parameters
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * 荣耀-授权 控制类
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Tag(name = "荣耀-授权")
@RestController
@RequestMapping("honor/auth")
class HonorAuthController {
    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authService: HonorAuthService

    @Autowired
    lateinit var userService: UserService

    // https://hnoauth-login.cloud.honor.com/oauth2/v3/authorize?client_id=104483969&redirect_uri=https://uploaddevapi.gusto.cn/api/honor/auth/callback&scope=openid+profile+https://www.hihonor.com/healthkit/step.read+https://www.hihonor.com/healthkit/distance.read+https://www.hihonor.com/healthkit/speed.read+https://www.hihonor.com/healthkit/calories.read+https://www.hihonor.com/healthkit/location.read+https://www.hihonor.com/healthkit/altitude.read+https://www.hihonor.com/healthkit/heartrate.read+https://www.hihonor.com/healthkit/activityrecord.read&response_type=code&access_type=offline&state=bf774b02dfa3d60f89a7772d47d7c00a

    /**
     * 授权回调（忽略）
     */
    @ApiVersion(UploadVersion.HONOR)
    @Operation(summary = "授权回调（忽略）")
    @Parameters(
        Parameter(name = "code", description = "授权码", required = true),
        Parameter(name = "state", description = "用户Token", required = true),
        Parameter(name = "error", description = "错误码", required = true),
        Parameter(name = "sub_error", description = "子错误码", required = true),
        Parameter(name = "error_description", description = "错误信息", required = true)
    )
    @GetMapping("callback")
    fun callback(
        @RequestParam(value = "code", defaultValue = "") code: String,
        @RequestParam(value = "state", defaultValue = "") token: String,
        @RequestParam(value = "error", defaultValue = "") error: String,
        @RequestParam(value = "sub_error", defaultValue = "") subError: String,
        @RequestParam(value = "error_description", defaultValue = "") errorDesc: String
    ): CommonResult<Boolean> {
        checkCallbackParams(error, subError, errorDesc, token)
        val user = userService.getOneByToken(token)
        return CommonResult.success(authService.upsertAccessTokenByCode(code, user.userId))
    }

    /**
     * 授权回调参数检查
     */
    private fun checkCallbackParams(
        error: String,
        subError: String,
        errorDesc: String,
        token: String
    ) {
        if (error.isNotEmpty()) {
            val tip = "clientToken=${token}, error = $error, sub_error = $subError, error_description = $errorDesc"
            log.warn("[checkCallbackParams] $tip")
            throw ServiceException(
                UploadErrorCode.HONOR_CALLBACK_ERROR.code,
                "授权失败，请稍候重试。如出现多次，请联系客服"
            )
        }
        if (token.isEmpty()) {
            log.warn("[checkCallbackParams] token empty")
            throw ServiceException(
                UploadErrorCode.HONOR_CALLBACK_ERROR.code,
                "授权失败，请稍候重试。如出现多次，请联系客服"
            )
        }
    }
}