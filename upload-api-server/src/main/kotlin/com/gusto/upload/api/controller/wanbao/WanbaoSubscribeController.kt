package com.gusto.upload.api.controller.wanbao

import cn.hutool.core.codec.Base64
import cn.hutool.core.util.StrUtil
import com.gusto.framework.core.bean.CommonResult
import com.gusto.framework.core.exception.ServiceException
import com.gusto.upload.core.kafka.producer.WanbaoActivityProducer
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import com.gusto.upload.model.entity.UploadErrorCode
import com.gusto.upload.model.entity.message.WanbaoActivityMessage
import com.gusto.upload.model.entity.wanbao.WanbaoProperties
import com.gusto.upload.model.entity.wanbao.req.WanbaoNotifyActivityReq
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.concurrent.Executor
import javax.annotation.Resource
import javax.servlet.http.HttpServletRequest

/**
 * <AUTHOR>
 * @since 2024-09-12
 */
@Tag(name = "腕宝-订阅")
@RestController
@RequestMapping("wanbao/subscription")
class WanbaoSubscribeController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var producer: WanbaoActivityProducer

    @Autowired
    lateinit var config: WanbaoProperties

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    /**
     * 运动数据推送
     */
    @ApiVersion(UploadVersion.WANBAO)
    @Operation(summary = "运动数据推送")
    @PostMapping("notify/activity")
    fun notifyActivity(
        @RequestBody req: WanbaoNotifyActivityReq,
        httpRequest: HttpServletRequest
    ): CommonResult<Boolean> {
        // 校验
        val authorization = httpRequest.getHeader("Authorization")
        if (StrUtil.isEmpty(authorization)) throw ServiceException(
            UploadErrorCode.WANBAO_PUSH_ERROR.code,
            "鉴权失败"
        )
        val authString = "Basic " + Base64.encode("${config.clientId}:${config.clientSecret}")
        if (!StrUtil.equals(authorization, authString)) {
            log.error("[notifyActivity] auth error")
            throw ServiceException(UploadErrorCode.WANBAO_PUSH_ERROR.code, "鉴权失败")
        }

        // 异步处理订阅事件
        asyncExecutor.execute {
            val message = WanbaoActivityMessage().apply {
                openId = req.memberAccountId
                countId = req.countId
                sportType = req.sportType
                fileUrl = req.fileUrl
            }
            producer.sendNotifyActivityMessage(message)
        }

        return CommonResult.success(true)
    }

}
