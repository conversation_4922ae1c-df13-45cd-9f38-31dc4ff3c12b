package com.gusto.upload.api.controller.garmin

import com.gusto.framework.core.util.json.formatToJson
import com.gusto.upload.core.garmin.GarminUserAuthInfoService
import com.gusto.upload.core.kafka.producer.GarminActivityProducer
import com.gusto.upload.core.service.NotifyRecordService
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import com.gusto.upload.model.entity.NotifyRecord
import com.gusto.upload.model.entity.garmin.req.GarminNotifyActivityDetailListReq
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.concurrent.Executor
import javax.annotation.Resource

/**
 * 佳明-订阅
 *
 * <AUTHOR>
 * @since 2022/5/23
 */
@Tag(name = "佳明-订阅")
@RestController
@RequestMapping("garmin/subscription")
class GarminSubscribeController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var producer: GarminActivityProducer

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    @Autowired
    lateinit var notifyRecordService: NotifyRecordService

    @Autowired
    lateinit var authInfoService: GarminUserAuthInfoService

    /**
     * 活动列表通知
     */
    @ApiVersion(UploadVersion.GARMIN)
    @Operation(summary = "活动列表通知")
    @PostMapping("notify/activity/detail/list/ping")
    fun notifyActivityDetailListByPing(@RequestBody req: GarminNotifyActivityDetailListReq): ResponseEntity<String> {
        log.debug("[notifyActivityDetailListByPing] ${req.formatToJson()}")
        // 异步处理订阅事件
        asyncExecutor.execute {
            if (req.activityDetails.isNotEmpty()) {
                req.activityDetails.forEach {
                    val authInfo = authInfoService.getOneByAccessToken(it.userAccessToken, 1) ?: return@forEach
                    val activityMessage = it.buildMessage(1, authInfo.userId)
                    val notifyRecord = NotifyRecord().apply {
                        this.uniqueKey = activityMessage.buildUniqueKey()
                        this.deviceId = 4
                    }
                    notifyRecordService.save(notifyRecord)
                    producer.sendNotifyActivityMessage(activityMessage)
                }
            }
        }
        return ResponseEntity(HttpStatus.OK)
    }

    /**
     * 海外活动列表通知
     */
    @ApiVersion(UploadVersion.GARMIN)
    @Operation(summary = "海外活动列表通知")
    @PostMapping("notify/global/activity/detail/list/ping")
    fun notifyGlobalActivityDetailListByPing(@RequestBody req: GarminNotifyActivityDetailListReq): ResponseEntity<String> {
        log.debug("[notifyGlobalActivityDetailListByPing] ${req.formatToJson()}")
        // 异步处理订阅事件
        asyncExecutor.execute {
            if (req.activityDetails.isNotEmpty()) {
                req.activityDetails.forEach {
                    val authInfo = authInfoService.getOneByAccessToken(it.userAccessToken, 2) ?: return@forEach
                    val activityMessage = it.buildMessage(2, authInfo.userId)
                    val notifyRecord = NotifyRecord().apply {
                        this.uniqueKey = activityMessage.buildUniqueKey()
                        this.deviceId = 2
                    }
                    notifyRecordService.save(notifyRecord)
                    producer.sendNotifyActivityMessage(activityMessage)
                }
            }
        }
        return ResponseEntity(HttpStatus.OK)
    }

}
