package com.gusto.upload.api.controller

import com.gusto.framework.core.bean.CommonResult
import com.gusto.upload.core.application.DeviceApplicationService
import com.gusto.upload.core.handler.CheckLogin
import com.gusto.upload.model.entity.dto.UserDeviceListElementDTO
import com.gusto.upload.model.entity.request.DeviceIdReq
import com.gusto.upload.model.entity.response.GetUserAuthInfoRsp
import com.gusto.upload.model.entity.response.ManualSyncRsp
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import javax.servlet.http.HttpServletRequest

/**
 * 设备 控制类
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Tag(name = "设备")
@RestController
@RequestMapping("device")
class DeviceController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var deviceService: DeviceApplicationService

    @Autowired
    lateinit var userAuthService: DeviceApplicationService

    /**
     * 获取用户设备列表
     */
    @Operation(summary = "获取用户设备列表", description = "登录检查")
    @CheckLogin
    @GetMapping("user/all/online")
    fun getAllUserDeviceWithOnline(httpRequest: HttpServletRequest): CommonResult<List<UserDeviceListElementDTO>> {
        val userId = httpRequest.getAttribute("userId") as Long
        return CommonResult.success(deviceService.getAllUserDevice(userId, 1))
    }

    /**
     * 获取用户授权状态
     */
    @Operation(
        summary = "获取用户授权状态",
        description = "登录检查，返回授权状态：0-默认 1-未授权 2-已授权 3-已过期 4-授权失败"
    )
    @Parameter(name = "deviceId", description = "设备ID", required = true)
    @CheckLogin
    @GetMapping("user/bind/state")
    fun getUserAuthState(
        @RequestParam(value = "deviceId", defaultValue = "0") deviceId: Long,
        httpRequest: HttpServletRequest
    ): CommonResult<GetUserAuthInfoRsp> {
        val userId = httpRequest.getAttribute("userId") as Long
        return CommonResult.success(userAuthService.getUserAuthState(userId, deviceId))
    }

    /**
     * 取消授权
     */
    @Operation(summary = "取消授权", description = "登录检查")
    @CheckLogin
    @PostMapping("user/unbind")
    fun unbind(@RequestBody req: DeviceIdReq, httpRequest: HttpServletRequest): CommonResult<Boolean> {
        val userId = httpRequest.getAttribute("userId") as Long
        return CommonResult.success(deviceService.unbind(userId, req.deviceId))
    }

    /**
     * 手动同步
     */
    @Operation(summary = "手动同步", description = "登录检查")
    @CheckLogin
    @PostMapping("user/manual/sync")
    fun manualSync(@RequestBody req: DeviceIdReq, httpRequest: HttpServletRequest): CommonResult<ManualSyncRsp?> {
        val userId = httpRequest.getAttribute("userId") as Long
        val clientSource = httpRequest.getHeader("client-source")
        return CommonResult.success(deviceService.manualSync(userId, req.deviceId, clientSource))
    }

    /**
     * 获取授权链接
     */
    @Operation(summary = "获取授权链接", description = "登录检查")
    @Parameter(name = "deviceId", description = "设备ID", required = true)
    @CheckLogin
    @GetMapping("auth/url")
    fun getAuthUrl(
        @RequestParam(value = "deviceId", defaultValue = "0") deviceId: Long,
        httpRequest: HttpServletRequest
    ): CommonResult<String> {
        val userId = httpRequest.getAttribute("userId") as Long
        return CommonResult.success(deviceService.authUrl(deviceId, userId))
    }

}
