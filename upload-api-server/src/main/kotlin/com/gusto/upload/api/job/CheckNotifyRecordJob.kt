package com.gusto.upload.api.job

import com.alicp.jetcache.Cache
import com.alicp.jetcache.anno.CreateCache
import com.gusto.upload.core.common.MatchConfigService
import com.gusto.upload.core.service.NotifyRecordService
import me.chanjar.weixin.cp.api.WxCpService
import org.apache.shardingsphere.elasticjob.api.ShardingContext
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.time.Instant

/**
 * 检查第三方开放平台通知
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Component
class CheckNotifyRecordJob : SimpleJob {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var notifyRecordService: NotifyRecordService

    @Autowired
    lateinit var configService: MatchConfigService

    @Autowired
    lateinit var wxCpService: WxCpService

    @CreateCache(name = "CheckNotifyRecordJob.notifyCache.", expire = 3600 * 3)
    private lateinit var notifyCache: Cache<Long, Long>

    override fun execute(p0: ShardingContext?) {
        log.info("[CheckNotifyRecordJob] start")

        val allRecord = notifyRecordService.list()
        allRecord
            .groupBy { it.deviceId }
            .forEach { (deviceId, recordList) ->
                val recordCount = recordList.size
                val alertCount = configService.getNotifyRecordAlertCount()
                val cache = notifyCache.GET(deviceId)
                if (recordCount >= alertCount) {
                    // 3小时内只通知一次
                    if (cache.isSuccess) return
                    notifyCache.PUT(deviceId, Instant.now().toEpochMilli())
                    wxCpService.groupRobotService.sendText(
                        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7affb7a2-99fd-433b-b3a1-45c48793e726",
                        "当前${buildDeviceName(deviceId)} ${recordCount} 条通知记录超过了 ${alertCount} 条，请及时处理。",
                        emptyList(),
                        emptyList()
                    )
                } else {
                    if (cache.isSuccess) {
                        notifyCache.REMOVE(deviceId)
                        wxCpService.groupRobotService.sendText(
                            "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7affb7a2-99fd-433b-b3a1-45c48793e726",
                            "当前${buildDeviceName(deviceId)} ${recordCount} 条通知记录已小于 ${alertCount} 条。",
                            emptyList(),
                            emptyList()
                        )
                    }
                }
            }

        log.info("[CheckNotifyRecordJob] end")
    }

    private fun buildDeviceName(deviceId: Long): String {
        return when (deviceId) {
            1L -> "华为"
            2L -> "佳明国际"
            4L -> "佳明"
            5L -> "高驰"
            else -> "未知"
        }
    }

}
