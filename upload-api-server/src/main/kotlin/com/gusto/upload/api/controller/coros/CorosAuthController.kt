package com.gusto.upload.api.controller.coros

import com.gusto.framework.core.bean.CommonResult
import com.gusto.upload.core.coros.CorosAuthService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * 高驰-授权
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Tag(name = "高驰-授权")
@RestController
@RequestMapping("coros/auth")
class CorosAuthController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authService: CorosAuthService

    @Autowired
    lateinit var userService: UserService

    /**
     * 授权回调
     */
    @ApiVersion(UploadVersion.COROS)
    @Operation(summary = "授权回调")
    @GetMapping("callback")
    fun confirmCallback(
        @RequestParam("code") code: String,
        @RequestParam("state") state: String
    ): CommonResult<Boolean> {
        val user = userService.getOneByToken(state)
        return CommonResult.success(authService.upsertAccessTokenByCode(code, user))
    }

}
