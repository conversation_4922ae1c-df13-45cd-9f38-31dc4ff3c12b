package com.gusto.upload.api.controller.roozym

import com.gusto.framework.core.bean.CommonResult
import com.gusto.upload.core.roozym.RoozymAuthService
import com.gusto.upload.core.service.user.UserService
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.Parameters
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * 如骏-授权控制类
 *
 * <AUTHOR>
 * @since 2022/6/9
 */
@Tag(name = "如骏-授权")
@RestController
@RequestMapping("roozym/auth")
class RoozymAuthController {

    // https://rj-api-test.iipii.net/oauth/authorize?client_id=c50cacee971e475dac61cb81bf9a33cf&redirect_uri=https://uploaddevapi.gusto.cn/api/roozym/auth/callback&response_type=code&scope=read&state=11111
    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var authService: RoozymAuthService

    @Autowired
    lateinit var userService: UserService

    /**
     * 授权回调（忽略）
     *
     * 授权码模式（Authorization Code）：
     *   描述：用户授权，生成授权码Code，开发者应用通过Code获取Tokens（如：Access Token、Refresh Token、ID Token）。
     *   场景：应用有服务器，可存储应用密钥，与如骏帐号服务器进行密钥交互。
     */
    @ApiVersion(UploadVersion.ROOZYM)
    @Operation(summary = "授权回调（忽略）")
    @Parameters(
        Parameter(name = "code", description = "授权码", required = true),
        Parameter(name = "state", description = "用户Token", required = true)
    )
    @GetMapping("callback")
    fun callback(
        @RequestParam(value = "code") code: String,
        @RequestParam(value = "state") token: String
    ): CommonResult<Boolean> {
        if (code == "") {
            log.info("[Roozym:callback] code null or empty")
        }
        if (token == "") {
            log.info("[Roozym:callback] token null or empty")
        }
        log.debug("[Roozym:callback] code = ${code}, token = ${token}")
        val user = userService.getOneByToken(token)
        return CommonResult.success(authService.upsertAccessTokenByCode(code, user.userId))
    }
}
