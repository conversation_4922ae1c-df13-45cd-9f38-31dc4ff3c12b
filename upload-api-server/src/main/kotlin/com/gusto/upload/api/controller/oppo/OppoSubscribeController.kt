package com.gusto.upload.api.controller.oppo

import cn.hutool.json.JSONUtil
import com.gusto.upload.core.kafka.producer.OppoActivityProducer
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import com.gusto.upload.core.util.OppoUtils
import com.gusto.upload.model.entity.message.OppoActivityMessage
import com.gusto.upload.model.entity.oppo.OppoProperties
import com.gusto.upload.model.entity.oppo.req.OppoNotifyActivityListReq
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.concurrent.Executor
import javax.annotation.Resource
import javax.servlet.http.HttpServletRequest

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Tag(name = "OPPO-订阅")
@RestController
@RequestMapping("oppo/subscription")
class OppoSubscribeController {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var producer: OppoActivityProducer

    @Autowired
    lateinit var properties: OppoProperties

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    /**
     * 运动数据推送
     */
    @ApiVersion(UploadVersion.OPPO)
    @Operation(summary = "运动数据推送")
    @PostMapping("notify/activity/list")
    fun notifyActivityList(
        @RequestBody req: OppoNotifyActivityListReq,
        httpRequest: HttpServletRequest
    ): ResponseEntity<Any> {
        log.debug("[notifyActivityList] encrypt = ${req.data}")
        val jsonString = OppoUtils.aesDecryptByGCM(properties.password, req.data)
        log.debug("[notifyActivityList] decrypt = $jsonString")
        if (jsonString.contains("dataType")) {
            val jsonObject = JSONUtil.parseObj(jsonString)
            val reqDataType = jsonObject.getOrDefault("dataType", "").toString()
            if (reqDataType == "SPORT_RECORD") {
                // 异步处理订阅事件
                asyncExecutor.execute {
                    val message = OppoActivityMessage().apply {
                        openId = jsonObject.getOrDefault("openId", "").toString()
                        dataType = reqDataType
                        timestamp = jsonObject.getOrDefault("timestamp", "0").toString().toLong()
                    }
                    producer.sendNotifyActivityMessage(message)
                }
            }
        }
        return ResponseEntity(true, HttpStatus.NO_CONTENT)
    }

}
