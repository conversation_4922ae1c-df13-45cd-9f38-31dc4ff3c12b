package com.gusto.upload.api.job

import com.alicp.jetcache.Cache
import com.alicp.jetcache.anno.CreateCache
import com.gusto.upload.core.coros.CorosActivityService
import com.gusto.upload.core.garmin.GarminActivityService
import com.gusto.upload.core.huawei.HuaweiActivityService
import com.gusto.upload.core.service.NotifyRecordService
import com.gusto.upload.core.util.formatDateHourMinuteSecond
import org.apache.shardingsphere.elasticjob.api.ShardingContext
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.time.Instant

/**
 * 重新上传第三方开放平台通知
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Component
class ReUploadNotifyRecordJob : SimpleJob {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var notifyRecordService: NotifyRecordService

    @Autowired
    lateinit var huaweiActivityService: HuaweiActivityService

    @Autowired
    lateinit var garminActivityService: GarminActivityService

    @Autowired
    lateinit var corosActivityService: CorosActivityService

    @CreateCache(name = "ReUploadNotifyRecordJob.executeCache.", expire = 3600 * 3)
    private lateinit var executeCache: Cache<String, Long>

    override fun execute(p0: ShardingContext?) {
        log.info("[ReUploadNotifyRecordJob] start")

        val cache = executeCache.GET("1")
        if (cache.isSuccess) {
            val time = Instant.ofEpochMilli(cache.value)
            log.debug("[ReUploadNotifyRecordJob] 上一次定时任务还在执行中（${time.formatDateHourMinuteSecond()})），跳过")
            return
        }
        executeCache.PUT("1", Instant.now().toEpochMilli())

        val recordList = notifyRecordService.list()
        log.info("[ReUploadNotifyRecordJob] get record count = ${recordList.size}")
        recordList.forEach {
            val splitString = it.uniqueKey.split("-")
            val userId = splitString.last().toLong()
            val startTime = Instant.ofEpochMilli(splitString[0].toLong())
            val endTime = Instant.ofEpochMilli(splitString[1].toLong())
            try {
                when (it.deviceId) {
                    1L -> huaweiActivityService.syncUserActivityRecordListByManual(
                        userId,
                        startTime,
                        endTime,
                        it.uniqueKey
                    )

                    2L -> garminActivityService.syncActivityListByManual(userId, 2, startTime, endTime)
                    4L -> garminActivityService.syncActivityListByManual(userId, 1, startTime, endTime)
                    5L -> corosActivityService.syncActivityListByManual(userId, startTime, endTime, it)
                }
            } catch (e: Exception) {
                log.error("[ReUploadNotifyRecordJob] userId = $userId, deviceId = ${it.deviceId}, error = ${e.message}")
            }
            Thread.sleep(100)
        }

        executeCache.REMOVE("1")

        log.info("[ReUploadNotifyRecordJob] end")
    }

}
