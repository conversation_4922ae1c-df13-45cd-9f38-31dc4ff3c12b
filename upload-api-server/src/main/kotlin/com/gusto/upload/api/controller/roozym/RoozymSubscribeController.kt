package com.gusto.upload.api.controller.roozym

import com.alicp.jetcache.Cache
import com.alicp.jetcache.anno.CreateCache
import com.gusto.upload.core.roozym.RoozymSubscriptionService
import com.gusto.upload.core.swagger.ApiVersion
import com.gusto.upload.core.swagger.UploadVersion
import com.gusto.upload.model.entity.roozym.req.RoozymNotificationReq
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.concurrent.Executor
import javax.annotation.Resource
import javax.servlet.http.HttpServletRequest

/**
 * 如骏-订阅 控制类
 * <AUTHOR>
 * @since 2022/6/1
 */
@Tag(name = "如骏-订阅")
@RestController
@RequestMapping("roozym/subscription")
class RoozymSubscribeController {
    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var subscriptionService: RoozymSubscriptionService

    @Resource(name = "asyncExecutor")
    lateinit var asyncExecutor: Executor

    @CreateCache(name = "RoozymSubscribeController.notifyCache.", expire = 3600 * 24 * 7)
    private lateinit var notifyCache: Cache<String, Long>

    /**
     * 订阅事件通知（忽略）
     */
    @ApiVersion(UploadVersion.ROOZYM)
    @Operation(summary = "订阅事件通知（忽略）")
    @PostMapping("notifications")
    fun notification(@RequestBody req: RoozymNotificationReq, httpRequest: HttpServletRequest): ResponseEntity<Any> {
        val key = "${req.openId}${req.startTime}${req.endTime}${req.sportType}${req.eventType}"
        val cache = notifyCache.GET(key)
        if (cache.isSuccess) {
            return ResponseEntity(true, HttpStatus.OK)
        }
        log.debug("[Roozym:notification] notifications: start, req = $req")
        notifyCache.PUT(key, 0)

        val roozymSign = httpRequest.getHeader("sign")
        if (roozymSign.isNullOrEmpty()) {
            log.error("[Roozym:notification] req = ${req}, roozym sign null or empty")
            return ResponseEntity(true, HttpStatus.OK)
        }

        // 校验签名
        val timestamp = httpRequest.getHeader("timestamp")
        val signResult = subscriptionService.sign(req, timestamp)
        if (signResult == null) {
            log.warn("[Roozym:notification] openId = ${req.openId}, sync error, message = authInfo not found")
            return ResponseEntity(true, HttpStatus.OK)
        }
        if (roozymSign != signResult) {
            log.warn("[Roozym:notification] sign error roozymSign = ${roozymSign}, our sign = ${signResult}")
            return ResponseEntity(true, HttpStatus.OK)
        }
        // 异步处理订阅事件
        asyncExecutor.execute {
            subscriptionService.handler(req)
        }
        return ResponseEntity(true, HttpStatus.OK)
    }
}
