package com.gusto.upload.api.job.garmin

import com.gusto.upload.core.kafka.producer.GarminActivityProducer
import com.gusto.upload.core.service.garmin.GarminUploadNotifyService
import org.apache.shardingsphere.elasticjob.api.ShardingContext
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * 佳明重新上传通知 服务类
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Component
class GarminReUploadNotifyJob : SimpleJob {

    private val log = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var uploadNotifyService: GarminUploadNotifyService

    @Autowired
    lateinit var producer: GarminActivityProducer

    override fun execute(p0: ShardingContext?) {
        log.info("[GarminReUploadNotifyJob] start")

        val notifyList = uploadNotifyService.getListByState(1).sortedBy { it.updateTime }
        notifyList.forEach {
            when (it.source) {
                2 -> producer.sendNotifyActivityMessage(it.buildMessage())
                else -> producer.sendNotifyActivityMessage(it.buildMessage())
            }
            it.state = 3
            uploadNotifyService.updateById(it)
        }

        log.info("[GarminReUploadNotifyJob] end")
    }

}
