docker buildx build -t sport-china-docker.pkg.coding.net/new-gusto/docker/upload-api-server:latest-amd64-dev --platform=linux/amd64 . --push

docker buildx build -t sport-china-docker.pkg.coding.net/new-gusto/docker/upload-api-server:latest-arm64-dev --platform=linux/arm64 . --push

docker manifest create sport-china-docker.pkg.coding.net/new-gusto/docker/upload-api-server:latest-dev sport-china-docker.pkg.coding.net/new-gusto/docker/upload-api-server:latest-amd64-dev sport-china-docker.pkg.coding.net/new-gusto/docker/upload-api-server:latest-arm64-dev

docker manifest push sport-china-docker.pkg.coding.net/new-gusto/docker/upload-api-server:latest-dev

docker manifest rm sport-china-docker.pkg.coding.net/new-gusto/docker/upload-api-server:latest-dev