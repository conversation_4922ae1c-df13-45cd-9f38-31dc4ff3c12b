<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gusto.upload</groupId>
        <artifactId>upload-server</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>upload-api-server</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.gusto.upload</groupId>
            <artifactId>upload-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gusto.framework</groupId>
            <artifactId>gusto-server</artifactId>
            <version>${gusto-framework.version}</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>