FROM sport-china-docker.pkg.coding.net/new-gusto/docker/base:1.0.0
VOLUME /tmp
COPY target/upload-api-server.jar app.jar
ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-Xms512m", "-Xmx1024m", "-Xmn512m", "-XX:MaxDirectMemorySize=256m", "-XX:+UseG1GC", "-XX:+UseCompressedOops", "-XX:+UseCompressedClassPointers", "-XX:MetaspaceSize=64m", "-XX:MaxMetaspaceSize=256m", "-XX:+SegmentedCodeCache", "-verbose:gc", "-XX:+PrintCommandLineFlags", "-XX:+ExplicitGCInvokesConcurrent", "-jar", "/app.jar"]
