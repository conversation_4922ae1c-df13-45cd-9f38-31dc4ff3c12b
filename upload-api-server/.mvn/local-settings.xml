<settings>
    <servers>
        <server>
            <id>sport-china-new-gusto-gusto-framework</id>
            <username>gusto-framework-1650876326885</username>
            <password>48e9c9af8b68c428590b3a722a63bc3af6f763c3</password>
        </server>
    </servers>

    <profiles>
        <profile>
            <id>Repository Proxy</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <repositories>
                <repository>
                    <id>sport-china-new-gusto-gusto-framework</id>
                    <name>gusto-framework</name>
                    <url>https://sport-china-maven.pkg.coding.net/repository/new-gusto/gusto-framework/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
        </profile>
    </profiles>

    <mirrors>
        <mirror>
            <id>nexus-tencentyun</id>
            <mirrorOf>!sport-china-new-gusto-gusto-framework</mirrorOf>
            <name>Nexus tencentyun</name>
            <url>http://mirrors.cloud.tencent.com/nexus/repository/maven-public/</url>
        </mirror>
    </mirrors>
</settings>