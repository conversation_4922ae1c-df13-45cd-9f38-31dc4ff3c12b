<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
        "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
        "http://checkstyle.sourceforge.net/dtds/configuration_1_3.dtd">

<!--
    Checkstyle configuration that checks the gusto coding conventions.

    Checkstyle is very configurable. Be sure to read the documentation at
    http://checkstyle.sf.net (or in your downloaded distribution).
 -->

<module name="Checker">
    <property name='localeCountry' value=''/>
    <property name='localeLanguage' value=''/>
    <property name="charset" value="UTF-8"/>
    <property name="severity" value="error"/>
    <property name="fileExtensions" value="java, properties, xml"/>

    <!-- Checks for whitespace                               -->
    <!-- See http://checkstyle.sf.net/config_whitespace.html -->
    <!--不允许使用 Tab 字符-->
    <module name="FileTabCharacter">
        <property name="eachLine" value="true"/>
    </module>
    <!--一个文件不允许超过 1500 行-->
    <module name="FileLength">
        <property name="max" value="1500"/>
        <property name="fileExtensions" value="java, properties, xml"/>
    </module>

    <module name="LineLength">
        <!--忽略注释-->
        <property name="ignorePattern" value="^ */* *[^ ]+$"/>
        <!--一行代码的长度不允许超过 130 -->
        <property name="max" value="130"/>
        <!--忽略xml和配置文件-->
        <property name="fileExtensions" value="xml, properties"/>
    </module>

    <module name="TreeWalker">
        <!--一个方法不允许超过 60 行-->
        <module name="MethodLength">
            <property name="max" value="60"/>
        </module>
        <!--类型名称必须与文件名匹配-->
        <module name="OuterTypeFilename"/>
        <!--一个文件只能有一个外部类-->
        <module name="OuterTypeNumber"/>
        <!--字符串字面量不允许使用八进制值或者 Unicode 转义值-->
        <module name="IllegalTokenText">
            <property name="tokens" value="STRING_LITERAL, CHAR_LITERAL"/>
            <property name="format"
                      value="\\u00(09|0(a|A)|0(c|C)|0(d|D)|22|27|5(C|c))|\\(0(10|11|12|14|15|42|47)|134)"/>
            <property name="message"
                      value="Consider using special escape sequence instead of octal value or Unicode escaped value."/>
        </module>
        <!--不允许使用 Unicode 码转义字符，除非是控制字符、不可打印字符或者随后跟有注释-->
        <module name="AvoidEscapedUnicodeCharacters">
            <property name="allowEscapesForControlCharacters" value="true"/>
            <property name="allowByTailComment" value="true"/>
            <property name="allowNonPrintableEscapes" value="true"/>
        </module>
        <!--不允许使用星号导入-->
        <module name="AvoidStarImport"/>
        <!--一个文件只能拥有一个顶级类-->
        <module name="OneTopLevelClass"/>
        <!--package, import 语句不可换行-->
        <module name="NoLineWrap"/>
        <!--try, finally, if, else, switch 代码块不可为空，除非有注释说明为空的理由-->
        <module name="EmptyBlock">
            <property name="option" value="TEXT"/>
            <property name="tokens" value="LITERAL_TRY, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE, LITERAL_SWITCH"/>
        </module>
        <!--禁止出现只有一个分号的空语句-->
        <module name="EmptyStatement"/>
        <!--禁止使用嵌套的代码块，否则可能导致变量的作用域混乱-->
        <module name="AvoidNestedBlocks"/>
        <!--代码块必须使用花括号包围，单行语句除外-->
        <module name="NeedBraces">
            <property name="allowSingleLineStatement" value="true"/>
        </module>
        <!--左花括号应在前面的语句的同一行的末尾-->
        <module name="LeftCurly">
            <property name="option" value="eol"/>
        </module>
        <!--try-catch-finally, if-else, do-while 等复合语句的右花括号应与后面的的语句在同一行-->
        <module name="RightCurly">
            <property name="id" value="RightCurlySame"/>
            <property name="tokens"
                      value="LITERAL_TRY, LITERAL_CATCH, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE, LITERAL_DO"/>
        </module>
        <!--其他情况右花括号应单独成一行-->
        <module name="RightCurly">
            <property name="id" value="RightCurlyAlone"/>
            <property name="option" value="alone_or_singleline"/>
            <property name="tokens"
                      value="CLASS_DEF, METHOD_DEF, CTOR_DEF, LITERAL_FOR, LITERAL_WHILE, STATIC_INIT, INSTANCE_INIT"/>
        </module>
        <!--各种操作符，关键字，赋值符号等，左右都应该有一个空格-->
        <module name="WhitespaceAround">
            <property name="allowEmptyConstructors" value="true"/>
            <property name="allowEmptyMethods" value="true"/>
            <property name="allowEmptyTypes" value="true"/>
            <property name="allowEmptyLoops" value="true"/>
            <message key="ws.notFollowed"
                     value="WhitespaceAround: ''{0}'' is not followed by whitespace. Empty blocks may only be represented as '{}' when not part of a multi-block statement (4.1.3)"/>
            <message key="ws.notPreceded"
                     value="WhitespaceAround: ''{0}'' is not preceded with whitespace."/>
        </module>
        <module name="WhitespaceAfter">
            <property name="tokens"
                      value="COMMA, SEMI, TYPECAST, LITERAL_IF, LITERAL_ELSE, LITERAL_WHILE, LITERAL_DO, LITERAL_FOR, DO_WHILE"/>
        </module>
        <module name="NoWhitespaceAfter">
            <property name="id" value="NoWhitespaceAfterNoLineBreaks"/>
            <property name="tokens"
                      value="INC, DEC, UNARY_MINUS, UNARY_PLUS, BNOT, LNOT, DOT, ARRAY_DECLARATOR, INDEX_OP, METHOD_REF"/>
            <property name="allowLineBreaks" value="false"/>
        </module>
        <module name="NoWhitespaceAfter">
            <property name="id" value="NoWhitespaceAfterAllowLineBreaks"/>
            <property name="tokens" value="ARRAY_INIT"/>
            <property name="allowLineBreaks" value="true"/>
        </module>
        <module name="NoWhitespaceBefore">
            <property name="id" value="NoWhitespaceBeforeNoLineBreaks"/>
            <property name="tokens" value="COMMA, SEMI, POST_INC, POST_DEC, GENERIC_END, ELLIPSIS, METHOD_REF"/>
            <property name="allowLineBreaks" value="false"/>
        </module>
        <module name="NoWhitespaceBefore">
            <property name="id" value="NoWhitespaceBeforeAllowLineBreaks"/>
            <property name="tokens" value="DOT"/>
            <property name="allowLineBreaks" value="true"/>
        </module>
        <!--每行只能有一条语句-->
        <module name="OneStatementPerLine"/>
        <!--每条语句只能声明一个变量-->
        <module name="MultipleVariableDeclarations"/>
        <!--数组定义的方括号应在类型后面（Java 风格），而不是变量名后面（C 语言风格）-->
        <module name="ArrayTypeStyle"/>
        <!--switch-case 语句必须要有 default case，就算你认为不可能，也应该抛出一个 AssertionError，而不是不写-->
        <module name="MissingSwitchDefault"/>
        <!--switch-case 的 default 分支必须写在最后-->
        <module name="DefaultComesLast"/>
        <!--不要在 switch-case 语句中使用穿透，每一个 case 都应该 break，如果要穿透到下一个 case，必须加上 // fall through 这样的注释-->
        <module name="FallThrough"/>
        <!--long 字面量要使用大写 L 结尾，禁止使用小写 l-->
        <module name="UpperEll"/>
        <!--组合使用多个修饰符时，要按照规定的规则排序，顺序为 public protected private abstract default static final transient volatile synchronized native strictfp-->
        <module name="ModifierOrder"/>
        <!--检查多余的修饰符-->
        <module name="RedundantModifier"/>
        <!--import 语句，类定义，方法定义，初始化块，字段定义等的后面要有空行分隔-->
        <module name="EmptyLineSeparator">
            <property name="allowNoEmptyLineBetweenFields" value="true"/>
        </module>
        <!--一条语句写成多行时，可在点号前面换行-->
        <module name="SeparatorWrap">
            <property name="id" value="SeparatorWrapDot"/>
            <property name="tokens" value="DOT"/>
            <property name="option" value="nl"/>
        </module>
        <!--一条语句写成多行时，可在逗号后面换行-->
        <module name="SeparatorWrap">
            <property name="id" value="SeparatorWrapComma"/>
            <property name="tokens" value="COMMA"/>
            <property name="option" value="EOL"/>
        </module>
        <!--一条语句写成多行时，可在三点号（varargs）后面换行-->
        <module name="SeparatorWrap">
            <!-- ELLIPSIS is EOL until https://github.com/google/styleguide/issues/258 -->
            <property name="id" value="SeparatorWrapEllipsis"/>
            <property name="tokens" value="ELLIPSIS"/>
            <property name="option" value="EOL"/>
        </module>
        <!--一条语句写成多行时，数组声明应写完整，可在后面换行-->
        <module name="SeparatorWrap">
            <!-- ARRAY_DECLARATOR is EOL until https://github.com/google/styleguide/issues/259 -->
            <property name="id" value="SeparatorWrapArrayDeclarator"/>
            <property name="tokens" value="ARRAY_DECLARATOR"/>
            <property name="option" value="EOL"/>
        </module>
        <!--一条语句写成多行时，可在方法引用的双冒号的前面换行-->
        <module name="SeparatorWrap">
            <property name="id" value="SeparatorWrapMethodRef"/>
            <property name="tokens" value="METHOD_REF"/>
            <property name="option" value="nl"/>
        </module>
        <!--包名必须由纯小写英文字母和数组组成，并且不能以数组开头-->
        <module name="PackageName">
            <property name="format" value="^[a-z]+(\.[a-z][a-z0-9]*)*$"/>
            <message key="name.invalidPattern"
                     value="Package name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <!--类型名称必须使用大写字母开头的驼峰命名法-->
        <module name="TypeName">
            <message key="name.invalidPattern"
                     value="Type name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <!--成员名称必须使用小写字母开头的驼峰命名法-->
        <module name="MemberName">
            <property name="format" value="^[a-z][a-z0-9][a-zA-Z0-9]*$"/>
            <message key="name.invalidPattern"
                     value="Member name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <!--参数名称、局部变量名称使用小写字母开头的驼峰命名法-->
        <module name="ParameterName">
            <property name="format" value="^[a-z]([a-z0-9][a-zA-Z0-9]*)?$"/>
            <message key="name.invalidPattern"
                     value="Parameter name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <module name="CatchParameterName">
            <property name="format" value="^e$|^ex$|^expected$|^ignored$"/>
            <message key="name.invalidPattern"
                     value="Catch parameter name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <module name="LocalVariableName">
            <property name="tokens" value="VARIABLE_DEF"/>
            <property name="format" value="^[a-z]([a-z0-9][a-zA-Z0-9]*)?$"/>
            <message key="name.invalidPattern"
                     value="Local variable name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <!--类型参数使用形如 E, T, X, T2, 或者 RequestT, FooBarT 这样的命名-->
        <module name="ClassTypeParameterName">
            <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
            <message key="name.invalidPattern"
                     value="Class type name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <module name="MethodTypeParameterName">
            <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
            <message key="name.invalidPattern"
                     value="Method type name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <module name="InterfaceTypeParameterName">
            <property name="format" value="(^[A-Z][0-9]?)$|([A-Z][a-zA-Z0-9]*[T]$)"/>
            <message key="name.invalidPattern"
                     value="Interface type name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <!--不使用 finalize() 方法-->
        <module name="NoFinalizer"/>
        <module name="GenericWhitespace">
            <message key="ws.followed"
                     value="GenericWhitespace ''{0}'' is followed by whitespace."/>
            <message key="ws.preceded"
                     value="GenericWhitespace ''{0}'' is preceded with whitespace."/>
            <message key="ws.illegalFollow"
                     value="GenericWhitespace ''{0}'' should followed by whitespace."/>
            <message key="ws.notPreceded"
                     value="GenericWhitespace ''{0}'' is not preceded with whitespace."/>
        </module>
        <!--使用 4 空格缩进，当一条语句写成多行时，下一行应使用 8 空格缩进-->
        <module name="Indentation">
            <property name="basicOffset" value="4"/>
            <property name="braceAdjustment" value="0"/>
            <property name="caseIndent" value="4"/>
            <property name="throwsIndent" value="8"/>
            <property name="arrayInitIndent" value="4"/>
            <property name="lineWrappingIndentation" value="8"/>
        </module>
        <!--缩写检查-->
        <module name="AbbreviationAsWordInName">
            <property name="ignoreFinal" value="true"/>
            <property name="ignoreStatic" value="true"/>
            <property name="allowedAbbreviationLength" value="2"/>
            <property name="allowedAbbreviations" value="DO, DTO"/>
        </module>
        <!--重载的方法要写在一起-->
        <module name="OverloadMethodsDeclarationOrder"/>
        <!--检查变量的声明和使用之处是否距离太远，http://checkstyle.sourceforge.net/config_coding.html#VariableDeclarationUsageDistance-->
        <module name="VariableDeclarationUsageDistance">
            <property name="allowedDistance" value="6"/>
        </module>
        <!--不要出现没用的 import 语句-->
        <module name="UnusedImports"/>
        <!--定义/调用方法时，左圆括号应与方法名在同一行，并且括号左右不空格-->
        <module name="MethodParamPad"/>
        <!--左圆括号之后，右圆括号之前不需要空格-->
        <module name="ParenPad"/>
        <!--一条语句写成多行时，应在操作符之前换行-->
        <module name="OperatorWrap">
            <property name="option" value="NL"/>
            <property name="tokens"
                      value="BAND, BOR, BSR, BXOR, DIV, EQUAL, GE, GT, LAND, LE, LITERAL_INSTANCEOF, LOR, LT, MINUS, MOD, NOT_EQUAL, PLUS, QUESTION, SL, SR, STAR, METHOD_REF "/>
        </module>
        <!--类定义、方法定义处的注解应写在上面，每个注解写一行-->
        <module name="AnnotationLocation">
            <property name="id" value="AnnotationLocationMostCases"/>
            <property name="tokens" value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF"/>
        </module>
        <!--变量定义的注解可以与该定义写在同一行-->
        <module name="AnnotationLocation">
            <property name="id" value="AnnotationLocationVariables"/>
            <property name="tokens" value="VARIABLE_DEF"/>
            <property name="allowSamelineMultipleAnnotations" value="true"/>
        </module>
        <!--注解的使用风格-->
        <module name="AnnotationUseStyle">
            <property name="elementStyle" value="compact_no_array"/>
            <property name="trailingArrayComma" value="never"/>
            <property name="closingParens" value="never"/>
        </module>
        <!--匿名内部类的代码不能超过 30 行，太长的应该重构为成员内部类或静态内部类-->
        <module name="AnonInnerLength">
            <property name="max" value="30"/>
        </module>
        <!--重写方法必须使用 @Override 注解-->
        <module name="MissingOverride"/>
        <!--@param @return 等 Javadoc 标注后面必须要有相应的描述，不想写描述的话，直接把整个标签行删除更好-->
        <module name="NonEmptyAtclauseDescription"/>
        <!--定义 Javadoc 标签的出现顺序-->
        <module name="AtclauseOrder">
            <property name="tagOrder" value="@param, @return, @throws, @deprecated"/>
            <property name="target" value="CLASS_DEF, INTERFACE_DEF, ENUM_DEF, METHOD_DEF, CTOR_DEF, VARIABLE_DEF"/>
        </module>
        <!--Javadoc 相关规定-->
        <module name="JavadocMethod">
            <!--所有 public 的类，以及 public 类里面的 public/protected 成员都必须有文档注释-->
            <property name="scope" value="protected"/>
            <!--只针对方法和注解的字段进行检查-->
            <property name="tokens" value="METHOD_DEF, ANNOTATION_FIELD_DEF"/>
            <!--可以不写 @return-->
            <property name="allowMissingReturnTag" value="true"/>
            <!--可以不写 @param-->
            <property name="allowMissingParamTags" value="true"/>
        </module>
        <module name="MissingJavadocMethod">
            <!--最少写一行文档注释-->
            <property name="minLineCount" value="1"/>
            <!--属性的 getter 和 setter 方法可以不写文档注释-->
            <property name="allowMissingPropertyJavadoc" value="true"/>
            <!--main 方法不需要写注释，以 of, to 开头的方法一般来说仅做数据转换，也可以不写注释-->
            <property name="ignoreMethodNamesRegex" value="^main$|^of.*$|^to.*$"/>
            <!--使用了 @Override, @Test, @Bean, @MessageMapping 注解的方法可以不写文档注释-->
            <property name="allowedAnnotations" value="Override, Test, Bean, MessageMapping"/>
        </module>
        <!--方法命名规范-->
        <module name="MethodName">
            <property name="format" value="^[a-z][a-z0-9][a-zA-Z0-9_]*$"/>
            <message key="name.invalidPattern"
                     value="Method name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <!--禁止出现空的 catch 代码块，除非你把这个忽略的异常命名为 expected 或者 ignored 表示你是有意忽略该异常，最好还要有注释说明-->
        <module name="EmptyCatchBlock">
            <property name="exceptionVariableName" value="expected|ignored"/>
        </module>
        <!--检查注释的缩进-->
        <module name="CommentsIndentation"/>
        <!--最多嵌套 2 层 for 循环-->
        <module name="NestedForDepth">
            <property name="max" value="1"/>
        </module>
        <!--最多嵌套 3 层 if 语句-->
        <module name="NestedIfDepth">
            <property name="max" value="2"/>
        </module>
        <!--最多嵌套 2 层 try-catch-->
        <module name="NestedTryDepth">
            <property name="max" value="1"/>
        </module>
        <!--简化布尔表达式-->
        <module name="SimplifyBooleanExpression"/>
        <!--简化布尔返回值-->
        <module name="SimplifyBooleanReturn"/>
        <!--禁止使用 == 和 != 比较字符串-->
        <module name="StringLiteralEquality"/>
        <!--异常必须是不可变的，所有字段使用 final -->
        <module name="MutableException"/>
        <!--类的成员字段必须是 private 的，如需要开放访问权限，使用 lombok 的 @Getter 或 @Setter 注解-->
        <module name="VisibilityModifier">
            <property name="protectedAllowed" value="true"/>
        </module>
        <!--如果定义了类似 public boolean equals(Foo f) 的方法，必须重写 Object 类中的 equals 方法，因为以 Foo 为参数的方法并没有重写成功-->
        <module name="CovariantEquals"/>
        <!--重写 equals 方法时，必须同时重写 hashCode 方法-->
        <module name="EqualsHashCode"/>
        <!--类中的成员必须按静态字段、实例字段、构造方法、普通方法的顺序声明，并且字段的声明顺序必须以可见性从大到小排序-->
        <module name="DeclarationOrder"/>
        <!--如果一个类只有 private 构造函数，必须声明为 final-->
        <!-- <module name="FinalClass"/> -->
        <!--赋值语句必须独立为一行，禁止类似 Integer.toString(i = 2) 的写法，当然像 while ((line = br.readLine()) != null) 这种常见模式是可以的-->
        <module name="InnerAssignment"/>
        <!--鉴于有些同学还是不理解 Java 方法调用时只有值传递，没有引用传递，故禁止对参数赋值-->
        <module name="ParameterAssignment"/>
        <!--方法参数不能超过 5 个，超过时，请进行重构-->
        <module name="ParameterNumber">
            <property name="max" value="5"/>
            <property name="ignoreOverriddenMethods" value="true"/>
        </module>
        <!--不允许使用 printStackTrace 打印错误信息，必须使用日志记录-->
        <module name="RegexpSinglelineJava">
            <property name="format" value="printStackTrace"/>
            <property name="message" value="DO NOT use printStackTrace, use a logger instead."/>
        </module>
        <!--不允许再使用旧的时间日期 api，请使用 java.time 包的新 api-->
        <module name="IllegalType">
            <property name="illegalClassNames" value="java.util.Date, java.util.Calendar, java.text.SimpleDateFormat"/>
        </module>
        <module name="IllegalInstantiation">
            <property name="classes" value="java.util.Date, java.util.Calendar, java.text.SimpleDateFormat"/>
        </module>
        <!--可以在一些特殊的地方使用 @SuppressWarnings 注解禁用 checkstyle 的某些检查, see SuppressWarningsFilter-->
        <module name="SuppressWarningsHolder"/>
    </module>
    <!--use SuppressWarningsHolder to enable @SuppressWarnings annotation-->
    <module name="SuppressWarningsFilter"/>

    <!--每行字符数量不能超过 140，package, import, url 等除外 -->
    <module name="LineLength">
        <property name="max" value="140"/>
        <property name="ignorePattern"
                  value="^package.*|^import.*|a href|href|http://|https://|ftp://|execution\(.*\)|static final String"/>
    </module>

    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern"
                  value="UtilsImpl.java$|AbstractException.java|StpBizUtil.java|MysqlCodeGenerator.java"/>
    </module>
</module>
