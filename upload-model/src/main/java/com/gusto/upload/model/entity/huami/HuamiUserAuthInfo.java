package com.gusto.upload.model.entity.huami;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.upload.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * 华米-用户授权信息
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("upload_user_auth_info_huami")
public class HuamiUserAuthInfo extends BaseEntity {

    private static final long serialVersionUID = -3704555668555160193L;

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long recordId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "RefreshToken，有效期10年")
    private String refreshToken;

    @Schema(description = "AccessToken，有效期90天")
    private String accessToken;

    @Schema(description = "AccessToken更新时间")
    private Instant accessTokenUpdateTime;

    @Schema(description = "华米用户ID")
    private String openId;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickname;

}
