package com.gusto.upload.model.entity.oppo.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
public class OppoGetActivityDetailOtherGpsRsp {

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "时间戳，单位毫秒")
    private Long timestamp;

    @Schema(description = "点类型：0-正常点 1-暂停点")
    private Integer type;

}
