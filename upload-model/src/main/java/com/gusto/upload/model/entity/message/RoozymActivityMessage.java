package com.gusto.upload.model.entity.message;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/6/1
 */
@Data
public class RoozymActivityMessage {
    public static final String SUBSCRIPTION_TOPIC = "RoozymActivityMessage_Subscription_Topic_1";

    /**
     * 活动开始时间
     */
    private String startTime;

    /**
     * 活动结束时间
     */
    private String endTime;

    /**
     * 活动的运动类型
     */
    private Integer activityType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 第三方标识
     */
    private String openId;

    /**
     * 事件类型
     */
    private Integer eventType;
}
