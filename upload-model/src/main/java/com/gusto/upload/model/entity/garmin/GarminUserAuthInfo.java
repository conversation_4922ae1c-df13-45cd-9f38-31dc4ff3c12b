package com.gusto.upload.model.entity.garmin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.upload.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 佳明-用户授权信息
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("upload_user_auth_info_garmin")
public class GarminUserAuthInfo extends BaseEntity {

    private static final long serialVersionUID = 1149245202106386199L;

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long recordId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "RequestToken")
    private String requestToken;

    @Schema(description = "RequestTokenSecret")
    private String requestTokenSecret;

    @Schema(description = "验证码")
    private String oauthVerifier;

    @Schema(description = "AccessToken")
    private String accessToken;

    @Schema(description = "AccessTokenSecret")
    private String accessTokenSecret;

    @Schema(description = "佳明用户ID")
    private String garminUserId;

    @Schema(description = "来源：1-中国大陆 2-海外")
    private Integer source;

}
