package com.gusto.upload.model.entity.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.upload.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 新用户跑步记录上传通知
 *
 * <AUTHOR>
 * @since 2023-02-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "app_run_upload_notify", autoResultMap = true)
public class NewUserRunUploadNotify extends BaseEntity {

    private static final long serialVersionUID = 6249209386746462193L;

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long notifyId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "跑步记录ID")
    private Long runRecordId;

    @Schema(description = "状态：0-默认 1-失败 2-成功")
    private Integer state;

}
