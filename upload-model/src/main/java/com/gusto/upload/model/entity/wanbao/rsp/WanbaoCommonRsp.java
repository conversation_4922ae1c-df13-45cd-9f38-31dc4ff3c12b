package com.gusto.upload.model.entity.wanbao.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-09-12
 */
@Data
public class WanbaoCommonRsp<T> {

    @Schema(description = "响应编码，下发成功状态下为200")
    private Integer code;

    @Schema(description = "成功标识，成功状态下为true")
    private Boolean success;

    @Schema(description = "数据，该字段未使用，未赋值")
    private T data;

    @Schema(description = "接口调用信息，例如：操作成功/失败等")
    private String msg;

}
