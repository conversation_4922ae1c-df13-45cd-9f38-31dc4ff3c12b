package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 华为-订阅事件
 *
 * <AUTHOR>
 * @since 2022-09-27
 */
@Data
public class HuaweiEventType {

    @Schema(description = "订阅事件类别")
    private String type;

    @Schema(description = "订阅事件子类别")
    private String subType;

    /**
     * 由type与subType按$连接
     * 详见：https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/subscription-0000001078496860#section13879857154018
     */
    @Schema(description = "订阅事件类型ID")
    private String eventType;

}
