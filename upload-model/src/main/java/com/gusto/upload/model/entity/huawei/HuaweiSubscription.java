package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 华为-订阅信息
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@Data

public class HuaweiSubscription {
    @Schema(description = "开发者应用ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String appId;

    @Schema(description = "关注/订阅的事件，由事件类别和子类别拼接而成", requiredMode = Schema.RequiredMode.REQUIRED)
    private String eventType;

    @Schema(description = "订阅者ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subscriberId;

    @Schema(description = "订阅记录ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subscriptionId;

    @Schema(description = "用户-appId，组合得到的唯一标识openId", requiredMode = Schema.RequiredMode.REQUIRED)
    private String openId;
}
