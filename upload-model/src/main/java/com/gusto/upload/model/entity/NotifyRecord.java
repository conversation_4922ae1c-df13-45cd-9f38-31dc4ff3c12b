package com.gusto.upload.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 第三方开放平台通知
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("upload_notify_record")
public class NotifyRecord extends BaseEntity {

    private static final long serialVersionUID = 6435923704789836078L;

    public static final String UNIQUE_KEY_FIELD = "unique_key";

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long recordId;

    @Schema(description = "唯一键")
    private String uniqueKey;

    @Schema(description = "设备ID")
    private Long deviceId;

}
