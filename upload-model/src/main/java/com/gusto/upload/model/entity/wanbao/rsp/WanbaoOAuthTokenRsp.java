package com.gusto.upload.model.entity.wanbao.rsp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-09-12
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WanbaoOAuthTokenRsp {

    @Schema(description = "身份令牌")
    private String accessToken;

    @Schema(description = "令牌类型", example = "bearer")
    private String tokenType;

    @Schema(description = "更新令牌，用来获取下一次的访问令牌")
    private String refreshToken;

    @Schema(description = "令牌过期时间(秒)")
    private Integer expiresIn;

    @Schema(description = "授权范围")
    private String scope;

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "用户真实名")
    private String realName;

    @Schema(description = "头像地址")
    private String avatar;

    @Schema(description = "客户端id")
    private String clientId;

    @Schema(description = "角色名")
    private String roleName;

    @Schema(description = "许可证")
    private String license;

    @Schema(description = "岗位id")
    private String postId;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "角色id")
    private String roleId;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "第三方认证id")
    private String oauthId;

    @Schema(description = "部门id")
    private String deptId;

    @Schema(description = "详情")
    private String detail;

    @Schema(description = "账号")
    private String account;

    @Schema(description = "jwt的唯一身份标识")
    private String jti;

}
