package com.gusto.upload.model.entity.honor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class HonorSummaryEntity {

    @Schema(description = "运动总时长(秒)")
    private Integer totalTime;

    @Schema(description = "活跃时长")
    private Integer activityTime;

    @Schema(description = "累计下降高度(分米)")
    private Integer totalDropHeight;

    @Schema(description = "累计爬升高度(分米)")
    private Integer totalRiseHeight;

    @Schema(description = "最高海拔")
    private Integer highestAltitude;

    @Schema(description = "最低海拔")
    private Integer lowestAltitude;

    @Schema(description = "平均功率")
    private Integer avgPower;

    @Schema(description = "运动负荷")
    private Integer sportsLoad;

    @Schema(description = "平均踏频(次/分钟)")
    private Integer avgCadence;

    @Schema(description = "最大踏频")
    private Integer maxCadence;

    @Schema(description = "跳绳绊绳次数")
    private Integer tripped;

    @Schema(description = "跳绳最多连跳次数")
    private Integer longestStreak;

}
