package com.gusto.upload.model.entity.wanbao.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-09-18
 */
@Data
public class WanbaoGetActivityDetailByFileRsp implements Serializable {

    private static final long serialVersionUID = -5459319792798339210L;

    @Schema(description = "运动数据集合")
    private WanbaoGetActivityRsp sportCount;

    @Schema(description = "运动详情集合")
    private List<WanbaoGetActivityDetailPointRsp> detailList;

    @Schema(description = "运动计圈集合")
    private List<WanbaoGetActivityDetailCycleRsp> sportCircleList;

}
