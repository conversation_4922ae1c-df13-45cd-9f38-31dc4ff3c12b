package com.gusto.upload.model.entity.roozym.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/9
 */
@Data

public class RoozymUpsertSubscriptionReq {
    @Schema(description = "用户标识id")
    private String openId;

    @Schema(description = "订阅事件集合(打开操作时可以传值，关闭时忽略)")
    private List<String> eventType;

    @Schema(description = "操作类型（1打开2关闭）")
    private Integer status;
}
