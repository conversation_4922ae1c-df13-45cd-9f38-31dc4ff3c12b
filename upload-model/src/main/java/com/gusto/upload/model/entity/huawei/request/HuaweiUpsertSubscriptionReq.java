package com.gusto.upload.model.entity.huawei.request;

import com.gusto.upload.model.entity.huawei.HuaweiEventType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 华为-新增/更新订阅请求
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@Data
public class HuaweiUpsertSubscriptionReq {

    /**
     * 关注/订阅的事件列表
     * 详见：https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/subscription-0000001078496860#section13879857154018
     */
    @Schema(description = "关注/订阅的事件列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<HuaweiEventType> eventTypes;

    @Schema(description = "订阅者ID")
    private String subscriberId;

}
