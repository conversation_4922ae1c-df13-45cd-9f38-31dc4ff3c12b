package com.gusto.upload.model.entity.huami;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 华米-运动记录
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HuamiActivity {

    @Schema(description = "轨迹ID")
    private String trackId;

    @Schema(description = "开始时间时间戳，单位秒")
    private Long startTime;

    @Schema(description = "结束时间时间戳，单位秒")
    private Long endTime;

    @Schema(description = "运动时间，单位：秒")
    private Integer sportTime;

    @Schema(description = "运动距离，单位：米")
    private Integer distance;

    @Schema(description = "运动的第一个GPS点的geohash")
    private String location;

    @Schema(description = "卡路里，单位：千卡")
    private Integer calories;

    @Schema(description = "平均配速，单位：秒/米")
    private Double averagePace;

    @Schema(description = "平均心率")
    private Integer averageHeartRate;

    @Schema(description = "平均步频，单位：步/分钟")
    private Integer averageStepFrequency;

    @Schema(description = "平均步幅，单位：厘米")
    private Integer averageStrideLength;

    @Schema(description = "运动期间海拔上升，单位：米")
    private Integer altitudeAscend;

    @Schema(description = "运动期间海拔下降，单位：米")
    private Integer altitudeDescend;

    @Schema(description = "下半场开始时间，仅在运动类型为足球时提供")
    private Long secondHalfStartTime;

    @Schema(description = "击球次数，仅在运动类型为网球时提供")
    private Integer strokeCount;

    @Schema(description = "正手次数，仅在运动类型为网球时提供")
    private Integer foreHandCount;

    @Schema(description = "反手次数，仅在运动类型为网球时提供")
    private Integer backHandCount;

    @Schema(description = "发球次数，仅在运动类型为网球时使用")
    private Integer serveCount;

    @Schema(description = "运动类型")
    private String type;

    @Schema(description = "设备类型")
    private String device;

    @Schema(description = "多语言设备名称，目前支持zh-CN和en-WW")
    private String deviceName;

    @Schema(description = "时间戳")
    private Long timestamp;

    @Schema(description = "运动步数")
    private Integer totalStep;

}
