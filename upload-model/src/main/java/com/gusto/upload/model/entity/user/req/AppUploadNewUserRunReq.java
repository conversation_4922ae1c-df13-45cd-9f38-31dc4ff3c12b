package com.gusto.upload.model.entity.user.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.gusto.upload.model.entity.user.dto.UserRunAdcodeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * APP 上传新跑步记录请求
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppUploadNewUserRunReq {

    @Schema(description = "版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "设备ID：1-APP 跑步 2-手动录入 3-手表 4-手环 5-跑步机，安卓不用传", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer deviceId = 1;

    @Schema(description = "设备类型：201-APP 录入 202-小程序 203-后台 301-佳明 302-宜准 303-高驰 304-华为 305-苹果 306-如骏，安卓不用传", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer deviceType = 0;

    @Schema(description = "设备名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deviceModel;

    @Schema(description = "标识符，运动开始时间的毫秒时间戳，特定用户拥有的所有运动记录中都是唯一的", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long activityId;

    @Schema(description = "运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer activityType;

    @Schema(description = "开始时间，单位毫秒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long startTime;

    @Schema(description = "结束时间，单位毫秒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long endTime;

    @Schema(description = "总距离，单位米", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double totalDistance;

    @Schema(description = "总时长，单位秒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long totalDuration;

    @Schema(description = "总步数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer totalStep;

    @Schema(description = "总卡路里，单位千卡", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double totalCalorie;

    @Schema(description = "平均配速，单位秒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double averagePace;

    @Schema(description = "最快配速，单位秒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double maxPace;

    @Schema(description = "平均步频，单位步/分钟", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer averageStepRate;

    @Schema(description = "最快步频，单位步/分钟", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer maxStepRate;

    @Schema(description = "平均步幅，单位厘米", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double averageStride;

    @Schema(description = "最低海拔，单位米", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double minAltitude;

    @Schema(description = "最高海拔，单位米", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double maxAltitude;

    @Schema(description = "累计上升，单位米", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double totalAscent;

    @Schema(description = "累计下降，单位米", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double totalDescent;

    @Schema(description = "天气", requiredMode = Schema.RequiredMode.REQUIRED)
    private String weather;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    private String city;

    @Schema(description = "实时气温，单位：摄氏度", required = true)
    private String temperature;

    @Schema(description = "空气湿度", required = true)
    private String humidity;

    @Schema(description = "分段千米时间列表，<千米，累计时间/秒>，每一千米记录一次，半马，全马额外记录一次", required = true)
    private Map<String, Integer> partTimeKmList;

    @Schema(description = "第三方活动类型，安卓不用传", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String thirdActivityType = "";

    @Schema(description = "第三方活动记录ID，安卓不用传", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String thirdActivityId = "";

    @Schema(description = "带地图轨迹图", required = true)
    private String mapTrackImage;

    @Schema(description = "轨迹图", required = true)
    private String trackImage;

    @Schema(description = "平均心率", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer averageHeartRate = 0;

    @Schema(description = "最大心率，安卓不用传", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer maxHeartRate = 0;

    @Schema(description = "最小心率，安卓不用传", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer minHeartRate = 0;

    @Schema(description = "原始总步数", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer rawTotalStep = 0;

    @Schema(description = "线上赛活动需要的adcode")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<UserRunAdcodeDTO> adCodeList = Collections.emptyList();

}
