package com.gusto.upload.model.entity.honor.rsp;

import com.gusto.upload.model.entity.honor.HonorActivityRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data

public class HonorGetActivityRecordListRsp {

    @Schema(description = "已授权权限列表")
    private List<String> scopes;

    @Schema(description = "运动记录列表")
    private List<HonorActivityRecord> sportRecord;

}
