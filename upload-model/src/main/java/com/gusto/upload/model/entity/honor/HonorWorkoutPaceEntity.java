package com.gusto.upload.model.entity.honor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class HonorWorkoutPaceEntity {

    @Schema(description = "配速ID = paceIndex + recordTime")
    private String paceId;

    @Schema(description = "运动记录ID = startTime(秒级戳) + deviceId")
    private String recordId;

    @Schema(description = "当前配速信息索引值")
    private Integer paceIndex;

    @Schema(description = "当前配速信息的分段列表")
    private List<HonorPaceData> paceDataList;

}
