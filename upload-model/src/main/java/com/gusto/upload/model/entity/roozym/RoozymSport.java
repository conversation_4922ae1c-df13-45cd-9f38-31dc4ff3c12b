package com.gusto.upload.model.entity.roozym;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/5/26
 */
@Data

public class RoozymSport {
    @Schema(description = "用户标识id")
    private String openId;

    @Schema(description = "开始时间(格式：yyyy-MM-dd HH:mm:ss)")
    private String startDate;

    @Schema(description = "结束时间(格式：yyyy-MM-dd HH:mm:ss)")
    private String endDate;

    @Schema(description = "运动类型（0 徒步 1 跑步 2 骑行 3 登山 4 室内游泳 5 室外游泳 6 铁人三项 8 健走 9 徒步越野 10 室内跑 11 12 马拉松 13 团队训练 14 ai训练 999 所有运动）")
    private String watchId;

    @Schema(description = "同步时间(格式：yyyy-MM-dd HH:mm:ss)")
    private String synDate;

    @Schema(description = "运动时长（单位：秒）")
    private Long actTime;

    @Schema(description = "运动时间(格式：yyyy-MM-dd)")
    private String activityDate;

    @Schema(description = "有氧效果")
    private Integer aerobic;

    @Schema(description = "无氧效果")
    private Integer anaerobic;

    @Schema(description = "平均步频（单位cm）")
    private Integer avgFrequency;

    @Schema(description = "平均心率")
    private Integer avgHeart;

    @Schema(description = "平均速度（骑行 单位：km/h 非骑行 m/s）")
    private Integer avgSpeed;

    @Schema(description = "平均步幅（单位cm）")
    private Integer avgStride;

    @Schema(description = "卡路里（单位：小卡 cal）")
    private Integer calories;

    @Schema(description = "运动地点（格式： 广东.广州）")
    private String city;

    @Schema(description = "距离（单位cm）")
    private Long distance;

    @Schema(description = "下降高度（单位cm）")
    private Integer downHeight;

    @Schema(description = "是否手动添加（0 否 1 是）")
    private Integer handAdd;

    @Schema(description = "心率恢复率")
    private Integer heartRateRecovery;

    @Schema(description = "计圈距离（单位：米）")
    private Integer lapsDistance;

    @Schema(description = "最大步频（单位cm）")
    private Integer maxFrequency;

    @Schema(description = "最大心率")
    private Integer maxHeart;

    @Schema(description = "最大高度（单位cm）")
    private Float maxHeight;

    @Schema(description = "最大气压")
    private Integer maxPressure;

    @Schema(description = "最大速度（单位米/s）")
    private Integer maxSpeed;

    @Schema(description = "最大步幅（单位cm）")
    private Integer maxStride;

    @Schema(description = "最大游泳效率")
    private Integer maxStroke;

    @Schema(description = "最大效率")
    private Integer maxSwolf;

    @Schema(description = "最高温度")
    private Integer maxTemperature;

    @Schema(description = "最小高度（单位cm）")
    private Float minHeight;

    @Schema(description = "最小气压")
    private Integer minPressure;

    @Schema(description = "最小气温")
    private Integer minTemperature;

    @Schema(description = "气压")
    private Integer pressure;

    @Schema(description = "恢复时间（单位秒）")
    private Integer recoveryTime;

    @Schema(description = "运动类型")
    private Integer sportType;

    @Schema(description = "步数")
    private Integer step;

    @Schema(description = "游泳划数")
    private Integer stroke;

    @Schema(description = "主观评价")
    private Integer subjectiveEvaluation;

    @Schema(description = "游泳效率")
    private Integer swolf;

    @Schema(description = "温度")
    private Integer temperature;

    @Schema(description = "上升高度（单位cm）")
    private Integer upHeight;

    @Schema(description = "摄氧量")
    private Integer vo2max;

    @Schema(description = "气压数据（逗号分隔，数据间隔时间 单位 分钟）")
    private String pressureData;

    @Schema(description = "速度数据（逗号分隔，数据间隔时间 单位 分钟）")
    private String speedData;

    @Schema(description = "划数数据（逗号分隔，数据间隔时间 单位 分钟）")
    private String strokeData;

    @Schema(description = "步数数据（逗号分隔，数据间隔时间 单位 分钟）")
    private String stepData;

    @Schema(description = "游泳效率数据（逗号分隔，数据间隔时间 单位 分钟）")
    private String swolfData;

    @Schema(description = "计圈gps数据（逗号分隔）")
    private String lapsGpsData;

    @Schema(description = "卡路里数据（逗号分隔，数据间隔时间 单位 分钟）")
    private String calorieData;

    @Schema(description = "距离数据（逗号分隔，数据间隔时间 单位 分钟）")
    private String distanceData;

    @Schema(description = "gps数据（逗号分隔）")
    private String gpsData;

    @Schema(description = "心率数据（逗号分隔，数据间隔时间 单位 秒）")
    private String heartrateData;

    @Schema(description = "心率区间数据（逗号分隔，数据间隔时间 单位 秒）")
    private String heartrateRangeData;

    @Schema(description = "高度数据（逗号分隔，数据间隔时间 单位 分钟）")
    private String heightData;

    @Schema(description = "计圈数据（逗号分隔，数据间隔时间 单位 秒）")
    private String lapsData;

    @Schema(description = "运动ID")
    private String sportId;
}
