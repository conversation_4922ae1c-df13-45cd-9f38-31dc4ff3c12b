package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * 华为-配速信息
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Data

public class HuaweiPaceSummary {
    @Schema(description = "平均配速，单位是秒/公里", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double avgPace;

    @Schema(description = "最佳配速，单位是秒/公里", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double bestPace;

    @Schema(description = "每公里配速表，单位是秒/公里。最后不满一公里的部分，按比例换算为整公里的配速存入")
    private Map<String, Double> paceMap;

    @Schema(description = "分段累计时间表。公制分段数据表（key：公里，value：秒），其中英里保留到小数点后4位，Value是一个累积到当前公里的时间")
    private Map<String, Double> partTimeMap;
}
