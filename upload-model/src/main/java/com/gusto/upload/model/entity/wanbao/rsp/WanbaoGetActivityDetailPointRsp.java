package com.gusto.upload.model.entity.wanbao.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-09-18
 */
@Data
public class WanbaoGetActivityDetailPointRsp implements Serializable {

    private static final long serialVersionUID = 153438561964351260L;

    @Schema(description = "运动列表id")
    private Long countId;

    @Schema(description = "运动详情主键id")
    private Long detailId;

    @Schema(description = "当前记录时间")
    private String recordTime;

    @Schema(description = "有氧体力值")
    private Double aerobicPower;

    @Schema(description = "无氧体力值")
    private Double anaerobicPower;

    @Schema(description = "有氧训练效果")
    private Double aerobicEffect;

    @Schema(description = "无氧训练效果")
    private Double anaerobicEffect;

    @Schema(description = "恢复时间")
    private Integer recoveryTime;

    @Schema(description = "卡路里")
    private Double calorie;

    @Schema(description = "步数，骑行、游泳、划船、跳绳无此指标")
    private Integer steps;

    @Schema(description = "心率")
    private Double heartRate;

    @Schema(description = "距离，划船机、跳绳无此指标")
    private Double distance;

    @Schema(description = "温度，游泳无此指标")
    private Double temperature;

    @Schema(description = "速度/配速，椭圆机、划船机、跳绳无此指标")
    private Double speed;

    @Schema(description = "经度，室内跑步、室内行走、泳池游泳、椭圆机、划船机、跳绳无此指标")
    private Double longitude;

    @Schema(description = "纬度，室内跑步、室内行走、泳池游泳、椭圆机、划船机、跳绳无此指标")
    private Double latitude;

    @Schema(description = "平均步幅，室内行走、室外行走独有")
    private Double avgStride;

    @Schema(description = "海拔，室外骑行、登山、越野跑独有")
    private Double altitude;

    @Schema(description = "坡度")
    private Double slope;

    @Schema(description = "趟数，游泳独有")
    private Integer tripNum;

    @Schema(description = "泳姿，游泳独有")
    private Integer posture;

    @Schema(description = "跳绳次数/划水数")
    private Integer sportNum;

    @Schema(description = "步频/跳绳频率/划水频率")
    private Double sportFrequency;

    @Schema(description = "爬上高度")
    private Double climHeight;

}
