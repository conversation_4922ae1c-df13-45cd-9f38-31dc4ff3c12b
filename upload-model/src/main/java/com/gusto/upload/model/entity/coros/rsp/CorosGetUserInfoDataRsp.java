package com.gusto.upload.model.entity.coros.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 高驰-获取用户信息
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
public class CorosGetUserInfoDataRsp {

    @Schema(description = "用户在 COROS 的昵称")
    private String nick;

    @Schema(description = "用户唯一标识")
    private String openId;

    @Schema(description = "用户头像")
    private String profilePhoto;

    @Schema(description = "累计跑步消耗卡路里，单位：大卡")
    private String runCalorie;

    @Schema(description = "累计跑步距离，单位：米")
    private String runDistance;

    @Schema(description = "累计跑步时间，单位：秒")
    private String runTotalTime;

    @Schema(description = "是否为灰度用户。true: 是灰度用户；false: 不是灰度用户。需要联系 COROS 平台管理员，在 COROS 平台配置。目前只有指定第三方用到了该字段")
    private Boolean isGray;

}
