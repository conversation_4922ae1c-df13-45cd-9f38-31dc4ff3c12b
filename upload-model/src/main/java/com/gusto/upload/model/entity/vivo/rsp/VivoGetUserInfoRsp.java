package com.gusto.upload.model.entity.vivo.rsp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VivoGetUserInfoRsp implements Serializable {

    private static final long serialVersionUID = -1321282590511183556L;

    @Schema(description = "healthOpenId用户ID")
    private String healthOpenId;

    @Schema(description = "应用测的用户ID")
    private String developerOpenId;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "用户类型，0：主用户 1：子用户**(通过code获取的主用户, 主用户只有一个，并且不可更改，不可删除)**")
    private Integer type;

}
