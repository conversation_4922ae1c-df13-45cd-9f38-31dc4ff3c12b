package com.gusto.upload.model.entity.huawei;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 华为-配置
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
@Data
@Component
@ConfigurationProperties(prefix = "upload.huawei")
public class HuaweiProperties {
    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 密钥
     */
    private String clientSecret;

    /**
     * 回调地址
     */
    private String redirectUri;

    /**
     * 我们的API版本号
     */
    private String outApiVersion;

    /**
     * 订阅secret
     */
    private String subscriptionSecret;
}
