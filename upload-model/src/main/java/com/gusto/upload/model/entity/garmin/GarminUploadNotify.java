package com.gusto.upload.model.entity.garmin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.upload.model.entity.BaseEntity;
import com.gusto.upload.model.entity.message.GarminActivityMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 佳明上传通知
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("garmin_upload_notify")
public class GarminUploadNotify extends BaseEntity {

    private static final long serialVersionUID = 1145564136826083213L;

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long notifyId;

    @Schema(description = "佳明用户ID")
    private String garminUserId;

    @Schema(description = "AccessToken")
    private String accessToken;

    @Schema(description = "开始时间，单位毫秒")
    private Long startTime;

    @Schema(description = "结束时间，单位毫秒")
    private Long endTime;

    @Schema(description = "状态：0-默认 1-失败 2-成功 3-执行中")
    private Integer state;

    @Schema(description = "来源：1-中国大陆 2-海外")
    private Integer source;

    /**
     * 创建消息
     */
    public GarminActivityMessage buildMessage() {
        var message = new GarminActivityMessage();
        message.setGarminUserId(garminUserId);
        message.setAccessToken(accessToken);
        message.setStartTime(startTime);
        message.setEndTime(endTime);
        message.setSource(source);
        return message;
    }

}
