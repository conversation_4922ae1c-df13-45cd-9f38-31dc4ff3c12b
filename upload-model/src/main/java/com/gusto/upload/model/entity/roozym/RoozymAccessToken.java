package com.gusto.upload.model.entity.roozym;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/6/9
 */
@Data

public class RoozymAccessToken {
    /**
     * 有效期（expiresIn）内请勿频繁请求，频率限制1000次/5分钟（出发限制后，5分钟重置）
     */
    @Schema(description = "用户的Access Token")
    private String accessToken;

    /**
     * 默认3600秒，即1小时有效
     */
    @Schema(description = "Access Token的剩余有效期，单位：秒")
    private Long expiresIn;

    /**
     * 如授权码Code请求的入参中包含access_type=offline，则会返回此参数，用于刷新Access Token
     */
    @Schema(description = "用户的Refresh Token")
    private String refreshToken;

    @Schema(description = "生成的凭证Access Token中包含的scope")
    private String scope;

    @Schema(description = "固定返回Bearer，标识返回凭证Access Token的类型")
    private String tokenType;

    /**
     * https://developer.huawei.com/consumer/cn/doc/development/HMSCore-References/account-verify-id
     * -token_hms_reference-****************#section3142132691914
     */
    @Schema(description = "第三方唯一ID")
    private String openId;
}
