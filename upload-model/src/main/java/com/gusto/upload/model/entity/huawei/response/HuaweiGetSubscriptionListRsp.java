package com.gusto.upload.model.entity.huawei.response;

import com.gusto.upload.model.entity.huawei.HuaweiSubscription;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 华为-查询订阅记录列表响应
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@Data

public class HuaweiGetSubscriptionListRsp {
    @Schema(description = "已成功注册订阅记录信息")
    private List<HuaweiSubscription> subscriptions;
}
