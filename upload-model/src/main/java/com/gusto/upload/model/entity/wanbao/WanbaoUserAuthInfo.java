package com.gusto.upload.model.entity.wanbao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.upload.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <AUTHOR>
 * @since 2024-09-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("upload_user_auth_info_wanbao")
public class WanbaoUserAuthInfo extends BaseEntity {

    private static final long serialVersionUID = -4689722361748771545L;

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long recordId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "RefreshToken")
    private String refreshToken;

    @Schema(description = "AccessToken")
    private String accessToken;

    @Schema(description = "AccessToken更新时间")
    private Instant accessTokenUpdateTime;

    @Schema(description = "平台用户ID")
    private String openId;

}
