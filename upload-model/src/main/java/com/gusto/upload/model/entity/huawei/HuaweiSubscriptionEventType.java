package com.gusto.upload.model.entity.huawei;

import com.gusto.upload.model.entity.BaseEnum;

/**
 * 华为-订阅事件类型
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
public enum HuaweiSubscriptionEventType implements BaseEnum {
    ACTIVITY_RECORD_EVENT_UPDATE(0, "ACTIVITY_RECORD_EVENT$UPDATE");  // 用户运动记录事件-创建/更新运动记录事件

    private final Integer number;
    private final String desc;

    HuaweiSubscriptionEventType(Integer number, String desc) {
        this.number = number;
        this.desc = desc;
    }

    /**
     * 根据枚举值获取枚举
     */
    public static HuaweiSubscriptionEventType forDesc(String desc) {
        switch (desc) {
            case "ACTIVITY_RECORD_EVENT$UPDATE":
                return ACTIVITY_RECORD_EVENT_UPDATE;
            default:
                return null;
        }
    }

    @Override
    public Integer getNumber() {
        return number;
    }

    public String getDesc() {
        return desc;
    }
}
