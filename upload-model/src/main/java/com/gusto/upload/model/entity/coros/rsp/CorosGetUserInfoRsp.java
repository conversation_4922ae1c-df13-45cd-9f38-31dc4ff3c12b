package com.gusto.upload.model.entity.coros.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 高驰-获取指定⽇期区间的活动列表
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
public class CorosGetUserInfoRsp {

    @Schema(description = "返回编码")
    private String result;

    @Schema(description = "返回信息")
    private String message;

    @Schema(description = "返回结果")
    private CorosGetUserInfoDataRsp data;

}
