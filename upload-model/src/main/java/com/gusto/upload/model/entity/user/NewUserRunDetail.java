package com.gusto.upload.model.entity.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 新用户跑步记录明细
 *
 * <AUTHOR>
 * @since 2022-07-20
 */
@Data
public class NewUserRunDetail implements Serializable {

    private static final long serialVersionUID = -3415233397770391770L;

    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "设备ID：1-APP 跑步 2-手动录入 3-手表 4-手环 5-跑步机")
    private Integer deviceId;

    @Schema(description = "设备类型：201-APP 录入 202-小程序 203-后台 301-佳明 302-宜准 303-高驰 304-华为 305-苹果 306-如骏")
    private Integer deviceType;

    @Schema(description = "设备名称")
    private String deviceModel;

    @Schema(description = "标识符，运动开始时间的毫秒时间戳，特定用户拥有的所有运动记录中都是唯一的")
    private Long activityId;

    @Schema(description = "运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它")
    private Integer activityType;

    @Schema(description = "开始时间，单位毫秒")
    private Long startTime;

    @Schema(description = "结束时间，单位毫秒")
    private Long endTime;

    @Schema(description = "总距离，单位米")
    private Double totalDistance;

    @Schema(description = "总时长，单位秒")
    private Integer totalDuration;

    @Schema(description = "总步数")
    private Integer totalStep;

    @Schema(description = "总卡路里，单位千卡")
    private Double totalCalorie;

    @Schema(description = "平均配速，单位秒")
    private Double averagePace;

    @Schema(description = "最快配速，单位秒")
    private Double maxPace;

    @Schema(description = "平均心率")
    private Integer averageHeartRate;

    @Schema(description = "最大心率")
    private Integer maxHeartRate;

    @Schema(description = "平均步频，单位步/分钟")
    private Integer averageStepRate;

    @Schema(description = "最快步频，单位步/分钟")
    private Integer maxStepRate;

    @Schema(description = "平均步幅，单位厘米")
    private Double averageStride;

    @Schema(description = "最低海拔，单位米")
    private Double minAltitude;

    @Schema(description = "最高海拔，单位米")
    private Double maxAltitude;

    @Schema(description = "累计上升，单位米")
    private Double totalAscent;

    @Schema(description = "累计下降，单位米")
    private Double totalDescent;

    @Schema(description = "天气")
    private String weather;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "实时气温，单位：摄氏度")
    private String temperature;

    @Schema(description = "空气湿度")
    private String humidity;

    @Schema(description = "每圈信息列表")
    private List<NewUserRunDetailLapItem> lapList;

    @Schema(description = "位置列表")
    private List<NewUserRunDetailLocationItem> locationList;

    @Schema(description = "心率列表")
    private List<NewUserRunDetailHeartRateItem> heartRateList;

    @Schema(description = "步频列表")
    private List<NewUserRunDetailStepRateItem> stepRateList;

    @Schema(description = "海拔列表")
    private List<NewUserRunDetailAltitudeItem> altitudeList;

    @Schema(description = "分段千米时间列表，<千米，累计时间/秒>，每一千米记录一次，半马，全马额外记录一次")
    private Map<String, Integer> partTimeKmList;

}
