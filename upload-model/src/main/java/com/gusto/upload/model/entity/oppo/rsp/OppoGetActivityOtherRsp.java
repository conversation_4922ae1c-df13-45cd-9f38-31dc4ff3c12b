package com.gusto.upload.model.entity.oppo.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
public class OppoGetActivityOtherRsp {

    @Schema(description = "平均心率，单位count/min")
    private Integer avgHeartRate;

    @Schema(description = "平均配速，单位s/km")
    private Integer avgPace;

    @Schema(description = "平均步频，单位step/min")
    private Integer avgStepRate;

    @Schema(description = "最佳步频，单位step/min")
    private Integer bestStepRate;

    @Schema(description = "最佳配速，单位s/km")
    private Integer bestPace;

    @Schema(description = "总消耗，单位卡")
    private Integer totalCalories;

    @Schema(description = "总距离，单位米")
    private Integer totalDistance;

    @Schema(description = "总步数")
    private Integer totalSteps;

    @Schema(description = "总时长，单位毫秒")
    private Long totalTime;

}
