package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 华为-分组周期
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data

public class HuaweiGroupByTimeGroupPeriod {
    @Schema(description = "时区，格式：+0800", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long timeZone;

    @Schema(description = "时间类型：day、month、week", requiredMode = Schema.RequiredMode.REQUIRED)
    private String unit;

    @Schema(description = "聚合周期值，需要和unit组合使用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer value;
}
