package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 华为-按照时间分组
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data

public class HuaweiGroupByTime {
    /**
     * 约束条件：不含数据的时间范围将包含在响应中，但采样集为空。配置时必须大于0
     */
    @Schema(description = "指定按照精确的duration时间帧来分组聚合数据到结果组。单位：毫秒。参数互斥，必须且只能配置其中之一", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long duration;

    /**
     * 约束条件：配置时必须大于0
     */
    @Schema(description = "分组周期", requiredMode = Schema.RequiredMode.REQUIRED)
    private HuaweiGroupByTimeGroupPeriod groupPeriod;
}
