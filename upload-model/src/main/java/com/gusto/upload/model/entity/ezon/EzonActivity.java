package com.gusto.upload.model.entity.ezon;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.gusto.upload.model.entity.message.EzonActivityMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 宜准-活动
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class EzonActivity {

    @Schema(description = "运动记录ID")
    private Long id;

    @Schema(description = "手表类型，目前有E1HR,C1,S6,E2为空表示手机跑数据")
    private String watchType;

    @Schema(description = "日期精确到天，时间格式为yyyy-MM-dd")
    private String day;

    @Schema(description = "轨迹开始时间，时间格式为yyyy-MM-ddTHH:mm:ss")
    private String startTime;

    @Schema(description = "轨迹结束时间，时间格式为yyyy-MM-ddTHH:mm:ss")
    private String endTime;

    @Schema(description = "总耗（秒）")
    private Integer totalSec;

    @Schema(description = "运动时长（秒）")
    private Integer duration;

    @Schema(description = "总消耗（千卡）")
    private Integer totalKcal;

    @Schema(description = "总距离（米）")
    private Integer totalMetre;

    @Schema(description = "总步步）")
    private Integer totalStep;

    @Schema(description = "运动类型：0-户外跑 1-室内跑 2-骑行 3-越野跑 4-泳池游泳 5-室外游泳 6-登山 7-徒步 8-滑雪 9-间歇训练 11-计时码表 12-无轨迹户外跑 13-无轨迹骑行 14-无轨迹登山 15-无轨迹徒步 16-羽毛球")
    private Integer movementTypeId;

    @Schema(description = "累计上升，单位米")
    private Integer totalAscent;

    @Schema(description = "累计下降，单位米")
    private Integer totalDescent;

    /**
     * 创建消息
     */
    public EzonActivityMessage buildMessage() {
        var message = new EzonActivityMessage();
        message.setActivity(this);
        return message;
    }

}
