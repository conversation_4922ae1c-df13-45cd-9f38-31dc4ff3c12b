package com.gusto.upload.model.entity.honor;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 荣耀-配置
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
@Component
@ConfigurationProperties(prefix = "upload.honor")
public class HonorProperties {

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 密钥
     */
    private String clientSecret;

    /**
     * 回调地址
     */
    private String redirectUri;

}
