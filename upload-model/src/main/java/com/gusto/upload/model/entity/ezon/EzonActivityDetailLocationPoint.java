package com.gusto.upload.model.entity.ezon;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 宜准-活动详情
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Data
public class EzonActivityDetailLocationPoint {

    @Schema(description = "经度")
    private Double lon;

    @Schema(description = "纬度")
    private Double lat;

    @Schema(description = "海拔")
    private Double ele;

    @Schema(description = "距离开始时间的间隔(秒)")
    private Integer dur;

}
