package com.gusto.upload.model.entity.roozym.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/6/17
 */
@Data

public class RoozymCancelAuthRsp {
    @Schema(description = "请求状态（1,成功 0,服务器异常 1001,用户id不存在 1002,商户id未授权 2001,请求参数错误 2002,请求header参数不完整 2003,签名错误 2004,版本已废弃）")
    private Long resultCode;

    @Schema(description = "描述")
    private String resultMsg;
}

