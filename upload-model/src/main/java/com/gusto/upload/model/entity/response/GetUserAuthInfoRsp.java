package com.gusto.upload.model.entity.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取用户授权状态响应
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@Data

public class GetUserAuthInfoRsp {

    @Schema(description = "授权状态：0-默认 1-未授权 2-已授权 3-已过期 4-授权失败")
    private Integer state;

    @Schema(description = "原因")
    private String reason;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "绑定时间")
    private Long bindTime;

}
