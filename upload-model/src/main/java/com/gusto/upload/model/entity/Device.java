package com.gusto.upload.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("upload_device")
public class Device extends BaseEntity {
    public static final String DEVICE_ID_FIELD = "device_id";
    private static final long serialVersionUID = -5848238904489941351L;
    @Schema(description = "设备ID")
    @TableId(type = IdType.AUTO)
    private Long deviceId;

    @Schema(description = "设备名称")
    private String name;

    @Schema(description = "设备logo")
    private String logo;

    @Schema(description = "上线状态：0-默认 1-上线 2-下线")
    private Integer onlineState;

    @Schema(description = "授权链接")
    private String authUrl;

    @Schema(description = "授权说明")
    private String content;

    @Schema(description = "是否支持手动同步")
    private Boolean manualSync;

    @Schema(description = "排序，倒序")
    private Integer sort;

}
