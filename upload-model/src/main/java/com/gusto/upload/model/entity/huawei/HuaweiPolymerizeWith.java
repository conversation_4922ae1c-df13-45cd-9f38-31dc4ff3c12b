package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 华为-聚合标准
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data
@Builder

public class HuaweiPolymerizeWith {
    /**
     * 约束条件：
     * 1. 拼接规则：collectorType:collectorDataType.name:appInfo.appPackageName:deviceInfo.manufacturer:deviceInfo
     * .modelNum:deviceInfo.uniqueId:collectorName
     * 2. 配置时，配置值需与请求体中相关字段保持一致
     */
    @Schema(description = "要聚合的数据采集器ID，参数互斥。必须且只能配置其中之一", requiredMode = Schema.RequiredMode.REQUIRED)
    private String dataCollectorId;

    @Schema(description = "要聚合的数据类型。属于此类型的数据都会被聚合到一起，长度范围：[1, 300]", requiredMode = Schema.RequiredMode.REQUIRED)
    private String dataTypeName;
}
