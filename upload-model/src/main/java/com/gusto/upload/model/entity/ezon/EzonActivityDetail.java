package com.gusto.upload.model.entity.ezon;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 宜准-活动详情
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Data
public class EzonActivityDetail {

    @Schema(description = "运动记录ID")
    private Long id;

    @Schema(description = "手表类型")
    private String watchType;

    @Schema(description = "日期,格式为yyyy-MM-dd")
    private String day;

    @Schema(description = "开始时间,格式为yyyy-MM-dd'T'HH:mm:ss")
    private String startTime;

    @Schema(description = "结束时间,格式为yyyy-MM-dd'T'HH:mm:ss")
    private String endTime;

    @Schema(description = "总耗时(秒)")
    private Integer totalSec;

    @Schema(description = "运动时长(秒)")
    private Integer duration;

    @Schema(description = "总消耗(千卡)")
    private Integer totalKcal;

    @Schema(description = "总距离(米)")
    private Integer totalMetre;

    @Schema(description = "总步数(步)")
    private Integer totalStep;

    @Schema(description = "轨迹坐标点列表")
    private List<EzonActivityDetailLocationPoint> locList;

    @Schema(description = "步频列表")
    private List<Integer> cadenceList;

    @Schema(description = "心率列表")
    private List<Integer> hrList;

    @Schema(description = "暂停时间段列表")
    private List<EzonActivityDetailStopTime> suspends;

    @Schema(description = "分段列表")
    private List<EzonActivityDetailLap> laps;

    @Schema(description = "配速列表")
    private List<Integer> paceList;

}
