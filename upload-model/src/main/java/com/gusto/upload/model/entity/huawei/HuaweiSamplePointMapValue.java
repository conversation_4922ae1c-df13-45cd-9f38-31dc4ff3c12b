package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 华为-采样点Map数值
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Data

public class HuaweiSamplePointMapValue {
    @Schema(description = "Map类型的采样点对应的浮点数值，以下同时只有一个字段生效")
    private Double floatValue;

    @Schema(description = "Map类型的采样点对应的整数值")
    private Integer integerValue;

    @Schema(description = "Map类型的采样点对应的长整型")
    private Long longValue;

    @Schema(description = "Map类型的采样点对应的字符串值。应尽可能减小字符串长度，具有大字符串值和高数据频率的数据流可能会被降低采样")
    private String stringValue;
}
