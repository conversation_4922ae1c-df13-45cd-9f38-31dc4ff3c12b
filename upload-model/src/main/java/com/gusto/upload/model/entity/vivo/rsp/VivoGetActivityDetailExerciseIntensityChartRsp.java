package com.gusto.upload.model.entity.vivo.rsp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VivoGetActivityDetailExerciseIntensityChartRsp implements Serializable {

    private static final long serialVersionUID = -1634716013124713921L;

    @Schema(description = "有氧耐力，单位：分钟")
    private Integer aerobic;

    @Schema(description = "无氧耐力")
    private Integer anaerobic;

    @Schema(description = "热身")
    private Integer warmUp;

    @Schema(description = "燃脂")
    private Integer fatBurning;

    @Schema(description = "极限")
    private Integer extremeSport;

}
