package com.gusto.upload.model.entity.huami.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 华米-使用授权码Code获取AT
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class HuamiGetAccessTokenRsp {

    @Schema(description = "AccessToken")
    private String accessToken;

    @Schema(description = "TokenType")
    private String tokenType;

    @Schema(description = "AccessToken超时时间，单位：秒")
    private Long expiresIn;

    @Schema(description = "RefreshToken")
    private String refreshToken;

}
