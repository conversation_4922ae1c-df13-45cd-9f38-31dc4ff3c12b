package com.gusto.upload.model.entity.app;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 用户消息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@Data

@TableName("app_message")
public class AppMessage {
    @TableId(value = "messageid", type = IdType.AUTO)
    private Long messageId;

    private Integer isSupper;

    @Schema(description = "用户ID")
    @TableField("uid")
    private Long userId;

    @Schema(description = "消息类型")
    @TableField("typeid")
    private Long typeId;

    @Schema(description = "消息标题")
    private String title;

    @Schema(description = "消息内容")
    private String content;

    @Schema(description = "发送时间")
    @TableField("createtime")
    private LocalDateTime createTime;

    @Schema(description = "1 已读 0未读 -1删除")
    private Integer state;

    @Schema(description = "转跳链接")
    private String link;

    @Schema(description = "与app约定操作")
    private String appointment;
}
