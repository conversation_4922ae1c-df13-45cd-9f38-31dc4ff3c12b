package com.gusto.upload.model.entity.vivo.rsp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VivoGetActivityDetailChartPointRsp implements Serializable {

    private static final long serialVersionUID = 7479570357061073736L;

    @Schema(description = "图表内容：" +
            "heartChart：心率；" +
            "altSportChart：海拔，单位米；" +
            "sphChart：时速，速度；" +
            "paceChart：配速，整公里数，最后不足一公里标志为-1；" +
            "speedChart：速度，单位km/h；" +
            "cadenceChart：步频，最近两个采集点间的步频（增加的相对步数/时间差转为分钟）")
    private Integer rate;

    @Schema(description = "时间戳")
    private Long time;

}
