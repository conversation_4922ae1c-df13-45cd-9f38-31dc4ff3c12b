package com.gusto.upload.model.entity.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 新用户跑步记录-明细-轨迹
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@Data
public class NewUserRunDetailLocationItem {

    @Schema(description = "毫秒时间戳")
    private Long timestamp;

    @Schema(description = "纬度，精确到八位小数")
    private Double latitude;

    @Schema(description = "经度，精确到八位小数")
    private Double longitude;

    @Schema(description = "速度，单位米/秒，1000/(m/s)=(s/km)")
    private Double speed;

}
