package com.gusto.upload.model.entity.user.old;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 跑步记录-明细-步频步幅配速
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Data
public class UserRunDetailStepItemTemp {
    @Schema(description = "步频，单位步/分钟")
    private Double stepfrequency;

    @Schema(description = "步幅，厘米/步")
    private Double stepstride;

    @Schema(description = "速度，km/h")
    private Double curSpeed;
}
