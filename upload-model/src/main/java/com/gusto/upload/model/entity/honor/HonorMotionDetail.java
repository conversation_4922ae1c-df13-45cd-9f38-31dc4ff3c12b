package com.gusto.upload.model.entity.honor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class HonorMotionDetail {

    @Schema(description = "海拔, 支持负数 (分米)")
    private Integer elevation;

    @Schema(description = "心率(次/分钟)")
    private Integer heartRate;

    @Schema(description = "跳绳速度(次/分钟)")
    private Integer jumpSpeed;

    @Schema(description = "配速(分米/秒)")
    private Integer speed;

    @Schema(description = "步频(步/分钟)")
    private Integer stepFrequency;

    @Schema(description = "游泳效率(无单位)")
    private Integer swimEfficiency;

    @Schema(description = "划水频率(次/分钟)")
    private Integer trashFrequency;

    @Schema(description = "燃脂速率")
    private Integer fatBurnRate;

    @Schema(description = "燃糖速率")
    private Integer sugarBurnRate;

    @Schema(description = "瞬时踏频")
    private Integer instantCadence;

    @Schema(description = "瞬时功率")
    private Integer instantPower;

    @Schema(description = "瞬时桨频")
    private Integer instantStrokeRate;

    @Schema(description = "瞬时配速(南向设备)")
    private Integer instantaneousPace;

}
