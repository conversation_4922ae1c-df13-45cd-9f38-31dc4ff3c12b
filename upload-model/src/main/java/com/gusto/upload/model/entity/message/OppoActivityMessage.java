package com.gusto.upload.model.entity.message;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
public class OppoActivityMessage {

    public static final String NOTIFY_ACTIVITY_TOPIC = "OppoActivityMessage_NotifyActivity_Topic_1";
    public static final String GROUP = "OppoActivityMessage_Group";

    /**
     * 用户唯一标识，授权的时候返回
     */
    private String openId;

    /**
     * 数据类型，目前只有一种SPORT_RECORD
     */
    private String dataType;

    /**
     * 运动记录开始时间
     */
    private Long timestamp;

}
