package com.gusto.upload.model.entity.roozym;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.upload.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <AUTHOR>
 * @since 2022/5/26
 */
@EqualsAndHashCode(callSuper = true)
@Data

@TableName("upload_user_auth_info_roozym")
public class RoozymAuthInfo extends BaseEntity {
    public static final String USER_ID_FIELD = "user_id";
    public static final String OPEN_ID_FIELD = "open_id";
    public static final String UNION_ID_FIELD = "union_id";
    public static final String DELETED_FIELD = "deleted";
    private static final long serialVersionUID = 1086337716120756399L;

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long recordId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "accessToken")
    private String accessToken;

    @Schema(description = "refreshToken")
    private String refreshToken;

    @Schema(description = "accessToken的有效时长，单位：秒")
    private Long expiresIn;

    @Schema(description = "accessToken更新时间")
    private Instant accessTokenUpdateTime;

    @Schema(description = "refreshToken是否过期")
    private Boolean refreshTokenExpires;

    @Schema(description = "三方应用的用户标识")
    private String openId;

    @Schema(description = "UnionId")
    private String unionId;

    @Schema(description = "逻辑删除")
    private Boolean deleted;

    /**
     * 更新AT统一入口
     */
    public void updateAccessToken(String newAccessToken) {
        accessToken = newAccessToken;
        accessTokenUpdateTime = Instant.now();
    }
}
