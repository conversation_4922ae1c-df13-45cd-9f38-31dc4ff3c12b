package com.gusto.upload.model.entity.huami.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 华米-获取运动列表响应
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class HuamiGetActivityDetailRsp {

    @Schema(description = "轨迹ID")
    private String trackId;

    @Schema(description = "跑步开始的时间戳，单位秒" +
        "例如1499156349")
    private Long startTime;

    @Schema(description = "以下采样时间: latitudeLongitude, altitude, accuracy, pace。格式: {时间差值;时间差值;…}，单位秒，第一个时间是与startTime的差值，" +
        "例如9;3;2;2;6;4")
    private String samplingTime;

    @Schema(description = "经纬度列表，格式: {纬度差值,经度差值;}，原始经纬度的每个值*100000000来做差值，第一个经纬度是与0的差值，缺失点用空字符串占位，" +
        "例如4004663552,11629333504;16403,8392;;;;14877,8392")
    private String latitudeLongitude;

    @Schema(description = "海拔列表，格式: {海拔;海拔;…}，单位厘米，2016-08之后默认值-2000000，之前为0，" +
        "例如7800;7772;7763;7763;-2000000")
    private String altitude;

    @Schema(description = "GPS精度列表，单位米，" +
        "例如0;1;1;1;1")
    private String accuracy;

    @Schema(description = "配速列表，格式: {配速;…}，单位秒/米，保留2位小数，" +
        "例如0.33;0.33;0.25;0.26")
    private String pace;

    @Schema(description = "步态列表，格式: {时间差(秒),步数差,步幅(厘米),步频;…}，" +
        "例如2,0,71,0;2,0,0,0;147,1,0,0")
    private String gait;

    @Schema(description = "距离列表，格式: {时间差值(秒),距离差值(米);…}，" +
        "例如2,0;10,48;2,27;2,5")
    private String distance;

    @Schema(description = "速度列表，格式: {时间差值(秒),速度(米/秒)}，速度保留2位小数，" +
        "例如2,1.20;4,2.45;3,3.5")
    private String speed;

    @Schema(description = "暂停列表，格式: {暂停开始时间,暂停结束时间差值,暂停开始索引,暂停停止索引,暂停类型;}，暂停类型:2手动,3自动，" +
        "例如1439383257,555,100,123,2")
    private String pause;

    @Schema(description = "心率列表，格式: {时间差值(秒),心率差值}，第一个时间是与startTime的差值，时间差值为1时用空字符串，" +
        "例如11,80;0,10;7,-6;4,1;,-1")
    private String heartRate;

    @Schema(description = "每公里配速统计，格式: {公里索引,耗费时间(秒),geohash,点索引,平均心率,与开始时间的差值(秒);..}，公里索引从0开始，" +
        "例如0,365,wx4ey4yqd7zt,130,79,366")
    private String kilometerPace;

    @Schema(description = "每英里统计，格式与kilometerPace相同")
    private String milePace;

    @Schema(description = "越野跑期间的配速统计，格式: {圈数索引,耗费时间(秒),距离(米),geohash,平均心率,与开始时间的差值(秒),绝对海拔(米),海拔上升(米),海拔下降(米);}，" +
        "圈数索引从0开始，海拔在2016-08之后默认值-2000000，之前为0")
    private String lapPace;

}
