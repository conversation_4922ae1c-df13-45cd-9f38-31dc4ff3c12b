package com.gusto.upload.model.entity.message;

import com.gusto.upload.model.entity.coros.CorosActivity;
import lombok.Data;

/**
 * 高驰-活动消息
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
@Data
public class CorosActivityMessage {

    public static final String NOTIFY_ACTIVITY_TOPIC = "CorosActivityMessage_NotifyActivity_Topic_1";
    public static final String GROUP = "CorosActivityMessage_Group";

    /**
     * 活动
     */
    private CorosActivity activity;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 创建唯一键
     */
    public String buildUniqueKey() {
        return activity.getStartTime() * 1000 + "-" + activity.getEndTime() * 1000 + "-" + activity.getMode() + "-" + activity.getSubMode() + "-" + userId;
    }

}
