package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 华为-活动记录
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Data

public class HuaweiActivityRecord {
    @Schema(description = "创建该活动记录的AppInfo", requiredMode = Schema.RequiredMode.REQUIRED)
    private HuaweiAppInfo appInfo;

    @Schema(description = "客户端生成的标识符。特定用户拥有的所有活动记录中都是唯一的。如果未指定标识符，将从开始时间和名称中创建一个", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    @Schema(description = "活动记录的最后修改时间。单位：毫秒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long modifyTime;

    @Schema(description = "用户设定的活动记录名称。例如：“周日骑行", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "活动记录开始时间(inclusive)。单位：毫秒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long startTime;

    @Schema(description = "活动记录活跃时间。单位：毫秒。毫秒开始/结束时间标识了活动记录的完整持续时间，但活动记录活跃时间可以更短")
    private Long activeTime;

    @Schema(description = "活动记录的概要汇总信息")
    private HuaweiActivitySummary activitySummary;

    @Schema(description = "活动记录对应的活动的类型")
    private Integer activityType;

    @Schema(description = "查询分页标识。当查询结果大于一页时才包含")
    private String cursor;

    @Schema(description = "活动记录描述")
    private String desc;

    @Schema(description = "活动记录结束时间。单位：毫秒如果未指定标识该活动记录仍在进行中")
    private Long endTime;

    @Schema(description = "时区信息，格式：+0800")
    private String timeZone;

    @Schema(description = "扩展元，JSON格式字符串")
    private String metadata;

    // new

    @Schema(description = "设备信息")
    private HuaweiDeviceInfo deviceInfo;
}
