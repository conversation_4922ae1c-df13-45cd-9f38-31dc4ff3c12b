package com.gusto.upload.model.entity.amap;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 高德逆地理编码
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AmapReGeoCode {

    @Schema(description = "地址元素")
    private AmapReGeoCodeAddressComponent addressComponent;

}
