package com.gusto.upload.model.entity.user.old;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 跑步记录-明细
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Data
public class UserRunDetailTemp {
    @Schema(description = "轨迹心率")
    private List<UserRunDetailLocationItemTemp> location;

    @Schema(description = "步频步幅配速")
    private List<UserRunDetailStepItemTemp> step;

    @Schema(description = "每公里配速表，单位是秒/公里")
    private List<Integer> lap;
}
