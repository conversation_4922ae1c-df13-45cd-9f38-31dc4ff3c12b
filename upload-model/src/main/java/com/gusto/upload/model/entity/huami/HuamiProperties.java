package com.gusto.upload.model.entity.huami;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 华米-配置
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@Component
@ConfigurationProperties(prefix = "upload.huami")
public class HuamiProperties {

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 密钥
     */
    private String clientSecret;

    /**
     * 回调地址
     */
    private String redirectUri;

    /**
     * API主机地址
     */
    private String apiHost;

}
