package com.gusto.upload.model.entity.garmin.req;

import com.gusto.upload.model.entity.message.GarminActivityMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 佳明-活动通知请求
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Data
public class GarminNotifyActivityReq {

    @Schema(description = "佳明用户ID")
    private String userId;

    @Schema(description = "用户AccessToken")
    private String userAccessToken;

    @Schema(description = "上传开始时间，单位秒")
    private Long uploadStartTimeInSeconds;

    @Schema(description = "上传结束时间，单位秒")
    private Long uploadEndTimeInSeconds;

    @Schema(description = "回调URL")
    private String callbackURL;

    /**
     * 创建消息
     */
    public GarminActivityMessage buildMessage(Integer source, Long outUserId) {
        var message = new GarminActivityMessage();
        message.setGarminUserId(userId);
        message.setAccessToken(userAccessToken);
        message.setStartTime(uploadStartTimeInSeconds);
        message.setEndTime(uploadEndTimeInSeconds);
        message.setSource(source);
        message.setUserId(outUserId);
        return message;
    }

}
