package com.gusto.upload.model.entity.huawei.response;

import com.gusto.upload.model.entity.huawei.HuaweiGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 华为-采样数据聚合分组响应
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data

public class HuaweiGetPolymerizeSampleSetRsp {
    @Schema(description = "包含汇总数据的组列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<HuaweiGroup> group;
}
