package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 华为-采样集
 * 1. SampleSet是一组SamplePoint的容器，它本身没有任何信息
 * 2. 一个SamplePoint可以被多个SampleSet包含
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data

public class HuaweiSampleSet {
    @Schema(description = "数据采集器ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String dataCollectorId;

    @Schema(description = "所有采样点的最大结束时间。单位：纳秒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long endTime;

    /**
     * 约束条件：
     * 1. 完整数据：对于较小的SampleSet
     * 2. 部分数据：SampleSet查询，且采样集较大时；patching增量更新SampleSet时
     */
    @Schema(description = "SamplePoint列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<HuaweiSamplePoint> samplePoints;

    @Schema(description = "所有采样点的最小开始时间。单位：纳秒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long startTime;


    @Schema(description = "SampleSet GET查询分页标识,且仅当查询结果大于一页时才包含")
    private String cursor;
}
