package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 华为-采样点数值
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Data

public class HuaweiSamplePointValue {
    @Schema(description = "字段名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fieldName;

    @Schema(description = "浮点数值，以下同时只有一个字段生效")
    private Double floatValue;

    @Schema(description = "整数值")
    private Integer integerValue;

    @Schema(description = "长整型")
    private Long longValue;

    @Schema(description = "Map值")
    private List<HuaweiSamplePointMap> mapValue;

    @Schema(description = "浮点数值，字符串值。应尽可能减小字符串长度，具有大字符串值和高数据频率的数据流可能会被降低采样")
    private String stringValue;
}
