package com.gusto.upload.model.entity.ezon.rsp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 宜准-用户信息
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class EzonUserInfo {

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "性别，0 为未知，1 为男，2 为女")
    private Integer gender;

    @Schema(description = "头像路径")
    private String icon;

}
