package com.gusto.upload.model.entity.honor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class HonorDetailData {

    @Schema(description = "当前分段数据的开始时间(秒级戳)")
    private Long startTime;

    @Schema(description = "开始时间(秒级戳)+deviceId")
    private String recordId;

    @Schema(description = "单次运动索引")
    private Integer index;

    @Schema(description = "每组数据的时间间隔(秒)")
    private Integer interval;

    @Schema(description = "运动详情数据列表")
    private List<HonorMotionDetail> detailDataList;

    @Schema(description = "心率来源")
    private String heartRateSource;

}
