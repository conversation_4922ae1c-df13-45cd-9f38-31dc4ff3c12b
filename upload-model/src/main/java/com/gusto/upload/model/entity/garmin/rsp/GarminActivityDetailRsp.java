package com.gusto.upload.model.entity.garmin.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 佳明-活动详情
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Data
public class GarminActivityDetailRsp {

    @Schema(description = "摘要的唯一标识符")
    private String summaryId = "";

    @Schema(description = "Garmin Connect上活动的唯一标识符")
    private String activityId = "";

    @Schema(description = "统计")
    private GarminActivityDetailSummaryRsp summary;

    @Schema(description = "样本列表")
    private List<GarminActivityDetailSampleRsp> samples;

    @Schema(description = "活动圈数数据")
    private List<GarminActivityDetailLapRsp> laps;

}
