package com.gusto.upload.model.entity.huawei;

import com.gusto.upload.model.entity.BaseEnum;

/**
 * 华为-活动类型
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
public enum HuaweiActivityType implements BaseEnum {
    UNKNOWN(0, "未知运动"),
    CYCLING(13, "骑自行车"),
    HIKING(30, "徒步/远足"),
    ON_FOOT(45, "步行中"),
    RUNNING(56, "跑步"),
    RUNNING_MACHINE(57, "在跑步机上跑步"),
    SWIMMING(81, "游泳"),
    SWIMMING_OPEN_WATER(82, "在开放水域游泳"),
    SWIMMING_POOL(83, "在游泳池游泳"),
    WALKING(90, "步行"),
    CYCLING_INDOOR(97, "骑室内单车"),
    SPINNING(110, "骑动感单车"),
    BMX(125, "骑BMX自行车"),
    ORIENTEERING(126, "进行定向越野运动"),
    WALKING_INDOOR(127, "室内步行"),
    RUNNING_INDOOR(128, "室内跑步"),
    MOUNTAIN_CLIMBING(129, "登山运动"),
    CROSS_COUNTRY_RACE(130, "越野跑活动"),

    FREEDIVING(154, "自由潜水");

    private final Integer number;
    private final String desc;

    HuaweiActivityType(Integer number, String desc) {
        this.number = number;
        this.desc = desc;
    }

    /**
     * 根据枚举值获取枚举
     */
    public static HuaweiActivityType forNumber(Integer value) {
        switch (value) {
            case 0:
                return UNKNOWN;
            case 13:
                return CYCLING;
            case 45:
                return ON_FOOT;
            case 56:
                return RUNNING;
            case 57:
                return RUNNING_MACHINE;
            case 81:
                return SWIMMING;
            case 82:
                return SWIMMING_OPEN_WATER;
            case 83:
                return SWIMMING_POOL;
            case 90:
                return WALKING;
            case 97:
                return CYCLING_INDOOR;
            case 110:
                return SPINNING;
            case 125:
                return BMX;
            case 126:
                return ORIENTEERING;
            case 127:
                return WALKING_INDOOR;
            case 128:
                return RUNNING_INDOOR;
            case 129:
                return MOUNTAIN_CLIMBING;
            case 130:
                return CROSS_COUNTRY_RACE;
            default:
                return null;
        }
    }

    @Override
    public Integer getNumber() {
        return number;
    }
}
