package com.gusto.upload.model.entity.roozym;

import com.gusto.upload.model.entity.BaseEnum;

/**
 * <AUTHOR>
 * @since 2022/6/1
 */
public enum RoozymSubscriptionEventType implements BaseEnum {
    EVENT_SPORT_ADD(0, "event_sport_add"),  // 用户运动记录事件-运动记录新增
    EVENT_SPORT_UPDATE(1, "event_sport_update"),  //用户运动记录事件-运动记录更新
    EVENT_SPORT_DELETE(2, "event_sport_delete"), //用户运动记录事件-运动记录删除
    EVENT_USER_UPDATE(3, "event_user_update"), // 个人信息修改
    EVENT_AUTH_CANCEL(4, "event_auth_cancel"); // 用户取消授权

    private final Integer number;
    private final String desc;

    RoozymSubscriptionEventType(Integer number, String desc) {
        this.number = number;
        this.desc = desc;
    }

    /**
     * 根据枚举值获取枚举
     */
    public static RoozymSubscriptionEventType forDesc(String desc) {
        switch (desc) {
            case "event_sport_add":
                return EVENT_SPORT_ADD;
            case "event_sport_update":
                return EVENT_SPORT_UPDATE;
            case "event_sport_delete":
                return EVENT_SPORT_DELETE;
            case "event_user_update":
                return EVENT_USER_UPDATE;
            case "event_auth_cancel":
                return EVENT_AUTH_CANCEL;
            default:
                return null;
        }
    }

    @Override
    public Integer getNumber() {
        return number;
    }

    public String getDesc() {
        return desc;
    }
}
