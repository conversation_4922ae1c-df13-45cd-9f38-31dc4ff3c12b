package com.gusto.upload.model.entity.vivo.rsp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VivoGetActivityDetailRsp implements Serializable {

    private static final long serialVersionUID = -3766623933833538339L;

    @Schema(description = "用户healthOpenId")
    private String openId;

    @Schema(description = "运动记录id也就是exerciseId")
    private String eid;

    @Schema(description = "运动类型（对应索引参考下方ExerciseInfo类型）")
    private Integer type;

    @Schema(description = "1异常数据，0正常数据(用户判定)")
    private Integer abnormalData;

    @Schema(description = "距离,单位米")
    private Integer distance;

    @Schema(description = "开始时间,13位时间戳")
    private Long startTime;

    @Schema(description = "时长,单位毫秒")
    private Long costTime;

    @Schema(description = "结束时间,13位时间戳")
    private Long endTime;

    @Schema(description = "身体恢复时间,单位分钟")
    private Integer recoveryTime;

    @Schema(description = "最大摄氧量,存在且使用")
    private Integer vo2Value;

    @Schema(description = "累计下降,单位米 （基于出发点海拔的下降的相对高度）")
    private Integer cumulativeDecline;

    @Schema(description = "累计上升,单位米")
    private Integer mountainTotalHeight;

    @Schema(description = "热量,单位千卡")
    private Integer calorie;

    @Schema(description = "平均心率")
    private Integer avgHeartRate;

    @Schema(description = "平均速度,km/h")
    private Integer averageSpeed;

    @Schema(description = "步频,步/每分钟")
    private Integer stepRate;

    @Schema(description = "步幅,单位米")
    private Integer pace;

    @Schema(description = "总步数")
    private Integer totalStep;

    @Schema(description = "和累计爬升同字段，需要赋值")
    private Integer totalAlt;

    @Schema(description = "最高速度km/h")
    private Integer maxSpeed;

    @Schema(description = "最低速度km/h")
    private Integer minSpeed;

    @Schema(description = "泳姿：'蛙泳'")
    private String swimmingPosture;

    @Schema(description = "划水次数")
    private Integer strokeNum;

    @Schema(description = "划水频次, 单位 次/分")
    private Integer strokeRate;

    @Schema(description = "划水圈数")
    private Integer cycleNum;

    @Schema(description = "泳池长度,单位米")
    private Integer swimmingPoolLength;

    @Schema(description = "数据来源 1：手机 2：手表")
    private Integer source;

    @Schema(description = "运动截图url")
    private String screenshotUrl;

    @Schema(description = "有氧强度")
    private Double aerobic;

    @Schema(description = "无氧强度")
    private Double anaerobic;

    @Schema(description = "是否暂停过 0未暂停,1暂停过")
    private Integer paused;

    @Schema(description = "平均海拔 单位米")
    private Integer averageAlt;

    @Schema(description = "每公里配速柱状图，存在但不使用")
    private String spmGram;

    @Schema(description = "心率折线")
    private VivoGetActivityDetailChartRsp heartChart;

    @Schema(description = "海拔折线")
    private VivoGetActivityDetailChartRsp altSportChart;

    @Schema(description = "时速折线")
    private VivoGetActivityDetailChartRsp sphChart;

    @Schema(description = "配速柱状图")
    private VivoGetActivityDetailChartRsp paceChart;

    @Schema(description = "速度折线")
    private VivoGetActivityDetailChartRsp speedChart;

    @Schema(description = "步频折线")
    private VivoGetActivityDetailChartRsp cadenceChart;

    @Schema(description = "运动强度分布")
    private VivoGetActivityDetailExerciseIntensityChartRsp exerciseIntensityBOChart;

    @Schema(description = "轨迹")
    private List<VivoGetActivityDetailTrackInfoRsp> trackInfos;

}
