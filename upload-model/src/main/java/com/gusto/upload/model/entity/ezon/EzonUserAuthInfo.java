package com.gusto.upload.model.entity.ezon;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.upload.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * 宜准-用户授权信息
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("upload_user_auth_info_ezon")
public class EzonUserAuthInfo extends BaseEntity {

    private static final long serialVersionUID = -5396661158914911425L;

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long recordId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "RefreshToken")
    private String refreshToken;

    @Schema(description = "AccessToken")
    private String accessToken;

    @Schema(description = "AccessToken更新时间")
    private Instant accessTokenUpdateTime;

    @Schema(description = "宜准用户ID")
    private String openId;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickname;

}
