package com.gusto.upload.model.entity.message;

import lombok.Data;

/**
 * 华米-活动消息
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class HuamiActivityMessage {

    public static final String NOTIFY_ACTIVITY_TOPIC = "HuamiActivityMessage_NotifyActivity_Topic_1";
    public static final String GROUP = "HuamiActivityMessage_Group";

    /**
     * 华米用户ID
     */
    private String openId;

    /**
     * 发生变更数据的参数json字符串
     */
    private String data;

    /**
     * 轨迹ID
     */
    private String trackId;

    /**
     * 构建唯一键
     */
    public String buildUniqueKey() {
        return String.format("huami_%s_%s", openId, trackId);
    }

}
