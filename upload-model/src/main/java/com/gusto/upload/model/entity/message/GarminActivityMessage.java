package com.gusto.upload.model.entity.message;

import lombok.Data;

/**
 * 佳明-活动消息
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Data
public class GarminActivityMessage {

    public static final String NOTIFY_ACTIVITY_TOPIC = "GarminActivityMessage_NotifyActivity_Topic_1";
    public static final String NOTIFY_GLOBAL_ACTIVITY_TOPIC = "GarminActivityMessage_NotifyGlobalActivity_Topic_1";
    public static final String GROUP = "GarminActivityMessage_Group";

    /**
     * 佳明用户ID
     */
    private String garminUserId;

    /**
     * 用户AccessToken
     */
    private String accessToken;

    /**
     * 开始时间，单位秒
     */
    private Long startTime;

    /**
     * 结束时间，单位秒
     */
    private Long endTime;

    /**
     * 来源：1-中国大陆 2-海外
     */
    private Integer source;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 创建唯一键
     */
    public String buildUniqueKey() {
        return startTime * 1000 + "-" + endTime * 1000 + "-" + userId;
    }

}
