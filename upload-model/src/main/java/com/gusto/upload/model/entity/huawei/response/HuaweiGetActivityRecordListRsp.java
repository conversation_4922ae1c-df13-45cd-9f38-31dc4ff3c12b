package com.gusto.upload.model.entity.huawei.response;

import com.gusto.upload.model.entity.huawei.HuaweiActivityRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 华为-查询已创建的活动记录响应
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Data

public class HuaweiGetActivityRecordListRsp {
    @Schema(description = "开始/结束时间范围内的活动记录列表")
    private List<HuaweiActivityRecord> activityRecord;

    @Schema(description = "已删除的活动记录列表。当includeDeleted为true时生效")
    private List<HuaweiActivityRecord> deletedActivityRecord;

    @Schema(description = "标识服务端是否有更多数据，默认为false")
    private Boolean hasMoreData;
}
