package com.gusto.upload.model.entity.ezon;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 宜准-配置
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Data
@Component
@ConfigurationProperties(prefix = "upload.ezon")
public class EzonProperties {

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 密钥
     */
    private String clientSecret;

    /**
     * 回调地址
     */
    private String redirectUri;

}
