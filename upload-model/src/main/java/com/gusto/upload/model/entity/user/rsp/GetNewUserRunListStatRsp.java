package com.gusto.upload.model.entity.user.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 获取跑步记录列表统计响应
 *
 * <AUTHOR>
 * @since 2022-08-22
 */
@Data
public class GetNewUserRunListStatRsp implements Serializable {

    private static final long serialVersionUID = -8134418610631897127L;

    @Schema(description = "跑步跑量")
    private Double totalDistance;

    @Schema(description = "跑步次数")
    private Integer runCount;

    @Schema(description = "平均配速，单位秒")
    private Double averagePace;

    @Schema(description = "总时长，单位秒")
    private Integer totalDuration;

    @Schema(description = "总卡路里，单位千卡")
    private Double totalCalorie;

    @Schema(description = "开始年份")
    private Integer startYear;

    @Schema(description = "结束年份")
    private Integer endYear;

    @Schema(description = "月份统计列表")
    private List<GetNewUserRunMonthStatRsp> monthStatList;

    @Schema(description = "最佳记录列表")
    private List<GetNewUserRunBestStatRsp> bestList;

    @Schema(description = "半马次数")
    private Integer halfMarathonCount;

    @Schema(description = "全马次数")
    private Integer marathonCount;

}
