package com.gusto.upload.model.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取用户设备列表子项
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@Data
public class UserDeviceListElementDTO {

    @Schema(description = "设备")
    private DeviceDTO device;

    @Schema(description = "是否绑定")
    private Boolean bind;

    @Schema(description = "绑定时间")
    private Long bindTime;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickname;

}
