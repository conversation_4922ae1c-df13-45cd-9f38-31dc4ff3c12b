package com.gusto.upload.model.entity.honor.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class HonorCommonRsp<T> {

    @Schema(description = "状态码：200-成功 其他-失败")
    private String code;

    @Schema(description = "状态码描述")
    private String message;

    @Schema(description = "返回数据，根据具体接口定义返回")
    private T data;

}
