package com.gusto.upload.model.entity.vivo.rsp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VivoCommonRsp<T> {

    @Schema(description = "状态码：0-成功 其他-失败")
    private Integer code;

    @Schema(description = "状态码描述")
    private String msg;

    @Schema(description = "返回数据，根据具体接口定义返回")
    private T data;

}
