package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 华为-活动记录
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Data

public class HuaweiActivitySummary {
    @Schema(description = "统计数据类型采样点列表，只能是统计类型")
    private List<HuaweiSamplePoint> dataSummary;

    @Schema(description = "配速信息")
    private HuaweiPaceSummary paceSummary;
}
