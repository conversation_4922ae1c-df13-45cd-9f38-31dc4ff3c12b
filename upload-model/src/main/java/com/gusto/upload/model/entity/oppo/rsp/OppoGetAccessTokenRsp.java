package com.gusto.upload.model.entity.oppo.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
public class OppoGetAccessTokenRsp {

    @Schema(description = "AccessToken")
    private String accessToken;

    @Schema(description = "RefreshToken")
    private String refreshToken;

    @Schema(description = "AccessToken超时时间，单位：秒，默认30天")
    private Long expireAt;

    @Schema(description = "权限列表")
    private List<String> scope;

}
