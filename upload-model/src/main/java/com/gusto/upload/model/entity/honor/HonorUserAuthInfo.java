package com.gusto.upload.model.entity.honor;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.upload.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * 荣耀-用户授权信息
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("upload_user_auth_info_honor")
public class HonorUserAuthInfo extends BaseEntity {
    public static final String USER_ID_FIELD = "user_id";
    public static final String OPEN_ID_FIELD = "open_id";
    public static final String UNION_ID_FIELD = "union_id";
    public static final String DELETED_FIELD = "deleted";

    private static final long serialVersionUID = 3508020488117179843L;

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long recordId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "accessToken")
    private String accessToken;

    @Schema(description = "refreshToken")
    private String refreshToken;

    @Schema(description = "accessToken更新时间")
    private Instant accessTokenUpdateTime;

    @Schema(description = "三方应用的用户标识")
    private String openId;

    @Schema(description = "UnionId")
    private String unionId;

    @Schema(description = "逻辑删除")
    private Boolean deleted;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickname;

    /**
     * 更新AT统一入口
     */
    public void updateAccessToken(String newAccessToken) {
        accessToken = newAccessToken;
        accessTokenUpdateTime = Instant.now();
    }
}
