package com.gusto.upload.model.entity.wanbao.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
public class WanbaoGetActivityDetailRsp {

    @Schema(description = "运动类型, trail_run:越野跑, run_inside:室内跑, run_outside:室外跑, swim_outside:户外游泳, swim_inside:泳池游泳")
    private String sportType;

    @Schema(description = "运动类型中文名称，例如：越野跑、室内跑")
    private String sportTypeName;

    @Schema(description = "用户Id")
    private String memberAccountId;

    @Schema(description = "运动详情集合")
    private List<WanbaoGetActivityDetailPointRsp> sportDetailList;

    @Schema(description = "运动计圈集合")
    private List<WanbaoGetActivityDetailCycleRsp> sportCircleList;

}
