package com.gusto.upload.model.entity.honor.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class HonorGetActivityRecordListReq {

    @Schema(description = "必填，ms时间戳")
    private Long startTime;

    @Schema(description = "非必填，ms时间戳，若为空，返回开始时间算起七天的数据（如果结束时间超过当前时间则返回至当前时间的数据）")
    private Long endTime;

    @Schema(description = "必填，数据类型列表，例如：RECORD_RUNNING_OUTDOOR、RECORD_RUNNING_INDOOR等")
    private List<String> dataTypes;

    @Schema(description = "必填，用户的openID")
    private String openId;

    @Schema(description = "必填，应用在荣耀开发者平台注册的appId")
    private String appId;

    @Schema(description = "必填，账号登录授权后获取的身份信息")
    private String accessToken;

    @Schema(description = "必填，app secret 加密后密文")
    private String secretData;

}
