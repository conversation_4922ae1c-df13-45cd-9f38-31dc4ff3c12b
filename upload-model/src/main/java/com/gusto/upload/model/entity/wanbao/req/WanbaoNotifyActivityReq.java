package com.gusto.upload.model.entity.wanbao.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
public class WanbaoNotifyActivityReq {

    @Schema(description = "运动列表主键id")
    private Long countId;

    @Schema(description = "运动类型, trail_run:越野跑, run_inside:室内跑, run_outside:室外跑, swim_outside:户外游泳, swim_inside:泳池游泳")
    private String sportType;

    @Schema(description = "会员id")
    private String memberAccountId;

    @Schema(description = "运动开始时间")
    private String startTime;

    @Schema(description = "运动数据文件地址(JSON格式文件)")
    private String fileUrl;

}
