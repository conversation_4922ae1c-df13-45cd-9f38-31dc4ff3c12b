package com.gusto.upload.model.entity.honor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class HonorActivityRecord {

    @Schema(description = "开始时间，单位秒")
    private Long startTime;

    @Schema(description = "结束时间，单位秒")
    private Long endTime;

    @Schema(description = "数据类型，例如：RECORD_RUNNING_OUTDOOR")
    private String dataType;

    @Schema(description = "运动预览信息")
    private HonorRecPreview recPreview;

    @Schema(description = "运动详细信息")
    private HonorRecDetail recDetail;

}
