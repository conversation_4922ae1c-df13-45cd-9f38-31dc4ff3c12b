package com.gusto.upload.model.entity.oppo.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
public class OppoGetActivityFitnessRsp {

    @Schema(description = "平均心率，单位count/min")
    private Integer avgHeartRate;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "完成次数")
    private Integer finishNumber;

    @Schema(description = "训练消耗卡路里，单位卡")
    private Integer trainedCalorie;

    @Schema(description = "训练时间，单位毫秒")
    private Long trainedDuration;

}
