package com.gusto.upload.model.entity.huawei.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.gusto.upload.model.entity.huawei.HuaweiMetaData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 华为-订阅时间通知请求
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@Data

@JsonIgnoreProperties(ignoreUnknown = true)
public class HuaweiNotificationReq {
    @Schema(description = "三方应用的用户标识")
    private String openId;

    @Schema(description = "事件发生时间")
    private Long eventTime;

    @Schema(description = "事件类型")
    private String eventType;

    @Schema(description = "事件扩展元数据")
    private List<HuaweiMetaData> metaData;
}
