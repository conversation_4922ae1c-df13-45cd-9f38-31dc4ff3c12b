package com.gusto.upload.model.entity.vivo.rsp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VivoNotifyActivityRecordRsp implements Serializable {

    private static final long serialVersionUID = 7436927760389859649L;

    @Schema(description = "用户healthOpenId")
    private String openId;

    @Schema(description = "运动记录id也就是exerciseId")
    private String eid;

    @Schema(description = "运动类型（对应索引参考下方ExerciseInfo类型）")
    private Integer type;

    @Schema(description = "距离,单位米")
    private Integer distance;

    @Schema(description = "开始时间,13位时间戳")
    private Long startTime;

    @Schema(description = "时长,单位毫秒")
    private Long costTime;

    @Schema(description = "结束时间,13位时间戳")
    private Long endTime;

    @Schema(description = "平均速度,km/h")
    private Integer averageSpeed;

    @Schema(description = "步频,步/每分钟")
    private Integer stepRate;

    @Schema(description = "步幅,单位米")
    private Integer pace;

    @Schema(description = "总步数")
    private Integer totalStep;

}
