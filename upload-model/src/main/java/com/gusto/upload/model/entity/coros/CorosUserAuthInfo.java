package com.gusto.upload.model.entity.coros;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.upload.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * 高驰-用户授权信息
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("upload_user_auth_info_coros")
public class CorosUserAuthInfo extends BaseEntity {

    private static final long serialVersionUID = -8083511859901798536L;

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long recordId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "RefreshToken")
    private String refreshToken;

    @Schema(description = "AccessToken")
    private String accessToken;

    @Schema(description = "AccessToken更新时间")
    private Instant accessTokenUpdateTime;

    @Schema(description = "高驰用户ID")
    private String openId;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickname;

}
