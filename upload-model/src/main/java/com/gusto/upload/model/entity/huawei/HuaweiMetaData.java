package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 华为-事件扩展元数据
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Data
public class HuaweiMetaData {
    /**
     * 约束条件：适用于用户运动记录事件和采样数据集事件
     * <p>
     * 当前支持的metaKey：
     * startTime：数据变化的开始时间
     * endTime：数据变化的结束时间
     * activityType：ActivityRecord运动类型
     * datatype：原子采样数据类型
     * timeZone：时区
     * recordDay：运动记录
     */
    @Schema(description = "元数据Key", requiredMode = Schema.RequiredMode.REQUIRED)
    private String metaKey;

    @Schema(description = "元数据值", requiredMode = Schema.RequiredMode.REQUIRED)
    private String metaValue;
}
