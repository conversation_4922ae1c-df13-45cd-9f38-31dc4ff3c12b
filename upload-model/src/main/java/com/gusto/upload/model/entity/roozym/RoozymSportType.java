package com.gusto.upload.model.entity.roozym;

import com.gusto.upload.model.entity.BaseEnum;

/**
 * <AUTHOR>
 * @since 2022/5/26
 */
public enum RoozymSportType implements BaseEnum {
    HIKING(0, "徒步"),
    RUNNING(1, "跑步"),
    CYCLING(2, "骑行"),
    MOUNTAIN_CLIMBING(3, "登山"),
    SWIMMING_POOL(4, "室内游泳"),
    SWIMMING_OPEN_WATER(5, "室外游泳"),
    TRIATHLON(6, "铁人三项"),
    WALKING(8, "健走"),
    WALKING_CROSS_COUNTRY_RACE(9, "徒步越野"),
    RUNNING_IN_ROOM(10, "室内跑"),
    CROSS_COUNTRY_RACE(11, "越野跑"),
    MARATHON(12, "马拉松"),
    TEAM_TRAINING(13, "团队训练"),
    AI_TRAINING(14, "AI训练");


    private final Integer number;
    private final String desc;

    RoozymSportType(Integer number, String desc) {
        this.number = number;
        this.desc = desc;
    }

    /**
     * 根据枚举值获取枚举
     */
    public static RoozymSportType forNumber(Integer value) {
        switch (value) {
            case 0:
                return HIKING;
            case 1:
                return RUNNING;
            case 2:
                return CYCLING;
            case 3:
                return MOUNTAIN_CLIMBING;
            case 4:
                return SWIMMING_POOL;
            case 5:
                return SWIMMING_OPEN_WATER;
            case 6:
                return TRIATHLON;
            case 8:
                return WALKING;
            case 9:
                return WALKING_CROSS_COUNTRY_RACE;
            case 10:
                return RUNNING_IN_ROOM;
            case 11:
                return CROSS_COUNTRY_RACE;
            case 12:
                return MARATHON;
            case 13:
                return TEAM_TRAINING;
            case 14:
                return AI_TRAINING;
            default:
                return null;
        }
    }

    @Override
    public Integer getNumber() {
        return number;
    }
}
