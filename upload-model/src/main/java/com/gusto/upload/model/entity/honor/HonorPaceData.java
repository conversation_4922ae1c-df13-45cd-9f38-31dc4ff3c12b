package com.gusto.upload.model.entity.honor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class HonorPaceData {

    @Schema(description = "距离点(计数, 标识当前配速是第几公里/英里的配速)")
    private Integer distance;

    @Schema(description = "尾端距离(分米), 非整公里/英里, 此字段为 null 则表示本次配速为整数公英里")
    private Integer distanceTail;

    @Schema(description = "配速值(单位由 unit 决定)")
    private Integer pace;

    @Schema(description = "从运动开始到配速计算点的 GPS 点累计个数, 如无 GPS 轨迹, 可能为null")
    private Integer pointCount;

    @Schema(description = "配速单位(0:秒/公里, 1:秒/英里)")
    private Integer unit;

}
