package com.gusto.upload.model.entity.honor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 荣耀-鉴权凭证
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class HonorAccessToken {

    @Schema(description = "用户凭证 Access Token")
    private String accessToken;

    @Schema(description = "Access Token 的过期时间，以秒为单位, 过期时间为3600秒")
    private Long expiresIn;

    @Schema(description = "如果scope中包含openid，则会返回id_token，JWT格式")
    private String idToken;

    @Schema(description = "如果auhtorize请求中access_type=offline，则返回Refresh Token")
    private String refreshToken;

    @Schema(description = "Access Token 中的用户权限scope")
    private String scope;

    @Schema(description = "固定值: Bearer")
    private String tokenType;

}
