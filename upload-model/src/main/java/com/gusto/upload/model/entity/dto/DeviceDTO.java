package com.gusto.upload.model.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
@Data
public class DeviceDTO {

    @Schema(description = "设备ID")
    private Long deviceId;

    @Schema(description = "设备名称")
    private String name;

    @Schema(description = "设备logo")
    private String logo;

    @Schema(description = "授权链接")
    private String authUrl;

    @Schema(description = "授权说明")
    private String content;

    @Schema(description = "是否支持手动同步")
    private Boolean manualSync;

}
