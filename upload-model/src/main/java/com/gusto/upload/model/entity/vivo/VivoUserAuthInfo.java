package com.gusto.upload.model.entity.vivo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.upload.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <AUTHOR>
 * @since 2024-04-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("upload_user_auth_info_vivo")
public class VivoUserAuthInfo extends BaseEntity {

    private static final long serialVersionUID = 5304100715244080709L;

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long recordId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "RefreshToken，有效期180天")
    private String refreshToken;

    @Schema(description = "RefreshToken过期时间")
    private Instant refreshTokenExpireTime;

    @Schema(description = "AccessToken，有效期30天")
    private String accessToken;

    @Schema(description = "AccessToken过期时间")
    private Instant accessTokenExpireTime;

    @Schema(description = "VIVO用户ID")
    private String openId;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickname;

}
