package com.gusto.upload.model.entity.vivo.rsp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VivoGetActivityDetailChartRsp implements Serializable {

    private static final long serialVersionUID = 6008401834066057498L;

    @Schema(description = "存在但不使用")
    private Integer gap;

    @Schema(description = "图表内容：" +
            "heartChart：最大心率；" +
            "altSportChart：最高海拔；" +
            "sphChart：最快时速；" +
            "paceChart：最快配速；" +
            "speedChart：最大速度，单位km/h；" +
            "cadenceChart：最大步频，单位步/每分钟")
    private Integer up;

    @Schema(description = "最小心率｜最低海拔｜最慢时速｜最慢配速｜最小速度｜最小步频")
    private Integer down;

    @Schema(description = "数据点")
    private List<VivoGetActivityDetailChartPointRsp> points;

}
