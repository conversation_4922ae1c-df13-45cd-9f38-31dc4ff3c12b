package com.gusto.upload.model.entity.honor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class HonorSectionDataEntity {

    @Schema(description = "分段索引")
    private Integer index;

    @Schema(description = "动作ID, 手表侧唯一ID, 字母+数字")
    private String actionId;

    @Schema(description = "根据 actionId 和设备侧提供的映射表格转换的动作名称")
    private String actionName;

    @Schema(description = "分段内平均心率(bpm)")
    private Integer heartRate;

    @Schema(description = "分段内平均速度(km/h)")
    private Integer avgSpeed;

    @Schema(description = "分段内最大速度(km/h)")
    private Integer maxSpeed;

    @Schema(description = "分段配速(秒/公里)")
    private Integer pace;

    @Schema(description = "分段内平均步频(步/分钟)")
    private Integer cadence;

    @Schema(description = "分段内平均步幅(厘米)")
    private Integer stepLength;

    @Schema(description = "分段距离(厘米)")
    private Integer distance;

    @Schema(description = "动作数据融合类型")
    private Integer resultType;

    @Schema(description = "动作完成数据值")
    private Integer resultValue;

    @Schema(description = "分段内平均触地时间(毫秒)")
    private Integer groundContactTime;

    @Schema(description = "分段内平均触地冲击(g（1重力加速度）)")
    private Integer groundImpactAcceleration;

    @Schema(description = "分段时间(秒)")
    private Integer time;

    @Schema(description = "动作 (目标/总) 数据值")
    private Integer targetValue;

    @Schema(description = "分段内累计爬升(厘米)")
    private Integer totalRise;

    @Schema(description = "分段内累计下降(厘米)")
    private Integer totalDescend;

}
