package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * 华为-已授权应用的权限
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data

public class HuaweiScopeLangItem {
    @Schema(description = "ScopeUrl与Scope描述的map表达形式", requiredMode = Schema.RequiredMode.REQUIRED)
    private Map<String, String> url2Desc;

    @Schema(description = "授权时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String authTime;

    @Schema(description = "应用的名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String appName;

    @Schema(description = "应用的图标地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String appIconPath;
}
