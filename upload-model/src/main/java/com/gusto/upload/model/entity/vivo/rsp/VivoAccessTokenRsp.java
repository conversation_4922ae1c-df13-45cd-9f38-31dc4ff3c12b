package com.gusto.upload.model.entity.vivo.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-04-15
 */
@Data
public class VivoAccessTokenRsp {

    @Schema(description = "vivo Health Kit服务的用户id")
    private String healthOpenId;

    @Schema(description = "accessToken(默认过期时间30天，过期后需要使用refreshToken获取新的accessToken)")
    private String accessToken;

    @Schema(description = "accessToken过期时间")
    private Long accessTokenExpireTime;

    @Schema(description = "refreshToken(默认过期时间180天，过期后需要用户重新授权)")
    private String refreshToken;

    @Schema(description = "refreshToken过期时间")
    private Long refreshTokenExpireTime;

}
