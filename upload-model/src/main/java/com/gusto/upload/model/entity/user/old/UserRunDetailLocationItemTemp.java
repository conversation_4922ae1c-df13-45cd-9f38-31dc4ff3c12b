package com.gusto.upload.model.entity.user.old;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 跑步记录-明细-轨迹心率
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Data
public class UserRunDetailLocationItemTemp {
    @Schema(description = "标记是否过快")
    private Boolean isTooFast;

    @Schema(description = "跑步索引")
    private Integer runindex;

    @Schema(description = "当前毫秒时间戳")
    private Long time;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "速度，单位m/s")
    private Double speed;

    @Schema(description = "海拔，单位m")
    private Double altitude;

    @Schema(description = "心率，单位次/分钟")
    private Integer heartRate;
}
