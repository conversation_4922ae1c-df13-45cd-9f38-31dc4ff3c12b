package com.gusto.upload.model.entity.app;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;

/**
 * 跑步记录错误日志
 *
 * <AUTHOR>
 * @since 2022-10-08
 */
@Data
@TableName("app_run_error_log")
public class AppRunErrorLog {

    @Schema(description = "记录ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long logId;

    @Schema(description = "用户ID")
    @TableField("date")
    private Instant startTime;

    @Schema(description = "标识符，运动开始时间的毫秒时间戳，特定用户拥有的所有运动记录中都是唯一的")
    @TableField("runId")
    private Long activityId;

    @Schema(description = "用户ID")
    @TableField("uid")
    private Long userId;

    @Schema(description = "手机号码")
    @TableField("tel")
    private String mobile;

    @Schema(description = "总距离，单位米")
    @TableField("distance")
    private Double totalDistance;

    @Schema(description = "总时长，单位秒")
    @TableField("usetime")
    private Integer totalDuration;

    @Schema(description = "平均配速，单位秒")
    @TableField("averagepace")
    private Double averagePace;

    @Schema(description = "总步数")
    @TableField("stepcount")
    private Integer totalStep;

    @Schema(description = "平均步频，单位步/分钟")
    @TableField("stepfrequency")
    private Integer averageStepRate;

    @Schema(description = "平均步幅，单位厘米")
    @TableField("stepstride")
    private Double averageStride;

    @Schema(description = "APP来源：安卓-android 苹果-iOS")
    @TableField("system")
    private String appSource;

    @Schema(description = "手机厂商")
    @TableField("brand")
    private String appBrand;

    @Schema(description = "APP版本")
    @TableField("version")
    private String appVersion;

}
