package com.gusto.upload.model.entity.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.Map;

/**
 * 新用户跑步记录
 *
 * <AUTHOR>
 * @since 2022-07-20
 */
@Data
@TableName(value = "app_run", autoResultMap = true)
public class NewUserRun implements Serializable {

    private static final long serialVersionUID = -4158908990398545337L;

    @Schema(description = "记录ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long recordId;

    @Schema(description = "标识符，运动开始时间的毫秒时间戳，特定用户拥有的所有运动记录中都是唯一的")
    @TableField("runId")
    private Long activityId;

    @Schema(description = "用户ID")
    @TableField("uid")
    private Long userId;

    @Schema(description = "设备ID：1-APP 跑步 2-手动录入 3-手表 4-手环 5-跑步机")
    @TableField("source")
    private Integer deviceId;

    @Schema(description = "设备类型：201-APP 录入 202-小程序 203-后台 301-佳明 302-宜准 303-高驰 304-华为 305-苹果 306-如骏 307-佳明海外 308-OPPO 309-VIVO 310-腕宝 311-荣耀 312-华米")
    @TableField("sub_source")
    private Integer deviceType;

    @Schema(description = "设备名称")
    @TableField("devicename")
    private String deviceModel;

    @Schema(description = "运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它")
    @TableField("sports_type")
    private Integer activityType;

    @Schema(description = "开始时间，单位毫秒")
    @TableField("starttime")
    private Long startTime;

    @Schema(description = "结束时间，单位毫秒")
    @TableField("endtime")
    private Long endTime;

    @Schema(description = "总距离，单位米")
    @TableField("distance")
    private Double totalDistance;

    @Schema(description = "总时长，单位秒")
    @TableField("usetime")
    private Integer totalDuration;

    @Schema(description = "总步数")
    @TableField("stepcount")
    private Integer totalStep;

    @Schema(description = "总卡路里，单位千卡")
    @TableField("calorie")
    private Double totalCalorie;

    @Schema(description = "平均配速，单位秒")
    @TableField("averagepace")
    private Double averagePace;

    @Schema(description = "最快配速，单位秒")
    @TableField("max_pace")
    private Double maxPace;

    @Schema(description = "平均心率")
    @TableField("averageheartrate")
    private Integer averageHeartRate;

    @Schema(description = "最大心率")
    @TableField("max_heart_rate")
    private Integer maxHeartRate;

    @Schema(description = "平均步频，单位步/分钟")
    @TableField("stepfrequency")
    private Integer averageStepRate;

    @Schema(description = "最快步频，单位步/分钟")
    @TableField("max_step_rate")
    private Integer maxStepRate;

    @Schema(description = "平均步幅，单位厘米")
    @TableField("stepstride")
    private Double averageStride;

    @Schema(description = "最低海拔，单位米")
    @TableField("min_altitude")
    private Double minAltitude;

    @Schema(description = "最高海拔，单位米")
    @TableField("max_altitude")
    private Double maxAltitude;

    @Schema(description = "累计上升，单位米")
    @TableField("ascent")
    private Double totalAscent;

    @Schema(description = "累计下降，单位米")
    @TableField("descent")
    private Double totalDescent;

    @Schema(description = "天气")
    @TableField("weather")
    private String weather;

    @Schema(description = "省")
    @TableField("province")
    private String province;

    @Schema(description = "市")
    @TableField("city")
    private String city;

    @Schema(description = "实时气温，单位：摄氏度")
    @TableField("temperature")
    private String temperature;

    @Schema(description = "空气湿度")
    @TableField("humidity")
    private String humidity;

    @Schema(description = "分段千米时间列表，<千米，累计时间/秒>，每一千米记录一次，半马，全马额外记录一次")
    @TableField(value = "part_time_km_list", typeHandler = JacksonTypeHandler.class)
    private Map<String, Integer> partTimeKmList;

    @Schema(description = "第三方活动类型")
    @TableField("eventid")
    private String thirdActivityType;

    @Schema(description = "第三方活动记录ID")
    @TableField("event")
    private String thirdActivityId;

    // 目前只是用来区分户外跑和室内跑
    @Schema(description = "0-正常 1-后台修改 2-跑步机 3-补录 6-佳明 7-华为 8-宜准 9-高驰 10-佳明海外 11-OPPO 12-VIVO 13-腕宝 14-荣耀 15-华米")
    @TableField("abnormal")
    private Integer abnormal;

    @Schema(description = "外部导入类型")
    @TableField("external_sports_type")
    private String externalSportType;

    @Schema(description = "带地图轨迹图")
    @TableField("fileurl")
    private String mapTrackImage;

    @Schema(description = "轨迹图")
    @TableField("fileurl_track")
    private String trackImage;

    @Schema(description = "原始总步数")
    private Integer rawTotalStep;

    @Schema(description = "fit文件URL")
    @TableField("upimg")
    private String fitUrl;

    // 以下字段在 NewUserRunApplicationService.handlerAndCreate 方法中进行填充

    @Schema(description = "年")
    @TableField("year")
    private Integer year;

    @Schema(description = "月")
    @TableField("month")
    private Integer month;

    @Schema(description = "日")
    @TableField("day")
    private Integer day;

    @Schema(description = "周")
    @TableField("week")
    private Integer week;

    @Schema(description = "版本")
    @TableField("version")
    private Integer version;

    @Schema(description = "最佳1km用时，单位秒")
    @TableField("best_one_km_time")
    private Integer bestOneKm;

    @Schema(description = "最佳3km用时，单位秒")
    @TableField("best_three_km_time")
    private Integer bestThreeKm;

    @Schema(description = "最佳5km用时，单位秒")
    @TableField("best_five_km_time")
    private Integer bestFiveKm;

    @Schema(description = "最佳10km用时，单位秒")
    @TableField("best_ten_km_time")
    private Integer bestTenKm;

    @Schema(description = "半马用时，单位秒")
    @TableField("best_half_marathon_time")
    private Integer bestHalfMarathon;

    @Schema(description = "全马用时，单位秒")
    @TableField("best_marathon_time")
    private Integer bestMarathon;

    @Schema(description = "轨迹文件名称")
    @TableField("trackfile")
    private String trackFile;

    @Schema(description = "上传时间")
    @TableField("uploadtime")
    private Instant uploadTime;

    @Schema(description = "更新时间")
    @TableField("modifytime")
    private Instant updateTime;

    @Schema(description = "是否后台修改")
    @TableField("admin_modify")
    private Boolean adminUpdate;

    @Schema(description = "修改时间")
    @TableField("admin_modify_time")
    private Instant adminUpdateTime;

    @Schema(description = "是否为跑步记录：1-是 2-其他运动")
    @TableField("category")
    private Integer runRecord;

    @Schema(description = "是否异常：0-正常 1-异常")
    @TableField("isunusual")
    private Integer unusual;

    @Schema(description = "异常信息，目前只有重复记录")
    @TableField("unusualmsg")
    private String unusualMessage;

    @Schema(description = "是否重复")
    @TableField("isduplicate")
    private Boolean duplicate;

    @Schema(description = "重复记录分组ID")
    @TableField("duplicate_groupid")
    private Long duplicateGroupId;

    @Schema(description = "状态：-1-已删除 1-正常")
    @TableField("state")
    private Integer state;

    @Schema(description = "0-正常 1-补录")
    @TableField("type")
    private Integer type;

    @Schema(description = "APP版本")
    private String appVersion;

    @Schema(description = "APP来源：0-默认 1-安卓 2-iOS 3-HarmonyOS")
    private Integer appSource;

    @Schema(description = "手机厂商")
    private String appBrand;

    @Schema(description = "OS版本")
    private String appOs;

    @Schema(description = "客户端设备ID")
    private String clientDeviceId;

    @Schema(description = "经纬度")
    @TableField(exist = false)
    private String location;

    public NewUserRunUploadNotify buildNewUserRunUploadNotify(Integer state) {
        var notify = new NewUserRunUploadNotify();
        notify.setUserId(userId);
        notify.setRunRecordId(recordId);
        notify.setState(state);
        return notify;
    }

    public String buildUniqueKey() {
        return startTime + "-" + endTime + "-" + thirdActivityType + "-" + userId;
    }

    public String buildUniqueKeyForGarmin() {
        return startTime + "-" + endTime + "-" + userId;
    }

}
