package com.gusto.upload.model.entity.oppo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.upload.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("upload_user_auth_info_oppo")
public class OppoUserAuthInfo extends BaseEntity {

    private static final long serialVersionUID = -8903563368879968639L;

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long recordId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "RefreshToken")
    private String refreshToken;

    @Schema(description = "AccessToken")
    private String accessToken;

    @Schema(description = "AccessToken更新时间")
    private Instant accessTokenUpdateTime;

    @Schema(description = "OPPO用户ID")
    private String openId;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickname;

}
