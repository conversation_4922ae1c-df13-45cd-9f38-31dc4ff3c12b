package com.gusto.upload.model.entity.message;

import lombok.Data;

/**
 * 荣耀-活动消息
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class HonorActivityMessage {

    public static final String SUBSCRIPTION_TOPIC = "HonorActivityMessage_Subscription_Topic_1";

    /**
     * 活动开始时间，单位毫秒
     */
    private Long startTime;

    /**
     * 活动运动类型
     */
    private String dataType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 创建唯一键
     */
    public String buildUniqueKey() {
        return startTime + "-" + dataType + "-" + userId;
    }

}
