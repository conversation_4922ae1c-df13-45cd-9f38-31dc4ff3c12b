package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 华为-采样点
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Data

public class HuaweiSamplePoint {
    @Schema(description = "数据类型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String dataTypeName;

    @Schema(description = "结束时间。单位：纳秒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long endTime;

    @Schema(description = "开始时间。单位：纳秒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long startTime;

    @Schema(description = "数据值。数据的顺序和类型需要与DataCollector中dataType定义保持一致", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<HuaweiSamplePointValue> value;

    @Schema(description = "最后修改该采样点的时间。单位：毫秒。仅在查询数据变更历史时可见")
    private Long modifyTime;

    @Schema(description = "原始数据采集器ID。仅在该SamplePoint被包含在派生dataCollector的SampleSet中时有效")
    private String originalDataCollectorId;

    @Schema(description = "扩展元，JSON格式字符串")
    private String metadata;
}
