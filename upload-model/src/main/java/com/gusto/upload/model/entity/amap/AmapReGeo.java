package com.gusto.upload.model.entity.amap;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 高德逆地理编码
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AmapReGeo {

    @Schema(description = "状态值，值为0或1，0表示失败，1表示成功")
    private Integer status;

    @Schema(description = "返回状态说明，返回状态说明，status为0时，info返回错误原因，否则返回“OK”。")
    private String info;

    @Schema(description = "状态码，返回状态说明，10000代表正确，详情参阅info状态表")
    private Integer infocode;

    @Schema(description = "逆地理编码")
    private AmapReGeoCode regeocode;

}
