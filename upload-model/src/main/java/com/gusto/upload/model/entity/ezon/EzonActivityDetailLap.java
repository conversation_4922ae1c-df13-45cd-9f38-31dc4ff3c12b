package com.gusto.upload.model.entity.ezon;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 宜准-活动详情
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Data
public class EzonActivityDetailLap {

    @Schema(description = "分段配速,单位秒")
    private Integer lapPace;

    @Schema(description = "距离上一分段点的秒数")
    private Integer lapSec;

    @Schema(description = "距离上一分段点的距离")
    private Integer lapMetre;

    @Schema(description = "分段点海拔")
    private Double lapEle;

    @Schema(description = "分段点经度")
    private Double lapLon;

    @Schema(description = "分段点纬度")
    private Double lapLat;

}
