package com.gusto.upload.model.entity.amap;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 高德天气
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AmapWeatherInfoLive {

    @Schema(description = "天气现象（汉字描述）")
    private String weather;

    @Schema(description = "实时气温，单位：摄氏度")
    private String temperature;

    @Schema(description = "空气湿度")
    private String humidity;

}
