package com.gusto.upload.model.entity.honor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class HonorRecDetail {

    @Schema(description = "运动总结实体")
    private HonorSummaryEntity summaryEntity;

    @Schema(description = "运动轨迹点列表")
    private List<HonorTracePointEntity> tracePointEntityList;

    @Schema(description = "配速实体列表")
    private List<HonorWorkoutPaceEntity> workoutPaceEntityList;

    @Schema(description = "运动详情数据列表")
    private List<HonorDetailData> detailDataList;

    @Schema(description = "分段数据实体列表")
    private List<HonorSectionDataEntity> sectionDataEntityList;

    @Schema(description = "外部实体")
    private HonorExternEntity externEntity;

}
