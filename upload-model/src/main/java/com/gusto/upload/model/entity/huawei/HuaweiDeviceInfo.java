package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 华为-设备信息
 *
 * <AUTHOR>
 * @since 2022-07-20
 */
@Data
public class HuaweiDeviceInfo {

    @Schema(description = "设备类型。" +
            "Phone：手机\n" +
            "Smart watch：智能手表\n" +
            "Smart band：智能手环\n" +
            "Smart earphone：耳机\n" +
            "Scales：体脂秤\n" +
            "Blood glucose monitor：血糖仪\n" +
            "Blood pressure monitor：血压计\n" +
            "Ecg equipment：心电设备\n" +
            "Heart rate monitor：心率设备\n" +
            "Thermometers：体温计\n" +
            "Oximeter：血氧仪\n" +
            "Treadmill：跑步机\n" +
            "Elliptical machine：椭圆机\n" +
            "Exercise bike：动感单车\n" +
            "Rowing machine：划船机\n" +
            "Walking machine：步行机\n" +
            "Rope skipping：跳绳\n" +
            "Third watch：第三手表\n" +
            "Unknown：未知设备")
    private String devType;

    @Schema(description = "终端型号名称")
    private String modelNum;

}
