package com.gusto.upload.model.entity.honor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 荣耀-鉴权凭证
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class HonorAccessTokenDetail {

    @Schema(description = "OpenId, 由用户账号和client_id计算得, 可以标识一个用户, 当入参有open_id=固定值: OPENID才会返回")
    private String openId;

    @Schema(description = "用户授权scope列表，空格分隔")
    private String scope;

    @Schema(description = "UnionId, 由用户账号和应用开发者账号计算得, 可以标识一个用户, 同一个开发者下的所有应用，UnionId均相同")
    private String unionId;

    @Schema(description = "过期时间, 单位: 秒")
    private Long expireIn;

    @Schema(description = "开发者平台申请的appid")
    private String clientId;

}
