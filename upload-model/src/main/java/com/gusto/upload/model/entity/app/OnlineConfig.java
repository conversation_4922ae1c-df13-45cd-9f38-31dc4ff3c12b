package com.gusto.upload.model.entity.app;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.upload.model.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 在线参数
 *
 * <AUTHOR>
 * @since 2021/9/2
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("online_config")
public class OnlineConfig extends BaseEntity {

    private static final long serialVersionUID = -8034654945436140269L;

    @Schema(description = "记录ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "参数名字")
    private String name;

    @Schema(description = "参数值")
    private String value;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "是否对外开放")
    private Boolean open;

}
