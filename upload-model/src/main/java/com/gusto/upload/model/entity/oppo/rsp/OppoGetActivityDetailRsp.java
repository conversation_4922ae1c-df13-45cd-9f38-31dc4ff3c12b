package com.gusto.upload.model.entity.oppo.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
public class OppoGetActivityDetailRsp {

    @Schema(description = "运动类型：1-健身类 2-其他运动类")
    private Integer dataType;

    @Schema(description = "开始时间，单位毫秒")
    private Long startTime;

    @Schema(description = "结束时间，单位毫秒")
    private Long endTime;

    @Schema(description = "运动模式")
    private Integer sportMode;

    @Schema(description = "设备类型，手表或手环")
    private String deviceType;

    @Schema(description = "设备型号")
    private String model;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "其他运动数据详情")
    private OppoGetActivityDetailOtherRsp otherSportData;

}
