package com.gusto.upload.model.entity.roozym;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/6/9
 */
@Data
@Component
@ConfigurationProperties(prefix = "upload.roozym")
public class RoozymProperties {
    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 密钥
     */
    private String clientSecret;

    /**
     * 回调地址
     */
    private String redirectUri;

    /**
     * 我们的API版本号
     */
    private String outApiVersion;

    /**
     * 订阅secret
     */
    private String subscriptionSecret;
}
