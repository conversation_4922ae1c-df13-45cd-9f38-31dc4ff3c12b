package com.gusto.upload.model.entity.coros.rsp;

import com.gusto.upload.model.entity.coros.CorosActivity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 高驰-获取指定⽇期区间的活动列表
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
public class CorosGetActivityListRsp {

    @Schema(description = "返回编码")
    private String result;

    @Schema(description = "返回信息")
    private String message;

    @Schema(description = "返回结果")
    private List<CorosActivity> data;

}
