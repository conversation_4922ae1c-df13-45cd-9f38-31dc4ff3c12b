package com.gusto.upload.model.entity.oppo.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
public class OppoGetActivityDetailOtherRsp {

    @Schema(description = "平均心率，单位count/min")
    private Integer avgHeartRate;

    @Schema(description = "平均配速，单位s/km")
    private Integer avgPace;

    @Schema(description = "平均步频，单位step/min")
    private Integer avgStepRate;

    @Schema(description = "最佳步频，单位step/min")
    private Integer bestStepRate;

    @Schema(description = "最佳配速，单位s/km")
    private Integer bestPace;

    @Schema(description = "总消耗，单位卡")
    private Integer totalCalories;

    @Schema(description = "总距离，单位米")
    private Integer totalDistance;

    @Schema(description = "总步数")
    private Integer totalSteps;

    // 不包含暂停时间
    @Schema(description = "总时长，单位毫秒")
    private Long totalTime;

    @Schema(description = "每公里耗时，单位秒，可能为空")
    private List<Integer> kmPace;

    // 大部分是一秒一个点。之前历史数据存在3秒一个点的。除了gps，其他数据类型时间戳都是一样的
    // 暂停时间段内也会继续打点，type都是1
    // 画轨迹显示颜色也是用距离和时间算的。单个距离和时间可能会有异常点，用20个点的距离和时间计算
    @Schema(description = "GPS点信息，可能为空")
    private List<OppoGetActivityDetailOtherGpsRsp> gpsPoint;

    @Schema(description = "海拔信息")
    private List<OppoGetActivityDetailOtherElevationRsp> elevation;

    @Schema(description = "心率信息")
    private List<OppoGetActivityDetailOtherHeartRateRsp> heartRate;

    // 累计距离，如果暂停距离是不变的
    @Schema(description = "距离信息")
    private List<OppoGetActivityDetailOtherDistanceRsp> distance;

    @Schema(description = "步频信息")
    private List<OppoGetActivityDetailOtherStepFrequencyRsp> frequency;

}
