package com.gusto.upload.model.entity.user.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 获取新跑步记录月份统计响应
 *
 * <AUTHOR>
 * @since 2022-08-22
 */
@Data
public class GetNewUserRunMonthStatRsp implements Serializable {

    private static final long serialVersionUID = 3670227938052731338L;

    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "月份")
    private Integer month;

    @Schema(description = "跑步跑量")
    private Double runDistance;

    @Schema(description = "其他跑量")
    private Double otherDistance;

}
