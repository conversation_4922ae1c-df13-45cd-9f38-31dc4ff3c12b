package com.gusto.upload.model.entity.wanbao;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024-09-12
 */
@Data
@Component
@ConfigurationProperties(prefix = "upload.wanbao")
public class WanbaoProperties {

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 密钥
     */
    private String clientSecret;

    /**
     * 回调地址
     */
    private String redirectUri;

}
