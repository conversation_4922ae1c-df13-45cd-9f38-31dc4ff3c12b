package com.gusto.upload.model.entity.coros;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.gusto.upload.model.entity.message.CorosActivityMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * 高驰-活动
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CorosActivity {

    @Schema(description = "高驰用户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String openId = "";

    @Schema(description = "运动ID")
    private String labelId;

    @Schema(description = "运动类型")
    private Integer mode;

    @Schema(description = "运动子类型")
    private Integer subMode;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "距离，单位：米")
    private Double distance;

    @Schema(description = "卡路里，单位：小卡")
    private Double calorie;

    @Schema(description = "平均配速，单位：秒/千米")
    private Integer avgSpeed;

    @Schema(description = "平均步频，单位：步/分钟")
    private Integer avgFrequency;

    @Schema(description = "总步数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer step = 0;

    @Schema(description = "运动开始时间戳，精确到秒")
    private Long startTime;

    @Schema(description = "运动结束时间戳，精确到秒")
    private Long endTime;

    @Schema(description = "开始时间时区，15分钟时区制，32表示东八区")
    private Integer startTimezone;

    @Schema(description = "结束时间时区，15分钟时区制，32表示东八区")
    private Integer endTimezone;

    @Schema(description = "fit文件下载地址，组合运动fit文件下载地址在每个单项运动数据中")
    private String fitUrl;

    @Schema(description = "铁人三项数据列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<CorosActivityTriathlonItem> triathlonItemList = Collections.emptyList();

    @Schema(description = "平均心率（次/分）")
    private Integer avgHeartRate;

    /**
     * 创建消息
     */
    public CorosActivityMessage buildMessage(Long userId) {
        var message = new CorosActivityMessage();
        message.setActivity(this);
        message.setUserId(userId);
        return message;
    }

}
