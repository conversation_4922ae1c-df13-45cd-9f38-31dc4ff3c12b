package com.gusto.upload.model.entity.wanbao.rsp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-09-12
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WanbaoGetActivityPageRsp {

    @Schema(description = "current")
    private Integer current;

    @Schema(description = "size")
    private Integer size;

    @Schema(description = "total")
    private Long total;

    @Schema(description = "运动数据集合")
    private List<WanbaoGetActivityRsp> records;

}
