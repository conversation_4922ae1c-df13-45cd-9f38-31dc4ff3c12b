package com.gusto.upload.model.entity.wanbao.rsp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-09-12
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WanbaoGetActivityRsp {

    @Schema(description = "运动列表主键id")
    private Long countId;

    @Schema(description = "父节点Id，该字段用于铁人三项")
    private Long parentId;

    @Schema(description = "运动类型, trail_run:越野跑, run_inside:室内跑, run_outside:室外跑, swim_outside:户外游泳, swim_inside:泳池游泳")
    private String sportType;

    @Schema(description = "运动类型中文名称，例如：越野跑、室内跑")
    private String sportTypeName;

    @Schema(description = "会员id")
    private String memberAccountId;

    @Schema(description = "设备sn码")
    private String serialNumber;

    @Schema(description = "本次记录data个数")
    private Integer dataNum;

    @Schema(description = "运动开始时间")
    private String startTime;

    @Schema(description = "运动结束时间")
    private String endTime;

    @Schema(description = "总用时，单位秒")
    private Integer totalTime;

    @Schema(description = "总步数，泳池游泳、户外游泳无此指标")
    private Integer totalSteps;

    @Schema(description = "总卡路里")
    private Double totalCalorie;

    @Schema(description = "平均配速，泳池游泳、户外游泳无此指标")
    private Double avgSpeed;

    @Schema(description = "最佳配速")
    private Double maxSpeed;

    @Schema(description = "平均步幅")
    private Double avgStride;

    @Schema(description = "本地记录计圈个数")
    private Integer totalCircleNum;

    @Schema(description = "总上升，只有越野跑适用")
    private Double totalAscend;

    @Schema(description = "总下降，只有越野跑适用")
    private Double totalDescend;

    @Schema(description = "平均划频/平均步频/平均频率")
    private Double avgFrequency;

    @Schema(description = "总划数/跳绳总次数，游泳适用")
    private Integer totalNum;

    @Schema(description = "总距离")
    private Double totalDistance;

    @Schema(description = "铁人三项-项1时间，单位秒")
    private Integer item1Time;

    @Schema(description = "铁人三项-项2时间，单位秒")
    private Integer item2Time;

    @Schema(description = "坡度")
    private Double slope;

    @Schema(description = "趟数")
    private Integer tripNum;

    @Schema(description = "泳姿")
    private Integer posture;

    @Schema(description = "有氧体力值")
    private Double aerobicPower;

    @Schema(description = "无氧体力值")
    private Double anaerobicPower;

    @Schema(description = "有氧训练效果")
    private Double aerobicEffect;

    @Schema(description = "无氧训练效果")
    private Double anaerobicEffect;

    @Schema(description = "恢复时间")
    private Integer recoveryTime;

    @Schema(description = "平均心率")
    private Double avgHeartRate;

    @Schema(description = "最大心率")
    private Double maxHeartRate;

    @Schema(description = "平均海拔")
    private Double avgAltitude;

    @Schema(description = "最高海拔")
    private Double maxAltitude;

    @Schema(description = "最佳swolf")
    private Double maxSwolf;

    @Schema(description = "平均swolf")
    private Double avgSwolf;

    @Schema(description = "最大步频/最大划水频率")
    private Double maxFrequency;

    @Schema(description = "无氧极限时间，单位秒")
    private Integer anaerobicTime;

    @Schema(description = "力量强化时间，单位秒")
    private Integer powerTime;

    @Schema(description = "心肺强化时间，单位秒")
    private Integer heartTime;

    @Schema(description = "脂肪燃烧时间，单位秒")
    private Integer fatTime;

    @Schema(description = "热身放松时间，单位秒")
    private Integer relaxTime;

    @Schema(description = "最大爬升高度")
    private Double maxClimHeight;

    @Schema(description = "平均爬上高度")
    private Double avgClimHeight;

}
