package com.gusto.upload.model.entity.garmin.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 佳明-活动详情样本
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Data
public class GarminActivityDetailSampleRsp {

    @Schema(description = "样本的开始时间（单位：秒）")
    private Long startTimeInSeconds = 0L;

    @Schema(description = "十进制纬度(DD)")
    private Double latitudeInDegree = 0.0;

    @Schema(description = "十进制经度(DD)")
    private Double longitudeInDegree = 0.0;

    @Schema(description = "海拔高度（米）")
    private Double elevationInMeters = 0.0;

    @Schema(description = "空气温度（摄氏度）")
    private Double airTemperatureCelcius = 0.0;

    @Schema(description = "心率（次/分）")
    private Integer heartrate = 0;

    @Schema(description = "速度（米/秒）")
    private Double speedMetersPerSecond = 0.0;

    @Schema(description = "步频（步/分）")
    private Double stepsPerMinute = 0.0;

    @Schema(description = "总距离（米）")
    private Double totalDistanceInMeters = 0.0;

    @Schema(description = "活动中的“计时器时间”量")
    private Long timerDurationInSeconds = 0L;

    @Schema(description = "从活动开始到结束的实际“时钟时间”量")
    private Long clockDurationInSeconds = 0L;

    @Schema(description = "用户运动的“计时器时间”量（高于临界速度的时间）")
    private Long movingDurationInSeconds = 0L;

    @Schema(description = "耗电量（单位：瓦）")
    private Double powerInWatts = 0.0;

    @Schema(description = "循环步频（单位：转/分）")
    private Double bikeCadenceInRPM = 0.0;

    @Schema(description = "游泳节奏（单位：次/分）")
    private Double swimCadenceInStrokesPerMinute = 0.0;

}
