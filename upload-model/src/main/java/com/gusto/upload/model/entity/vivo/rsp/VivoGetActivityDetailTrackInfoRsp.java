package com.gusto.upload.model.entity.vivo.rsp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VivoGetActivityDetailTrackInfoRsp implements Serializable {

    private static final long serialVersionUID = 3031466948635040449L;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "上报点的时间")
    private Long time;

    @Schema(description = "该点的运动状态（0：运动中且 gps 信号没问题，1：暂停运动，2：恢复运动，3：gps 信号丢失，4：WLJ 作弊）")
    private Integer status;

}
