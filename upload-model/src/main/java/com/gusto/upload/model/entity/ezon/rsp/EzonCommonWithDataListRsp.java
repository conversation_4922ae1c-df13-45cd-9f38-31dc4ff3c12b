package com.gusto.upload.model.entity.ezon.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 宜准-通用
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EzonCommonWithDataListRsp<T> extends EzonCommonRsp {

    @Schema(description = "data_list")
    private List<T> dataList;

}
