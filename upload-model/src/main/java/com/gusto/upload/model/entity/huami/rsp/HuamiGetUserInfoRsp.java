package com.gusto.upload.model.entity.huami.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 华米-获取用户信息
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class HuamiGetUserInfoRsp {

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "用户性别，0：女性，1：男性")
    private String gender;

    @Schema(description = "用户昵称")
    private String nickName;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "用户生日，如1980-10")
    private String birthday;

}
