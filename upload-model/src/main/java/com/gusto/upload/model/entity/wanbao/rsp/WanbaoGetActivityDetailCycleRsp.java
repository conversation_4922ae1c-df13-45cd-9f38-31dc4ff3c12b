package com.gusto.upload.model.entity.wanbao.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-09-18
 */
@Data
public class WanbaoGetActivityDetailCycleRsp implements Serializable {

    private static final long serialVersionUID = -5505363296251045880L;

    @Schema(description = "运动列表id")
    private Long countId;

    @Schema(description = "计圈详情主键id")
    private Long circledId;

    @Schema(description = "计圈序号")
    private Integer orderNum;

    @Schema(description = "单圈/本段用时")
    private Integer singleTime;

    @Schema(description = "单圈/本段距离")
    private Integer singleDistance;

    @Schema(description = "单圈/本段配速")
    private Double singleSpeed;

    @Schema(description = "本组/本段划数")
    private Integer singleStrokeNum;

    @Schema(description = "游泳效率Swolf")
    private Double singleSwolf;

    @Schema(description = "自动计圈/分段目标值")
    private Integer autoCount;

    @Schema(description = "计圈开始数据索引")
    private Integer startIndex;

    @Schema(description = "计圈结束数据索引")
    private Integer endIndex;

    @Schema(description = "本组划频/频率")
    private Double singleFrequency;

    @Schema(description = "本段泳姿")
    private Integer singlePosture;

}
