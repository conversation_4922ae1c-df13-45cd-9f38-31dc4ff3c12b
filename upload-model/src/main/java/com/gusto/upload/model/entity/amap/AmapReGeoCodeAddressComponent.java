package com.gusto.upload.model.entity.amap;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 高德逆地理编码
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AmapReGeoCodeAddressComponent {

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "行政区编码")
    private String adcode;

}
