package com.gusto.upload.model.entity.oppo;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
@Component
@ConfigurationProperties(prefix = "upload.oppo")
public class OppoProperties {

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 密钥
     */
    private String clientSecret;

    /**
     * 回调地址
     */
    private String redirectUri;

    /**
     * 加解密password
     */
    private String password;

}
