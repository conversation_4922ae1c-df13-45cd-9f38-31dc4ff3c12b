package com.gusto.upload.model.entity.huawei.request;

import com.gusto.upload.model.entity.huawei.HuaweiGroupByTime;
import com.gusto.upload.model.entity.huawei.HuaweiPolymerizeWith;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 华为-采样数据聚合分组请求
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data
@Builder

public class HuaweiGetPolymerizeSampleSetReq {
    @Schema(description = "期望被聚合的数据开始时间，未配置表示从0开始。单位：毫秒")
    private Long startTime;

    @Schema(description = "期望被聚合的数据结束时间，未配置表示从0开始。单位：毫秒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long endTime;

    @Schema(description = "聚合数据的标准。至少提供一个聚合标准。每个聚合标准产生一个汇总采样数据集，List最大容量20", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<HuaweiPolymerizeWith> polymerizeWith;

    @Schema(description = "指定数据由时间间隔聚合数据")
    private HuaweiGroupByTime groupByTime;
}
