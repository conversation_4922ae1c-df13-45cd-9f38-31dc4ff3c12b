package com.gusto.upload.model.entity.honor.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 荣耀-订阅时间通知请求
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HonorNotificationReq {

    @Schema(description = "三方应用的用户标识")
    private String openId;

    @Schema(description = "开始时间，单位毫秒")
    private Long startTime;

    @Schema(description = "运动类型")
    private String datatype;

}
