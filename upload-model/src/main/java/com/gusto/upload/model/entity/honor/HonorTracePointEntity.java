package com.gusto.upload.model.entity.honor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class HonorTracePointEntity {

    @Schema(description = "精度(米)")
    private Integer accuracy;

    @Schema(description = "海拔(米)")
    private Double altitude;

    @Schema(description = "气压海拔(米)")
    private Double barometerAltitude;

    @Schema(description = "方向角(度)")
    private Double directionAngle;

    @Schema(description = "Gps 点取值, GpsTrajV2.FLAG_NORMAL=0(正常点), GpsTrajV2.FLAG_STOP=1(暂停点)")
    private Integer flag;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "速度(米/秒)")
    private Double speed;

    @Schema(description = "时间戳(秒级戳)")
    private Long timestamp;

}
