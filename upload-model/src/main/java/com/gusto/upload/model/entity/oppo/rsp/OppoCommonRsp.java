package com.gusto.upload.model.entity.oppo.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
public class OppoCommonRsp<T> {

    @Schema(description = "状态码：0-成功 其他-失败")
    private Integer errorCode;

    @Schema(description = "状态码描述")
    private String message;

    @Schema(description = "返回数据，根据具体接口定义返回")
    private T body;

}
