package com.gusto.upload.model.entity.config;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class PaymentConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public WxCpService wxCpService() {
        WxCpDefaultConfigImpl wxCpConfigStorage = new WxCpDefaultConfigImpl();
        WxCpService wxCpService = new WxCpServiceImpl();
        wxCpService.setWxCpConfigStorage(wxCpConfigStorage);
        log.debug("企业微信SDK初始化完成");
        log.debug("wxcp config: {}", wxCpService.getWxCpConfigStorage());
        return wxCpService;
    }

}
