package com.gusto.upload.model.entity.roozym.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/5/26
 */
@Data

public class RoozymQuerySportReq {
    @Schema(description = "第三方用户标识id")
    private String openId;

    @Schema(description = "开始时间(格式：yyyy-MM-dd HH:mm:ss)")
    private String startTime;

    @Schema(description = "结束时间(格式：yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @Schema(description = "运动类型（0 徒步 1 跑步 2 骑行 3 登山 4 室内游泳 5 室外游泳 6 铁人三项 8 健走 9 徒步越野 10 室内跑 11 12 马拉松 13 团队训练 14 ai训练 999 所有运动）")
    private Integer sportType;
}
