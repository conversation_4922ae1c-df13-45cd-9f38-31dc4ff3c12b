package com.gusto.upload.model.entity.message;

import lombok.Data;

/**
 * 华为-活动消息
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
@Data
public class HuaweiActivityMessage {

    public static final String SUBSCRIPTION_TOPIC = "HuaweiActivityMessage_Subscription_Topic_1";

    /**
     * 活动开始时间
     */
    private String startTime;

    /**
     * 活动结束时间
     */
    private String endTime;

    /**
     * 活动的运动类型
     */
    private Integer activityType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 创建唯一键
     */
    public String buildUniqueKey() {
        return startTime + "-" + endTime + "-" + activityType + "-" + userId;
    }

}
