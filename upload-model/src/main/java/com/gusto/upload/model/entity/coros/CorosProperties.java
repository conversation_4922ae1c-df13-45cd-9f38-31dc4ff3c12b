package com.gusto.upload.model.entity.coros;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 高驰-配置
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@Component
@ConfigurationProperties(prefix = "upload.coros")
public class CorosProperties {

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 密钥
     */
    private String clientSecret;

    /**
     * 回调地址
     */
    private String redirectUri;

}
