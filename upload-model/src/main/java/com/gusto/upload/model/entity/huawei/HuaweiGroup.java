package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 华为-汇总数据分组
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data

public class HuaweiGroup {
    @Schema(description = "该请求中每个PolymerizeWith将有一个采样集", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<HuaweiSampleSet> sampleSet;

    @Schema(description = "聚合数据组的结束时间(inclusive)。单位:毫秒")
    private Long endTime;

    @Schema(description = "聚合数据组的开始时间(inclusive)。单位:毫秒")
    private Long startTime;
}
