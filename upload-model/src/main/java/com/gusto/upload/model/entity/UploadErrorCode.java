package com.gusto.upload.model.entity;

import com.gusto.framework.core.exception.ErrorCode;

/**
 * 错误码
 *
 * <AUTHOR>
 * @since 2022-07-05
 */
public interface UploadErrorCode {
    ErrorCode USER_NOT_EXISTS = new ErrorCode(10000, "用户不存在");
    ErrorCode OPERATION_DUPLICATE = new ErrorCode(10001, "请勿重复提交");

    ErrorCode HUAWEI_CALLBACK_ERROR = new ErrorCode(11000, "华为-回调失败");
    ErrorCode HUAWEI_CALLBACK_TOKEN_EMPTY = new ErrorCode(11001, "华为-回调用户Token为空");
    ErrorCode HUAWEI_USER_AUTH_INFO_NOT_FOUND = new ErrorCode(11002, "华为-用户授权信息不存在");
    ErrorCode HUAWEI_GET_ACCESS_TOKEN_ERROR = new ErrorCode(11003, "华为-获取用户AT失败，请尝试重新授权");
    ErrorCode HUAWEI_REFRESH_ACCESS_TOKEN_ERROR = new ErrorCode(11004, "华为-刷新用户AT失败");
    ErrorCode HUAWEI_ACCESS_TOKEN_EXPIRES = new ErrorCode(11004, "华为-用户AT过期");
    ErrorCode HUAWEI_REFRESH_TOKEN_EXPIRES = new ErrorCode(11005, "华为-用户RT过期");
    ErrorCode HUAWEI_GET_SCOPE_LIST_ERROR = new ErrorCode(11006, "华为-获取用户授权权限列表失败");
    ErrorCode HUAWEI_GET_SUBSCRIPTION_LIST_ERROR = new ErrorCode(11007, "华为-获取用户订阅记录列表失败");
    ErrorCode HUAWEI_UPSERT_SUBSCRIPTION_ERROR = new ErrorCode(11007, "华为-新增/更新用户订阅记录失败");
    ErrorCode HUAWEI_CANCEL_AUTH_ERROR = new ErrorCode(11008, "华为-取消授权失败");

    // 如骏错误码
    ErrorCode ROOZYM_CALLBACK_ERROR = new ErrorCode(12000, "如骏-回调失败");
    ErrorCode ROOZYM_USER_AUTH_INFO_NOT_FOUND = new ErrorCode(11002, "如骏-用户授权信息不存在");
    ErrorCode ROOZYM_GET_ACCESS_TOKEN_ERROR = new ErrorCode(12003, "如骏-获取用户AT失败");
    ErrorCode ROOZYM_UPSERT_SUBSCRIPTION_ERROR = new ErrorCode(12007, "如骏-新增/更新用户订阅记录失败");
    ErrorCode ROOZYM_REFRESH_TOKEN_EXPIRES = new ErrorCode(12005, "如骏-用户RT过期");
    ErrorCode ROOZYM_REFRESH_ACCESS_TOKEN_ERROR = new ErrorCode(12004, "如骏-刷新用户AT失败");

    ErrorCode GARMIN_USER_AUTH_INFO_NOT_FOUND = new ErrorCode(12000, "佳明-用户授权信息不存在");
    ErrorCode GARMIN_GET_AUTH_URL_ERROR = new ErrorCode(12001, "佳明-获取授权链接失败");
    ErrorCode GARMIN_GET_ACCESS_TOKEN_ERROR = new ErrorCode(12001, "佳明-获取用户AT失败");

    // 高驰
    ErrorCode COROS_USER_AUTH_INFO_NOT_FOUND = new ErrorCode(13000, "高驰-用户授权信息不存在");
    ErrorCode COROS_REFRESH_ACCESS_TOKEN_ERROR = new ErrorCode(13001, "高驰-刷新用户AT失败");

    // 宜准
    ErrorCode EZON_USER_AUTH_INFO_NOT_FOUND = new ErrorCode(14000, "高驰-用户授权信息不存在");
    ErrorCode EZON_REFRESH_ACCESS_TOKEN_ERROR = new ErrorCode(14001, "高驰-刷新用户AT失败");

    // OPPO
    ErrorCode OPPO_USER_AUTH_INFO_NOT_FOUND = new ErrorCode(15000, "OPPO-用户授权信息不存在");
    ErrorCode OPPO_REFRESH_ACCESS_TOKEN_ERROR = new ErrorCode(15001, "OPPO-刷新用户AT失败");

    // VIVO
    ErrorCode VIVO_USER_AUTH_INFO_NOT_FOUND = new ErrorCode(16000, "VIVO-用户授权信息不存在");
    ErrorCode VIVO_REFRESH_ACCESS_TOKEN_ERROR = new ErrorCode(16001, "VIVO-刷新用户AT失败");

    // 七牛
    ErrorCode QINIU_UPLOAD_ERROR = new ErrorCode(17000, "七牛-上传错误");

    // 腕宝
    ErrorCode WANBAO_USER_AUTH_INFO_NOT_FOUND = new ErrorCode(18000, "腕宝-用户授权信息不存在");
    ErrorCode WANBAO_REFRESH_ACCESS_TOKEN_ERROR = new ErrorCode(18001, "腕宝-刷新用户AT失败");
    ErrorCode WANBAO_PUSH_ERROR = new ErrorCode(18002, "推送失败");

    // 华米
    ErrorCode HUAMI_USER_AUTH_INFO_NOT_FOUND = new ErrorCode(19000, "华米-用户授权信息不存在");
    ErrorCode HUAMI_REFRESH_ACCESS_TOKEN_ERROR = new ErrorCode(19001, "华米-刷新用户AT失败");
    ErrorCode HUAMI_GET_ACCESS_TOKEN_ERROR = new ErrorCode(19002, "华米-获取用户AT失败");
    ErrorCode HUAMI_CANCEL_AUTH_ERROR = new ErrorCode(19003, "华米-取消授权失败");

    // 荣耀
    ErrorCode HONOR_CALLBACK_ERROR = new ErrorCode(11000, "荣耀-回调失败");
    ErrorCode HONOR_CALLBACK_TOKEN_EMPTY = new ErrorCode(11001, "荣耀-回调用户Token为空");
    ErrorCode HONOR_USER_AUTH_INFO_NOT_FOUND = new ErrorCode(11002, "荣耀-用户授权信息不存在");
    ErrorCode HONOR_AUTH_ERROR = new ErrorCode(11003, "荣耀-获取用户AT失败，请尝试重新授权");
    ErrorCode HONOR_REFRESH_ACCESS_TOKEN_ERROR = new ErrorCode(11004, "荣耀-刷新用户AT失败");
    ErrorCode HONOR_ACCESS_TOKEN_EXPIRES = new ErrorCode(11004, "荣耀-用户AT过期");
    ErrorCode HONOR_REFRESH_TOKEN_EXPIRES = new ErrorCode(11005, "荣耀-用户RT过期");
    ErrorCode HONOR_GET_SCOPE_LIST_ERROR = new ErrorCode(11006, "荣耀-获取用户授权权限列表失败");
    ErrorCode HONOR_GET_SUBSCRIPTION_LIST_ERROR = new ErrorCode(11007, "荣耀-获取用户订阅记录列表失败");
    ErrorCode HONOR_UPSERT_SUBSCRIPTION_ERROR = new ErrorCode(11007, "荣耀-新增/更新用户订阅记录失败");
    ErrorCode HONOR_CANCEL_AUTH_ERROR = new ErrorCode(11008, "荣耀-取消授权失败");

}
