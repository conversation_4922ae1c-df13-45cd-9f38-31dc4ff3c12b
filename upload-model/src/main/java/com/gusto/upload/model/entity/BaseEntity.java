package com.gusto.upload.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 基类
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Data
public class BaseEntity implements Serializable {

    public static final String CREATE_TIME_FIELD = "create_time";
    public static final String UPDATE_TIME_FIELD = "update_time";
    private static final long serialVersionUID = -1004434549424708980L;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Instant createTime;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Instant updateTime;
}
