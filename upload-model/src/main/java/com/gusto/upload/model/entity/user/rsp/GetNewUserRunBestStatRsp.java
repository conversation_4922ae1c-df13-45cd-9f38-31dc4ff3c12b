package com.gusto.upload.model.entity.user.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 获取新跑步记录最佳统计响应
 *
 * <AUTHOR>
 * @since 2022-08-22
 */
@Data
public class GetNewUserRunBestStatRsp implements Serializable {

    private static final long serialVersionUID = -4364571267218467501L;

    @Schema(description = "类型：" +
            "0-默认 " +
            "1-最远距离(公里) " +
            "2-最长时间 " +
            "3-最快配速 " +
            "4-5公里最快时间 " +
            "5-10公里最快时间 " +
            "6-最快半马时间 " +
            "7-最快全马时间")
    private Integer type;

    @Schema(description = "成绩")
    private String result;

    @Schema(description = "记录ID")
    private Long recordId;

}
