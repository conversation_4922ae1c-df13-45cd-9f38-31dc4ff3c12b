package com.gusto.upload.model.entity.garmin;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/6/13
 */
@Data
@Component
@ConfigurationProperties(prefix = "upload.garmin")
public class GarminProperties {

    /**
     * 客户端ID
     */
    private String consumerKey;

    /**
     * 密钥
     */
    private String consumerSecret;

    /**
     * 回调地址
     */
    private String redirectUri;

    /**
     * 海外客户端ID
     */
    private String globalConsumerKey;

    /**
     * 海外密钥
     */
    private String globalConsumerSecret;

    /**
     * 海外回调地址
     */
    private String globalRedirectUri;

}
