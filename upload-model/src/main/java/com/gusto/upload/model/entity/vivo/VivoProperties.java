package com.gusto.upload.model.entity.vivo;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024-04-15
 */
@Data
@Component
@ConfigurationProperties(prefix = "upload.vivo")
public class VivoProperties {

    /**
     * APP ID
     */
    private String appId;

    /**
     * APP 密钥
     */
    private String appSecret;

    /**
     * 回调地址
     */
    private String redirectUri;

    /**
     * 加解密password
     */
    private String password;

}
