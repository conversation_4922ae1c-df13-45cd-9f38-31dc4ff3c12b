package com.gusto.upload.model.entity.user.old;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;

/**
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@TableName(value = "app_run_county_log")
public class UserRunAdcode {

    @Schema(description = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "uid")
    @TableField("uid")
    private Long uid;

    @Schema(description = "runid")
    @TableField("runid")
    private Long runId;

    @Schema(description = "adcode")
    @TableField("adcode")
    private Integer adcode;

    @Schema(description = "createTime")
    @TableField("create_time")
    private Instant createTime;

    @Schema(description = "latitude")
    @TableField("x")
    private Double latitude;

    @Schema(description = "longitude")
    @TableField("y")
    private Double longitude;

}
