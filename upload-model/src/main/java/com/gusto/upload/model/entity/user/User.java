package com.gusto.upload.model.entity.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户
 * PS: upload-server只涉及查询
 *
 * <AUTHOR>
 * @since 2021-11-04
 */
@Data
@TableName("member")
public class User implements Serializable {
    public static final String USER_ID_FIELD = "uid";
    public static final String APP_TOKEN_FIELD = "apptoken";
    private static final long serialVersionUID = 4065266941199028243L;

    @Schema(description = "用户ID")
    @TableId(value = "uid", type = IdType.AUTO)
    private Long userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "微信openId")
    @TableField("openid")
    private String openId;

    @Schema(description = "APP登录token")
    @TableField("apptoken")
    private String appToken;

    @Schema(description = "头像")
    @TableField("avatar")
    private String avatar;

    @Schema(description = "手机")
    @TableField("mobile")
    private String mobile;
}
