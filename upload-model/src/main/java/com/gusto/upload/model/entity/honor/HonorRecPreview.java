package com.gusto.upload.model.entity.honor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class HonorRecPreview {

    @Schema(description = "开始时间，单位秒")
    private Long startTime;

    @Schema(description = "结束时间，单位秒")
    private Long endTime;

    @Schema(description = "卡路里(千卡)")
    private Double consumeKcal;

    @Schema(description = "距离(米)")
    private Integer distance;

    @Schema(description = "有效活动时长(秒)")
    private Integer sportTime;

    @Schema(description = "平均速度(分米/秒)")
    private Integer speed;

    @Schema(description = "步数")
    private Integer step;

    @Schema(description = "平均心率")
    private Integer avgHeartRate;

}
