package com.gusto.upload.model.entity.coros;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 高驰-铁三活动
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
public class CorosActivityTriathlonItem {

    @Schema(description = "铁人三项单项运动类型")
    private Integer mode;

    @Schema(description = "铁人三项单项运动子类型")
    private Integer subMode;

    @Schema(description = "铁人三项单项运动距离，单位：米")
    private Double distance;

    @Schema(description = "铁人三项单项卡路里，单位：小卡")
    private Double calorie;

    @Schema(description = "铁人三项单项运动时长，单位：秒")
    private Integer duration;

    @Schema(description = "铁人三项单项步数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer step = 0;

    @Schema(description = "铁人三项单项运动fit文件下载地址，组合运动只有每个单项运动才有fit⽂件下载地址")
    private String fitUrl;

}
