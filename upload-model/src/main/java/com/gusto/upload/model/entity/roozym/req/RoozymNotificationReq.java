package com.gusto.upload.model.entity.roozym.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/6/1
 */
@Data

public class RoozymNotificationReq {
    @Schema(description = "第三方用户标识id")
    private String openId;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "运动类型")
    private Integer sportType;

    @Schema(description = "事件类型")
    private String eventType;
}
