package com.gusto.upload.model.entity.user.old;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gusto.upload.model.entity.user.NewUserRunUploadNotify;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * 用户跑步记录
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data
@TableName("app_run")
public class UserRun implements Serializable {
    public static final String USER_ID_FIELD = "uid";
    public static final String RUN_ID_FIELD = "runid";
    public static final String STATE_FIELD = "state";
    public static final String START_TIME_FIELD = "starttime";
    public static final String END_TIME_FIELD = "endtime";
    private static final long serialVersionUID = -233739075768921014L;

    @Schema(description = "记录ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID")
    @TableField("uid")
    private Long userId;

    @Schema(description = "年")
    @TableField("year")
    private Integer year;

    @Schema(description = "月")
    @TableField("month")
    private Integer month;

    @Schema(description = "日")
    @TableField("day")
    private Integer day;

    @Schema(description = "识别ID")
    @TableField("runid")
    private Long runId;

    @Schema(description = "总时长，单位秒")
    @TableField("usetime")
    private Long duration;

    @Schema(description = "总距离，单位米")
    @TableField("distance")
    private Float distance;

    @Schema(description = "总步数")
    @TableField("stepcount")
    private Integer stepCount;

    @Schema(description = "总消耗")
    @TableField("calorie")
    private Float calorie;

    @Schema(description = "平均心率")
    @TableField("averageheartrate")
    private Integer averageHeartRate;

    @Schema(description = "平均配速，单位秒")
    @TableField("averagepace")
    private Float averagePace;

    @Schema(description = "平均步幅")
    @TableField("stepstride")
    private Float stepStride;

    @Schema(description = "平均步频")
    @TableField("stepfrequency")
    private Integer stepFrequency;

    @Schema(description = "开始时间，单位毫秒")
    @TableField("starttime")
    private Long startTime;

    @Schema(description = "结束时间，单位毫秒")
    @TableField("endtime")
    private Long endTime;

    @Schema(description = "第三方活动类型")
    @TableField("eventid")
    private String event;

    @Schema(description = "第三方活动记录ID")
    @TableField("event")
    private String eventId;

    @Schema(description = "设备名称，暂默认为空字符串")
    @TableField("devicename")
    private String deviceName;

    @Schema(description = "异常信息，目前只有重叠记录")
    @TableField("unusualmsg")
    private String unusualMsg;

    @Schema(description = "是否异常：0-正常 1-异常")
    @TableField("isunusual")
    private Integer unusual;

    @Schema(description = "0正常 1后台修改 2跑步机 3补录 6佳明手表 7华为手表 8宜准 9高驰 10如骏")
    @TableField("abnormal")
    private Integer abnormal;

    @Schema(description = "0正常 1补录")
    @TableField("type")
    private Integer type;

    @Schema(description = "状态 -2外部非跑步类型 -1删除 1记录")
    @TableField("state")
    private Integer state;

    @Schema(description = "状态 -2外部非跑步类型 -1删除 1记录")
    @TableField("uploadtime")
    private Instant uploadTime;

    @Schema(description = "更新时间")
    @TableField("modifytime")
    private Instant modifyTime;

    @Schema(description = "运动类型：1-跑步 2-其它运动")
    @TableField("category")
    private Integer category;

    @Schema(description = "数据来源：1-app跑步 2-手动录入 3-手表 4-手环 5-跑步机")
    @TableField("source")
    private Integer source;

    @Schema(description = "数据来源子类：201-app录入 202-小程序 203-后台 301-佳明 302-宜准 303-高驰 304-华为 306-如骏")
    @TableField("sub_source")
    private Integer subSource;

    @Schema(description = "外部导入类型")
    @TableField("external_sports_type")
    private String externalSportType;

    @Schema(description = "运动类型：1-户外跑 2-室内跑 3-徒步 4-越野 5-骑车 6-游泳 100-其它")
    @TableField("sports_type")
    private Integer sportType;

    @Schema(description = "是否重复")
    @TableField("isduplicate")
    private Boolean duplicate;

    @Schema(description = "重复记录分组ID")
    @TableField("duplicate_groupid")
    private Long duplicateGroupId;

    @Schema(description = "是否后台修改")
    @TableField("admin_modify")
    private Boolean adminUpdate;

    @Schema(description = "修改时间")
    @TableField("admin_modify_time")
    private Instant adminUpdateTime;

    @Schema(description = "轨迹文件名称")
    @TableField("trackfile")
    private String trackFile;

    @Schema(description = "海拔累计上升，单位米")
    @TableField("ascent")
    private Float ascent;

    @Schema(description = "海拔累计下降，单位米")
    @TableField("descent")
    private Float descent;

    @Schema(description = "周")
    @TableField("week")
    private Integer week;

    @Schema(description = "天气")
    @TableField("weather")
    private String weather;

    @Schema(description = "省")
    @TableField("province")
    private String province;

    @Schema(description = "市")
    @TableField("city")
    private String city;

    @Schema(description = "实时气温，单位：摄氏度")
    @TableField("temperature")
    private String temperature;

    @Schema(description = "空气湿度")
    @TableField("humidity")
    private String humidity;

    public NewUserRunUploadNotify buildNewUserRunUploadNotify(Integer state) {
        var notify = new NewUserRunUploadNotify();
        notify.setUserId(userId);
        notify.setRunRecordId(id);
        notify.setState(state);
        return notify;
    }

}
