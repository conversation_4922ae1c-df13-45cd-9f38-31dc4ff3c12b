package com.gusto.upload.model.entity.message;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-09-13
 */
@Data
public class WanbaoActivityMessage {

    public static final String NOTIFY_ACTIVITY_TOPIC = "WanbaoActivityMessage_NotifyActivity_Topic_1";
    public static final String GROUP = "WanbaoActivityMessage_Group";

    /**
     * 用户唯一标识，授权的时候返回
     */
    private String openId;

    /**
     * 运动列表主键id
     */
    private Long countId;

    /**
     * 运动类型, trail_run:越野跑, run_inside:室内跑, run_outside:室外跑, swim_outside:户外游泳, swim_inside:泳池游泳
     */
    private String sportType;

    /**
     * 运动数据文件地址(JSON格式文件)
     */
    private String fileUrl;

}
