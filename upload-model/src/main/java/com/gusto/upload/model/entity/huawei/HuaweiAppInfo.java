package com.gusto.upload.model.entity.huawei;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 华为-应用信息
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Data
public class HuaweiAppInfo {
    @Schema(description = "应用的名称，appName和appPackageName至少存在一个", requiredMode = Schema.RequiredMode.REQUIRED)
    private String appName;

    @Schema(description = "应用程序的包名称，如果appPackageName/clientId同时存在，appPackageName/clientId必须匹配", requiredMode = Schema.RequiredMode.REQUIRED)
    private String appPackageName;

    @Schema(description = "应用的版本信息")
    private String appVersion;

    @Schema(description = "应用ID")
    private String clientId;

    @Schema(description = "用于链接该应用程序的URL")
    private String desc;
}
