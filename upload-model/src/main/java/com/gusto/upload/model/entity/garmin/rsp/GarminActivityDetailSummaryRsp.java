package com.gusto.upload.model.entity.garmin.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 佳明-活动详情统计
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Data
public class GarminActivityDetailSummaryRsp {

    @Schema(description = "活动开始时间（单位：秒）")
    private Long startTimeInSeconds;

    @Schema(description = "添加到startTimeInSeconds的偏移量，以获取捕获数据的设备的本地时间（单位：秒）")
    private Long startTimeOffsetInSeconds;

    @Schema(description = "活动类型")
    private String activityType = "RUNNING";

    @Schema(description = "监测期的时间长度（单位：秒）")
    private Integer durationInSeconds = 0;

    @Schema(description = "平均踏频（次/分）")
    private Double averageBikeCadenceInRoundsPerMinute = 0.0;

    @Schema(description = "平均心率（次/分）")
    private Integer averageHeartRateInBeatsPerMinute = 0;

    @Schema(description = "平均步频（步/分）。")
    private Double averageRunCadenceInStepsPerMinute = 0.0;

    @Schema(description = "平均速度（米/秒）")
    private Double averageSpeedInMetersPerSecond = 0.0;

    @Schema(description = "平均划水频率（次/分）")
    private Double averageSwimCadenceInStrokesPerMinute = 0.0;

    @Schema(description = "平均配速（分/公里）")
    private Double averagePaceInMinutesPerKilometer = 0.0;

    @Schema(description = "实际活动消耗的热量（千卡）")
    private Integer activeKilocalories = 0;

    @Schema(description = "设备名称")
    private String deviceName = "";

    @Schema(description = "距离（米）")
    private Double distanceInMeters = 0.0;

    @Schema(description = "最大踏频（次/分）")
    private Double maxBikeCadenceInRoundsPerMinute = 0.0;

    @Schema(description = "最大心率")
    private Double maxHeartRateInBeatsPerMinute = 0.0;

    @Schema(description = "最大配速（分/公里）")
    private Double maxPaceInMinutesPerKilometer = 0.0;

    @Schema(description = "最大步频（步/分）")
    private Double maxRunCadenceInStepsPerMinute = 0.0;

    @Schema(description = "最大速度（米/秒）")
    private Double maxSpeedInMetersPerSecond = 0.0;

    @Schema(description = "泳道游泳游完泳道长度的次数，仅用于游泳")
    private Integer numberOfActiveLengths = 0;

    @Schema(description = "活动开始纬度")
    private Double startingLatitudeInDegree = 0.0;

    @Schema(description = "活动开始经度")
    private Double startingLongitudeInDegree = 0.0;

    @Schema(description = "步数（步）")
    private Integer steps = 0;

    @Schema(description = "海拔总上升（米）")
    private Double totalElevationGainInMeters = 0.0;

    @Schema(description = "海拔总下降（米）")
    private Double totalElevationLossInMeters = 0.0;

    @Schema(description = "如果存在并设置为true，则该活动是应在数据摘要中提供给合作伙伴一个或多个子活动的父活动")
    private Boolean isParent = false;

    @Schema(description = "如果存在，则这是其相关父活动的摘要ID。如一个MULTI_SPORT活动包含的CYCLING活动")
    private String parentSummaryId = "";

    @Schema(description = "表示该活动是直接在Connect网站上手动更新的活动。该属性仅适用于手动活动")
    private Boolean manual = false;

}
