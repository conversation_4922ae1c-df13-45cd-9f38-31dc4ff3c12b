package com.gusto.upload.model.entity.coros.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 高驰-使用授权码Code获取AT
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
public class CorosGetAccessTokenRsp {

    @Schema(description = "AccessToken超时时间，单位：秒，默认30天")
    private Long expiresIn;

    @Schema(description = "RefreshToken")
    private String refreshToken;

    @Schema(description = "AccessToken")
    private String accessToken;

    @Schema(description = "高驰用户ID")
    private String openId;

}
